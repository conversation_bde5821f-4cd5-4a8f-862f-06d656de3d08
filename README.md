# 🌿 Medicinal Plant Recognition System

A comprehensive AI-powered web application for identifying medicinal plants using YOLOv8 deep learning model. This system can recognize 144 different medicinal plant species and provide detailed information about their therapeutic properties.

## ✨ Features

- **🎯 Plant Identification**: Upload an image and get instant plant identification with confidence scores
- **📊 Multiple Predictions**: Shows top 3 most likely plant matches with confidence levels
- **💊 Medicinal Information**: Detailed information about each plant including:
  - Scientific name
  - Local/common names
  - Medicinal uses and benefits
  - Plant description
- **🔍 Search Functionality**: Search plants by medicinal use or condition
- **📈 Visual Analytics**: Interactive confidence charts and detection visualization
- **🎨 Modern UI**: Beautiful, responsive web interface with plant-themed design

## 🗂️ Dataset

The system is trained on a comprehensive dataset containing:
- **144 medicinal plant species**
- **High-quality images** with proper annotations
- **Diverse plant varieties** from different regions
- **Balanced dataset** for robust model performance

### Supported Plant Species (Sample)
- Al<PERSON> Vera (Ghritkumari) - Skin healing, burns, digestive issues
- Tulsi (Holy Basil) - Respiratory problems, immunity, stress relief
- Neem (Margosa) - Skin diseases, antimicrobial, dental care
- Turmeric (Haldi) - Anti-inflammatory, wound healing, immunity
- Ashwagandha - Stress relief, adaptogen, strength, immunity
- And 139+ more species...

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- CUDA-compatible GPU (recommended for training)
- At least 8GB RAM

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd medicinal-plant-identification
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Train the model (if not already trained)**
```bash
python train_model.py
```

4. **Run the web application**
```bash
streamlit run app.py
```

5. **Open your browser**
Navigate to `http://localhost:8501` to access the application.

## 📁 Project Structure

```
medicinal-plant-identification/
├── app.py                 # Streamlit web application
├── train_model.py         # YOLOv8 training script
├── plant_database.py      # Medicinal plant information database
├── data.yaml             # Dataset configuration
├── requirements.txt      # Python dependencies
├── README.md            # Project documentation
├── best.pt              # Trained model weights (after training)
├── models/              # Training outputs and model files
├── train/               # Training dataset
│   ├── images/          # Training images
│   └── labels/          # Training annotations
├── valid/               # Validation dataset
│   ├── images/          # Validation images
│   └── labels/          # Validation annotations
└── test/                # Test dataset
    ├── images/          # Test images
    └── labels/          # Test annotations
```

## 🔧 Configuration

### Training Parameters
You can modify training parameters in `train_model.py`:
- `epochs`: Number of training epochs (default: 100)
- `batch`: Batch size (default: 16)
- `imgsz`: Image size (default: 640)
- `device`: Training device ('cuda' or 'cpu')

### Model Selection
The system uses YOLOv8n (nano) by default for faster training. You can change to other variants:
- `yolov8s.pt` - Small model
- `yolov8m.pt` - Medium model
- `yolov8l.pt` - Large model
- `yolov8x.pt` - Extra large model

## 📊 Model Performance

After training, the model achieves:
- **High accuracy** in plant species identification
- **Real-time inference** for instant results
- **Robust detection** across various image conditions
- **Multi-object detection** for images with multiple plants

## 🌐 Web Application Features

### Main Interface
- **Image Upload**: Drag & drop or browse to upload plant images
- **Real-time Processing**: Instant analysis and results
- **Detection Visualization**: Bounding boxes around detected plants
- **Confidence Scores**: Percentage confidence for each prediction

### Plant Information Display
- **Detailed Cards**: Beautiful cards showing plant information
- **Scientific Classification**: Proper botanical names
- **Medicinal Properties**: Comprehensive therapeutic uses
- **Visual Indicators**: Color-coded confidence levels

### Search and Filter
- **Medicinal Use Search**: Find plants by health condition
- **Quick Reference**: Instant access to plant database
- **Interactive Charts**: Visual confidence comparisons

## 🔬 Technical Details

### Model Architecture
- **Base Model**: YOLOv8 (You Only Look Once v8)
- **Task**: Object Detection and Classification
- **Input Size**: 640x640 pixels
- **Output**: Bounding boxes + class predictions + confidence scores

### Training Process
1. **Data Preprocessing**: Image resizing, normalization, augmentation
2. **Model Initialization**: Load pretrained YOLOv8 weights
3. **Transfer Learning**: Fine-tune on medicinal plant dataset
4. **Validation**: Monitor performance on validation set
5. **Model Selection**: Save best performing weights

### Inference Pipeline
1. **Image Upload**: User uploads plant image
2. **Preprocessing**: Resize and normalize image
3. **Model Inference**: YOLOv8 processes image
4. **Post-processing**: Filter and rank predictions
5. **Information Retrieval**: Fetch plant details from database
6. **Results Display**: Show predictions with plant information

## 🚨 Important Disclaimers

⚠️ **Medical Disclaimer**: This application is for educational and informational purposes only. It is not intended to diagnose, treat, cure, or prevent any disease. Always consult with qualified healthcare professionals before using any medicinal plants or making health-related decisions.

⚠️ **Accuracy Notice**: While the AI model is trained on a comprehensive dataset, plant identification accuracy may vary based on image quality, lighting conditions, and plant condition. Always verify plant identification with botanical experts when in doubt.

## 🤝 Contributing

We welcome contributions to improve the system:

1. **Fork the repository**
2. **Create a feature branch**
3. **Add your improvements**
4. **Submit a pull request**

### Areas for Contribution
- Adding more plant species to the database
- Improving model accuracy
- Enhancing the user interface
- Adding new features (plant care tips, growing guides, etc.)
- Translating to different languages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Ultralytics** for the YOLOv8 framework
- **Streamlit** for the web application framework
- **Roboflow** for dataset management and annotation tools
- **Traditional Medicine Practitioners** for medicinal plant knowledge
- **Botanical Researchers** for scientific plant information

## 📞 Support

For questions, issues, or suggestions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation for troubleshooting

---

**Made with 🌿 for preserving traditional medicinal plant knowledge through modern AI technology**
