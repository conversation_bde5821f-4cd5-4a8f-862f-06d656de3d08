"""
Comprehensive Dataset Generator for Medicinal Plant Recognition
Combines AI generation, advanced augmentation, and dataset management
"""

import os
import sys
import yaml
import cv2
import numpy as np
from pathlib import Path
import logging
import json
import random
import argparse
from datetime import datetime
from typing import List, Dict, Tuple
import concurrent.futures
from tqdm import tqdm

# Import our custom modules
from ai_dataset_generator import AIDatasetGenerator
from advanced_augmentation import AdvancedAugmentation

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveDatasetGenerator:
    """Comprehensive dataset generator combining all techniques"""
    
    def __init__(self, data_yaml_path: str = "data.yaml"):
        self.data_yaml_path = data_yaml_path
        self.load_config()
        
        # Initialize components
        self.ai_generator = AIDatasetGenerator(data_yaml_path)
        self.advanced_aug = AdvancedAugmentation()
        
        # Statistics
        self.stats = {
            "start_time": datetime.now().isoformat(),
            "original_images": 0,
            "ai_generated": 0,
            "basic_augmented": 0,
            "advanced_augmented": 0,
            "total_generated": 0,
            "classes_processed": 0,
            "processing_time": 0
        }
        
        # Setup output directories
        self.setup_output_directories()
    
    def load_config(self):
        """Load dataset configuration"""
        with open(self.data_yaml_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.num_classes = self.config['nc']
        self.class_names = self.config['names']
        
        logger.info(f"Loaded {self.num_classes} classes from {self.data_yaml_path}")
    
    def setup_output_directories(self):
        """Setup output directory structure"""
        self.output_base = Path("comprehensive_dataset")
        
        # Create directory structure
        directories = [
            "ai_generated",
            "basic_augmented", 
            "advanced_augmented",
            "final_dataset/train/images",
            "final_dataset/train/labels",
            "final_dataset/valid/images",
            "final_dataset/valid/labels",
            "final_dataset/test/images",
            "final_dataset/test/labels"
        ]
        
        for directory in directories:
            (self.output_base / directory).mkdir(parents=True, exist_ok=True)
        
        # Create class subdirectories
        for class_name in self.class_names.values():
            if class_name == '-':
                continue
            
            for subdir in ["ai_generated", "basic_augmented", "advanced_augmented"]:
                (self.output_base / subdir / class_name).mkdir(parents=True, exist_ok=True)
    
    def count_existing_images(self) -> Dict[str, int]:
        """Count existing images per class"""
        counts = {}
        train_dir = Path("train/images")
        
        for class_name in self.class_names.values():
            if class_name == '-':
                continue
            
            count = 0
            for img_file in train_dir.glob("*.jpg"):
                if class_name.lower() in img_file.name.lower():
                    count += 1
            
            counts[class_name] = count
            self.stats["original_images"] += count
        
        return counts
    
    def generate_ai_images_for_class(self, class_name: str, target_count: int = 20) -> List[str]:
        """Generate AI images for a specific class"""
        if class_name == '-':
            return []
        
        logger.info(f"🤖 Generating {target_count} AI images for {class_name}")
        
        output_dir = self.output_base / "ai_generated" / class_name
        generated_files = []
        
        # Use the AI generator's local method (synthetic generation)
        for i in range(target_count):
            try:
                # Generate synthetic plant image
                synthetic_img = self.ai_generator.create_synthetic_plant_image(class_name)
                
                # Convert PIL to numpy array
                img_array = np.array(synthetic_img)
                
                # Apply some randomization to make it more realistic
                img_array = self.randomize_synthetic_image(img_array, class_name)
                
                # Save image
                filename = f"{class_name}_ai_{i+1:03d}.jpg"
                filepath = output_dir / filename
                
                cv2.imwrite(str(filepath), cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR))
                generated_files.append(str(filepath))
                
                self.stats["ai_generated"] += 1
                
            except Exception as e:
                logger.warning(f"Failed to generate AI image {i+1} for {class_name}: {e}")
                continue
        
        logger.info(f"✅ Generated {len(generated_files)} AI images for {class_name}")
        return generated_files
    
    def randomize_synthetic_image(self, img_array: np.ndarray, class_name: str) -> np.ndarray:
        """Add randomization to synthetic images to make them more realistic"""
        
        # Add color variations based on plant type
        color_variations = {
            'neem': [(34, 139, 34), (50, 205, 50), (0, 128, 0)],
            'tulsi': [(107, 142, 35), (85, 107, 47), (128, 128, 0)],
            'aloe': [(0, 255, 127), (46, 139, 87), (60, 179, 113)],
            'default': [(34, 139, 34), (50, 205, 50), (0, 128, 0), (107, 142, 35)]
        }
        
        # Get color palette for this plant
        plant_key = class_name.lower()
        colors = color_variations.get(plant_key, color_variations['default'])
        
        # Apply color tinting
        tint_color = random.choice(colors)
        tint_strength = random.uniform(0.1, 0.3)
        
        for i in range(3):
            img_array[:, :, i] = img_array[:, :, i] * (1 - tint_strength) + tint_color[i] * tint_strength
        
        # Add texture
        texture_noise = np.random.normal(0, 5, img_array.shape)
        img_array = img_array + texture_noise
        
        # Add lighting variation
        h, w = img_array.shape[:2]
        center_x, center_y = w // 2, h // 2
        y, x = np.ogrid[:h, :w]
        
        # Create radial gradient
        distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        max_distance = np.sqrt(center_x**2 + center_y**2)
        gradient = 1 - (distance / max_distance) * random.uniform(0.1, 0.3)
        
        img_array = img_array * gradient[:, :, np.newaxis]
        
        return np.clip(img_array, 0, 255).astype(np.uint8)
    
    def apply_basic_augmentations(self, image_path: str, class_name: str, count: int = 10) -> List[str]:
        """Apply basic augmentations to an image"""
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                return []
            
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            output_dir = self.output_base / "basic_augmented" / class_name
            augmented_files = []
            
            base_name = Path(image_path).stem
            
            for i in range(count):
                try:
                    # Apply basic augmentation
                    augmented = self.ai_generator.apply_comprehensive_augmentations(image_path, class_name, 1)
                    
                    if augmented:
                        # Copy to our output directory
                        src_file = Path(augmented[0])
                        dst_file = output_dir / f"{base_name}_basic_aug_{i+1:02d}.jpg"
                        
                        if src_file.exists():
                            dst_file.write_bytes(src_file.read_bytes())
                            augmented_files.append(str(dst_file))
                            self.stats["basic_augmented"] += 1
                
                except Exception as e:
                    logger.warning(f"Basic augmentation {i+1} failed: {e}")
                    continue
            
            return augmented_files
            
        except Exception as e:
            logger.error(f"Failed to apply basic augmentations to {image_path}: {e}")
            return []
    
    def apply_advanced_augmentations(self, image_path: str, class_name: str, count: int = 15) -> List[str]:
        """Apply advanced augmentations to an image"""
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                return []
            
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            output_dir = self.output_base / "advanced_augmented" / class_name
            augmented_files = []
            
            base_name = Path(image_path).stem
            
            for i in range(count):
                try:
                    # Choose augmentation type
                    aug_type = random.choice(['albumentations', 'color_space', 'geometric', 'texture'])
                    
                    if aug_type == 'albumentations':
                        augmented = self.advanced_aug.apply_albumentations(img_rgb)
                    elif aug_type == 'color_space':
                        augmented = self.advanced_aug.apply_color_space_augmentation(img_rgb)
                    elif aug_type == 'geometric':
                        augmented = self.advanced_aug.apply_geometric_augmentation(img_rgb)
                    else:  # texture
                        augmented = self.advanced_aug.apply_texture_augmentation(img_rgb)
                    
                    # Save augmented image
                    filename = f"{base_name}_adv_aug_{i+1:02d}.jpg"
                    filepath = output_dir / filename
                    
                    cv2.imwrite(str(filepath), cv2.cvtColor(augmented, cv2.COLOR_RGB2BGR))
                    augmented_files.append(str(filepath))
                    
                    self.stats["advanced_augmented"] += 1
                
                except Exception as e:
                    logger.warning(f"Advanced augmentation {i+1} failed: {e}")
                    continue
            
            return augmented_files
            
        except Exception as e:
            logger.error(f"Failed to apply advanced augmentations to {image_path}: {e}")
            return []
    
    def process_single_class(self, class_name: str, ai_images: int = 20, basic_augs: int = 10, advanced_augs: int = 15) -> Dict:
        """Process a single class with all augmentation techniques"""
        if class_name == '-':
            return {}
        
        logger.info(f"\n🌿 Processing class: {class_name}")
        
        class_stats = {
            "original_count": 0,
            "ai_generated": 0,
            "basic_augmented": 0,
            "advanced_augmented": 0,
            "total_generated": 0
        }
        
        # Count existing images
        train_dir = Path("train/images")
        existing_images = []
        for img_file in train_dir.glob("*.jpg"):
            if class_name.lower() in img_file.name.lower():
                existing_images.append(str(img_file))
        
        class_stats["original_count"] = len(existing_images)
        
        # Step 1: Generate AI images
        logger.info(f"🤖 Generating {ai_images} AI images...")
        ai_generated = self.generate_ai_images_for_class(class_name, ai_images)
        class_stats["ai_generated"] = len(ai_generated)
        
        # Step 2: Apply basic augmentations to existing + AI images
        logger.info(f"🔄 Applying basic augmentations...")
        all_source_images = existing_images + ai_generated
        
        for img_path in all_source_images[:10]:  # Limit to prevent explosion
            basic_augs_result = self.apply_basic_augmentations(img_path, class_name, basic_augs)
            class_stats["basic_augmented"] += len(basic_augs_result)
        
        # Step 3: Apply advanced augmentations to a subset
        logger.info(f"⚡ Applying advanced augmentations...")
        sample_images = random.sample(all_source_images, min(5, len(all_source_images)))
        
        for img_path in sample_images:
            adv_augs_result = self.apply_advanced_augmentations(img_path, class_name, advanced_augs)
            class_stats["advanced_augmented"] += len(adv_augs_result)
        
        class_stats["total_generated"] = (class_stats["ai_generated"] + 
                                        class_stats["basic_augmented"] + 
                                        class_stats["advanced_augmented"])
        
        logger.info(f"✅ {class_name}: {class_stats['total_generated']} new images generated")
        
        return class_stats
    
    def generate_comprehensive_dataset(self, 
                                     ai_images_per_class: int = 20,
                                     basic_augs_per_image: int = 10,
                                     advanced_augs_per_image: int = 15,
                                     max_workers: int = 4):
        """Generate comprehensive dataset for all classes"""
        
        logger.info("🚀 Starting comprehensive dataset generation")
        logger.info(f"📊 Target per class: {ai_images_per_class} AI + {basic_augs_per_image} basic + {advanced_augs_per_image} advanced")
        
        start_time = datetime.now()
        
        # Count existing images
        existing_counts = self.count_existing_images()
        logger.info(f"📁 Found {self.stats['original_images']} existing images across {len(existing_counts)} classes")
        
        # Process classes
        class_results = {}
        
        # Get list of classes to process (exclude '-')
        classes_to_process = [name for name in self.class_names.values() if name != '-']
        
        # Process classes with progress bar
        with tqdm(total=len(classes_to_process), desc="Processing classes") as pbar:
            for class_name in classes_to_process:
                try:
                    result = self.process_single_class(
                        class_name, 
                        ai_images_per_class, 
                        basic_augs_per_image, 
                        advanced_augs_per_image
                    )
                    class_results[class_name] = result
                    self.stats["classes_processed"] += 1
                    
                except Exception as e:
                    logger.error(f"❌ Failed to process {class_name}: {e}")
                    class_results[class_name] = {"error": str(e)}
                
                pbar.update(1)
        
        # Calculate final statistics
        end_time = datetime.now()
        self.stats["end_time"] = end_time.isoformat()
        self.stats["processing_time"] = (end_time - start_time).total_seconds()
        self.stats["total_generated"] = (self.stats["ai_generated"] + 
                                       self.stats["basic_augmented"] + 
                                       self.stats["advanced_augmented"])
        
        # Save detailed results
        results = {
            "generation_stats": self.stats,
            "class_results": class_results,
            "configuration": {
                "ai_images_per_class": ai_images_per_class,
                "basic_augs_per_image": basic_augs_per_image,
                "advanced_augs_per_image": advanced_augs_per_image
            }
        }
        
        results_file = self.output_base / "generation_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"\n🎉 Comprehensive dataset generation completed!")
        logger.info(f"⏱️ Processing time: {self.stats['processing_time']:.2f} seconds")
        logger.info(f"📊 Total images generated: {self.stats['total_generated']}")
        logger.info(f"📁 Results saved to: {results_file}")
        
        return results

    def create_final_dataset(self, train_split: float = 0.8, val_split: float = 0.15, test_split: float = 0.05):
        """Create final dataset with proper train/val/test splits"""
        logger.info("📦 Creating final dataset with train/val/test splits")

        final_dataset_dir = self.output_base / "final_dataset"

        # Directories
        train_img_dir = final_dataset_dir / "train" / "images"
        train_lbl_dir = final_dataset_dir / "train" / "labels"
        val_img_dir = final_dataset_dir / "valid" / "images"
        val_lbl_dir = final_dataset_dir / "valid" / "labels"
        test_img_dir = final_dataset_dir / "test" / "images"
        test_lbl_dir = final_dataset_dir / "test" / "labels"

        total_images = 0
        split_stats = {"train": 0, "val": 0, "test": 0}

        for class_id, class_name in self.class_names.items():
            if class_name == '-':
                continue

            # Collect all images for this class
            all_images = []

            # Original images
            train_dir = Path("train/images")
            for img_file in train_dir.glob("*.jpg"):
                if class_name.lower() in img_file.name.lower():
                    all_images.append(("original", str(img_file)))

            # AI generated images
            ai_dir = self.output_base / "ai_generated" / class_name
            if ai_dir.exists():
                for img_file in ai_dir.glob("*.jpg"):
                    all_images.append(("ai_generated", str(img_file)))

            # Basic augmented images
            basic_dir = self.output_base / "basic_augmented" / class_name
            if basic_dir.exists():
                for img_file in basic_dir.glob("*.jpg"):
                    all_images.append(("basic_augmented", str(img_file)))

            # Advanced augmented images
            adv_dir = self.output_base / "advanced_augmented" / class_name
            if adv_dir.exists():
                for img_file in adv_dir.glob("*.jpg"):
                    all_images.append(("advanced_augmented", str(img_file)))

            # Shuffle and split
            random.shuffle(all_images)

            n_total = len(all_images)
            n_train = int(n_total * train_split)
            n_val = int(n_total * val_split)
            n_test = n_total - n_train - n_val

            # Split images
            train_images = all_images[:n_train]
            val_images = all_images[n_train:n_train + n_val]
            test_images = all_images[n_train + n_val:]

            # Copy images and create labels
            for split_name, images, img_dir, lbl_dir in [
                ("train", train_images, train_img_dir, train_lbl_dir),
                ("val", val_images, val_img_dir, val_lbl_dir),
                ("test", test_images, test_img_dir, test_lbl_dir)
            ]:
                for img_type, img_path in images:
                    try:
                        # Copy image
                        src_path = Path(img_path)
                        dst_img = img_dir / f"{class_name}_{img_type}_{src_path.stem}.jpg"
                        dst_img.write_bytes(src_path.read_bytes())

                        # Create label file
                        dst_lbl = lbl_dir / f"{dst_img.stem}.txt"
                        # YOLO format: class_id x_center y_center width height
                        dst_lbl.write_text(f"{class_id} 0.5 0.5 0.8 0.8\n")

                        split_stats[split_name] += 1
                        total_images += 1

                    except Exception as e:
                        logger.warning(f"Failed to copy {img_path}: {e}")

        # Create updated data.yaml
        final_config = {
            'train': 'final_dataset/train/images',
            'val': 'final_dataset/valid/images',
            'test': 'final_dataset/test/images',
            'nc': self.num_classes,
            'names': self.class_names
        }

        with open(final_dataset_dir / "data.yaml", 'w') as f:
            yaml.dump(final_config, f, default_flow_style=False)

        logger.info(f"✅ Final dataset created with {total_images} images")
        logger.info(f"📊 Split: Train={split_stats['train']}, Val={split_stats['val']}, Test={split_stats['test']}")
        logger.info(f"📁 Location: {final_dataset_dir}")

        return str(final_dataset_dir), split_stats

    def print_final_summary(self):
        """Print comprehensive summary of dataset generation"""
        print("\n" + "="*80)
        print("🌿 COMPREHENSIVE DATASET GENERATION SUMMARY 🌿")
        print("="*80)
        print(f"⏱️ Processing Time: {self.stats['processing_time']:.2f} seconds")
        print(f"🏷️ Classes Processed: {self.stats['classes_processed']}")
        print(f"📁 Original Images: {self.stats['original_images']}")
        print(f"🤖 AI Generated: {self.stats['ai_generated']}")
        print(f"🔄 Basic Augmented: {self.stats['basic_augmented']}")
        print(f"⚡ Advanced Augmented: {self.stats['advanced_augmented']}")
        print(f"📈 Total Generated: {self.stats['total_generated']}")
        print(f"📊 Total Dataset Size: {self.stats['original_images'] + self.stats['total_generated']}")
        print("="*80)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Comprehensive Dataset Generator for Medicinal Plants')
    parser.add_argument('--data', type=str, default='data.yaml', help='Path to data.yaml file')
    parser.add_argument('--ai-images', type=int, default=20, help='AI images per class')
    parser.add_argument('--basic-augs', type=int, default=10, help='Basic augmentations per image')
    parser.add_argument('--advanced-augs', type=int, default=15, help='Advanced augmentations per image')
    parser.add_argument('--workers', type=int, default=4, help='Number of worker processes')
    parser.add_argument('--create-final', action='store_true', help='Create final dataset with splits')

    args = parser.parse_args()

    print("🌿" * 40)
    print("🌿 COMPREHENSIVE DATASET GENERATOR 🌿")
    print("🌿" * 40)
    print()

    try:
        # Initialize generator
        generator = ComprehensiveDatasetGenerator(args.data)

        # Generate comprehensive dataset
        results = generator.generate_comprehensive_dataset(
            ai_images_per_class=args.ai_images,
            basic_augs_per_image=args.basic_augs,
            advanced_augs_per_image=args.advanced_augs,
            max_workers=args.workers
        )

        # Create final dataset if requested
        if args.create_final:
            final_path, split_stats = generator.create_final_dataset()
            print(f"\n📦 Final dataset created at: {final_path}")

        # Print summary
        generator.print_final_summary()

        print(f"\n🎉 Dataset generation completed successfully!")
        print(f"📁 Output directory: {generator.output_base}")

    except Exception as e:
        logger.error(f"❌ Dataset generation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
