#!/usr/bin/env python3
"""
Setup script for Enhanced Global Plant AI Recognition System
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_banner():
    print("🌿" * 60)
    print("🌿 ENHANCED GLOBAL PLANT AI SETUP")
    print("🌿" * 60)
    print()

def install_requirements():
    """Install required packages"""
    print("📦 Installing enhanced requirements...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements_enhanced.txt'
        ])
        print("✅ Enhanced requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def setup_api_keys():
    """Setup API keys for global plant identification"""
    print("\n🔑 Setting up API Keys for Global Plant Identification")
    print("=" * 50)
    
    api_keys = {
        'PLANTNET_API_KEY': {
            'description': 'PlantNet API (Free - Register at https://my.plantnet.org/)',
            'required': False,
            'example': 'your_plantnet_api_key_here'
        },
        'PLANT_ID_API_KEY': {
            'description': 'Plant.id API (Commercial - Register at https://plant.id/)',
            'required': False,
            'example': 'your_plant_id_api_key_here'
        },
        'GOOGLE_VISION_API_KEY': {
            'description': 'Google Vision API (Register at https://cloud.google.com/vision)',
            'required': False,
            'example': 'your_google_vision_api_key_here'
        }
    }
    
    print("📝 API Key Setup Instructions:")
    print()
    
    for key, info in api_keys.items():
        print(f"🔹 {key}:")
        print(f"   Description: {info['description']}")
        print(f"   Required: {'Yes' if info['required'] else 'No (Optional)'}")
        print()
    
    print("💡 Note: The system works without API keys using local identification")
    print("   For best results, register for at least PlantNet API (free)")
    print()
    
    setup_choice = input("Do you want to setup API keys now? (y/n): ").lower().strip()
    
    if setup_choice == 'y':
        env_file = Path('medicinal-plant-detection/.env')
        
        # Read existing .env file
        env_content = ""
        if env_file.exists():
            env_content = env_file.read_text()
        
        print("\n🔧 Enter your API keys (press Enter to skip):")
        
        for key, info in api_keys.items():
            current_value = input(f"{key}: ").strip()
            
            if current_value:
                # Update or add the key in .env file
                if f"{key}=" in env_content:
                    # Replace existing key
                    lines = env_content.split('\n')
                    for i, line in enumerate(lines):
                        if line.startswith(f"{key}="):
                            lines[i] = f"{key}={current_value}"
                            break
                    env_content = '\n'.join(lines)
                else:
                    # Add new key
                    env_content += f"\n{key}={current_value}"
        
        # Write updated .env file
        env_file.write_text(env_content)
        print("✅ API keys saved to .env file")
    
    else:
        print("⏭️ Skipping API key setup - system will use local identification")

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "uploads",
        "logs",
        "plant_images",
        "learning_data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created: {directory}")

def test_system():
    """Test the enhanced system"""
    print("\n🧪 Testing Enhanced System...")
    
    try:
        # Test imports
        print("Testing imports...")
        
        import flask
        print("✅ Flask")
        
        import pymongo
        print("✅ PyMongo")
        
        import requests
        print("✅ Requests")
        
        from PIL import Image
        print("✅ PIL")
        
        import numpy as np
        print("✅ NumPy")
        
        # Test database connection
        print("\nTesting database connection...")
        sys.path.append('medicinal-plant-detection/backend')
        
        from database import get_db
        db = get_db()
        print("✅ Database connection")
        
        # Test global AI system
        print("\nTesting Global AI system...")
        from global_plant_ai import GlobalPlantAI
        global_ai = GlobalPlantAI()
        print("✅ Global AI system initialized")
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False

def create_quick_start_guide():
    """Create a quick start guide"""
    guide_content = """
# 🌿 Enhanced Global Plant AI Recognition System - Quick Start Guide

## 🚀 How to Run the System

1. **Start the Enhanced Backend:**
   ```bash
   python enhanced_backend.py
   ```

2. **Open your browser and go to:**
   ```
   http://127.0.0.1:5000
   ```

## 🌟 Features

### 🌍 Global Plant Identification
- Upload ANY plant image from anywhere in the world
- Uses multiple AI APIs: PlantNet, Plant.id, Google Vision
- Provides scientific names, common names, and local names
- Includes medicinal properties and uses

### 📚 Auto-Learning Database
- Automatically adds new plants to your database
- Learns from each upload to improve future identifications
- Builds a comprehensive plant knowledge base
- Tracks identification statistics

### 💊 Medicinal Information
- Provides detailed medicinal uses
- Safety warnings and precautions
- Traditional preparation methods
- Parts of plant used medicinally

## 🔑 API Keys (Optional but Recommended)

For best results, register for these free/paid APIs:

1. **PlantNet API (Free):**
   - Register at: https://my.plantnet.org/
   - Add to .env: `PLANTNET_API_KEY=your_key_here`

2. **Plant.id API (Paid):**
   - Register at: https://plant.id/
   - Add to .env: `PLANT_ID_API_KEY=your_key_here`

3. **Google Vision API (Paid):**
   - Register at: https://cloud.google.com/vision
   - Add to .env: `GOOGLE_VISION_API_KEY=your_key_here`

## 📊 Database Statistics

View learning statistics at: http://127.0.0.1:5000 (click "View Statistics")

## 🔍 Search Plants

Search your plant database: http://127.0.0.1:5000/search-plants?q=neem

## 🛠️ Troubleshooting

1. **Import Errors:** Run `pip install -r requirements_enhanced.txt`
2. **Database Issues:** Check MongoDB connection in .env file
3. **API Errors:** Verify API keys in .env file
4. **Slow Identification:** Normal for global APIs (10-30 seconds)

## 📈 Expected Results

- **Without API Keys:** Local identification with basic results
- **With PlantNet API:** Scientific database with accurate botanical info
- **With All APIs:** Comprehensive global identification with high accuracy

## 🎯 Usage Tips

1. **Best Images:** Clear, well-lit photos of leaves, flowers, or whole plant
2. **Multiple Angles:** Upload different parts of the plant for better accuracy
3. **High Resolution:** Higher quality images give better results
4. **Patience:** Global identification takes time but provides excellent results

Enjoy exploring the world of plants with AI! 🌿🤖
"""
    
    with open('QUICK_START_GUIDE.md', 'w') as f:
        f.write(guide_content)
    
    print("✅ Quick start guide created: QUICK_START_GUIDE.md")

def main():
    """Main setup function"""
    print_banner()
    
    print("🎯 This setup will configure the Enhanced Global Plant AI Recognition System")
    print("   Features: Global identification, Auto-learning, Multiple AI APIs")
    print()
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed at requirements installation")
        return
    
    # Setup API keys
    setup_api_keys()
    
    # Create directories
    create_directories()
    
    # Test system
    if not test_system():
        print("⚠️ Some tests failed, but system may still work")
    
    # Create quick start guide
    create_quick_start_guide()
    
    print("\n" + "🎉" * 60)
    print("🎉 ENHANCED GLOBAL PLANT AI SYSTEM SETUP COMPLETE!")
    print("🎉" * 60)
    print()
    print("🚀 To start the system:")
    print("   python enhanced_backend.py")
    print()
    print("🌐 Then open: http://127.0.0.1:5000")
    print()
    print("📖 Read QUICK_START_GUIDE.md for detailed instructions")
    print()
    print("🌿 Upload any plant image and get global AI identification!")

if __name__ == "__main__":
    main()
