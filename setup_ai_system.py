#!/usr/bin/env python3
"""
🌱 AI Medicinal Plant Recognition System Setup
Quick setup script for the complete AI system
"""

import os
import sys
import subprocess
from pathlib import Path

def create_directories():
    """Create necessary directories"""
    directories = [
        'dataset/train/images', 'dataset/train/labels',
        'dataset/val/images', 'dataset/val/labels', 
        'dataset/test/images', 'dataset/test/labels',
        'augmented', 'new_plants', 'models', 'logs',
        'uploads', 'generated_images', 'temp',
        'runs/train', 'runs/detect'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements_ai_system.txt'
        ])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False
    
    return True

def create_sample_data_yaml():
    """Create initial data.yaml file"""
    data_yaml_content = """# AI Medicinal Plant Recognition Dataset Configuration

# Paths (relative to this file)
train: dataset/train
val: dataset/val
test: dataset/test

# Number of classes (will be updated automatically)
nc: 0

# Class names (will be updated automatically)
names: []

# Optional: Dataset info
dataset_name: "AI Medicinal Plants"
description: "Self-learning medicinal plant recognition dataset"
version: "1.0"
"""
    
    with open('data.yaml', 'w') as f:
        f.write(data_yaml_content)
    
    print("✅ Created initial data.yaml")

def setup_config():
    """Setup configuration with user input"""
    print("\n🔧 Configuration Setup")
    print("=" * 40)
    
    # Get API keys from user
    gemini_key = input("Enter your Gemini API key (or press Enter to skip): ").strip()
    replicate_key = input("Enter your Replicate API key (or press Enter to skip): ").strip()
    
    # Read config template
    with open('config.yaml', 'r') as f:
        config_content = f.read()
    
    # Update with user keys
    if gemini_key:
        config_content = config_content.replace('your-gemini-api-key-here', gemini_key)
    
    if replicate_key:
        config_content = config_content.replace('your-replicate-api-key-here', replicate_key)
    
    # Save updated config
    with open('config.yaml', 'w') as f:
        f.write(config_content)
    
    print("✅ Configuration updated")

def main():
    """Main setup function"""
    print("🌱 AI Medicinal Plant Recognition System Setup")
    print("=" * 60)
    
    # Create directories
    print("\n📁 Creating directory structure...")
    create_directories()
    
    # Install requirements
    print("\n📦 Installing Python packages...")
    if not install_requirements():
        print("❌ Setup failed during package installation")
        return 1
    
    # Create data.yaml
    print("\n📝 Creating configuration files...")
    create_sample_data_yaml()
    
    # Setup configuration
    setup_config()
    
    print("\n🎉 Setup completed successfully!")
    print("=" * 60)
    print("🚀 Next steps:")
    print("1. Run: python auto_augment_and_generate.py")
    print("2. Open browser to: http://localhost:5000")
    print("3. Upload plant images to test the system")
    print("=" * 60)
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
