"""
Demo script for Medicinal Plant Recognition System
This script demonstrates the core functionality without the web interface
"""

import os
import cv2
import numpy as np
from PIL import Image
import torch
from ultralytics import YOLO
from plant_database import get_plant_info, get_all_plant_names
import matplotlib.pyplot as plt

def load_demo_model():
    """Load the trained model for demo"""
    model_path = "best.pt"
    
    if not os.path.exists(model_path):
        print("❌ Model file 'best.pt' not found!")
        print("🔧 Please train the model first using: python train_model.py")
        return None
    
    try:
        model = YOLO(model_path)
        print("✅ Model loaded successfully!")
        print(f"📊 Model supports {len(model.names)} plant classes")
        return model
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def demo_prediction(model, image_path):
    """Demonstrate prediction on a sample image"""
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return
    
    print(f"\n🔍 Analyzing image: {image_path}")
    
    # Load and preprocess image
    image = cv2.imread(image_path)
    if image is None:
        print("❌ Could not load image")
        return
    
    # Run prediction
    results = model(image, conf=0.25, iou=0.45)
    
    predictions = []
    for result in results:
        boxes = result.boxes
        if boxes is not None:
            for box in boxes:
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                class_name = model.names[class_id]
                
                predictions.append({
                    'class_name': class_name,
                    'confidence': confidence,
                    'bbox': box.xyxy[0].tolist()
                })
    
    # Sort by confidence
    predictions.sort(key=lambda x: x['confidence'], reverse=True)
    
    if predictions:
        print(f"✅ Found {len(predictions)} plant(s)!")
        
        # Display top 3 predictions
        for i, pred in enumerate(predictions[:3]):
            plant_name = pred['class_name']
            confidence = pred['confidence']
            
            print(f"\n🌿 Prediction #{i+1}:")
            print(f"   Plant: {plant_name}")
            print(f"   Confidence: {confidence:.1%}")
            
            # Get plant information
            plant_info = get_plant_info(plant_name)
            print(f"   Scientific Name: {plant_info['scientific_name']}")
            print(f"   Local Name: {plant_info['local_name']}")
            print(f"   Medicinal Uses: {plant_info['medicinal_uses']}")
    else:
        print("❌ No plants detected in the image")

def demo_database():
    """Demonstrate the plant database functionality"""
    print("\n📚 Plant Database Demo")
    print("=" * 40)
    
    # Get all plant names
    all_plants = get_all_plant_names()
    print(f"📊 Total plants in database: {len(all_plants)}")
    
    # Show some sample plants
    print("\n🌿 Sample plants:")
    sample_plants = ['Aloe vera', 'Ocimum tenuiflorum', 'Turmeric', 'Ashwagandha', 'neem']
    
    for plant in sample_plants:
        if plant in all_plants:
            info = get_plant_info(plant)
            print(f"\n• {plant}")
            print(f"  Scientific: {info['scientific_name']}")
            print(f"  Uses: {info['medicinal_uses'][:60]}...")

def demo_search():
    """Demonstrate search functionality"""
    print("\n🔍 Search Demo")
    print("=" * 40)
    
    from plant_database import search_plants_by_use
    
    search_terms = ['diabetes', 'skin', 'respiratory', 'fever']
    
    for term in search_terms:
        results = search_plants_by_use(term)
        print(f"\n🔎 Plants for '{term}': {len(results)} found")
        
        for i, (plant_name, info) in enumerate(results[:3]):
            print(f"   {i+1}. {plant_name}")

def create_sample_test_image():
    """Create a sample test image for demo purposes"""
    print("\n🎨 Creating sample test image...")
    
    # Create a simple test image with text
    img = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # Add some text
    cv2.putText(img, "Sample Plant Image", (150, 200), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 100, 0), 2)
    cv2.putText(img, "Replace with actual plant photo", (100, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    # Save the image
    cv2.imwrite("sample_plant.jpg", img)
    print("✅ Sample image created: sample_plant.jpg")
    return "sample_plant.jpg"

def run_system_check():
    """Run a comprehensive system check"""
    print("🔧 System Check")
    print("=" * 40)
    
    # Check Python version
    import sys
    print(f"🐍 Python: {sys.version.split()[0]}")
    
    # Check key dependencies
    dependencies = {
        'torch': 'PyTorch',
        'cv2': 'OpenCV',
        'ultralytics': 'YOLOv8',
        'streamlit': 'Streamlit',
        'PIL': 'Pillow'
    }
    
    for module, name in dependencies.items():
        try:
            if module == 'PIL':
                import PIL
                version = PIL.__version__
            elif module == 'cv2':
                import cv2
                version = cv2.__version__
            else:
                mod = __import__(module)
                version = getattr(mod, '__version__', 'Unknown')
            
            print(f"✅ {name}: {version}")
        except ImportError:
            print(f"❌ {name}: Not installed")
    
    # Check CUDA
    if torch.cuda.is_available():
        print(f"🚀 CUDA: Available ({torch.cuda.device_count()} GPU(s))")
    else:
        print("💻 CUDA: Not available (CPU only)")
    
    # Check dataset
    if os.path.exists('data.yaml'):
        print("✅ Dataset configuration: Found")
    else:
        print("❌ Dataset configuration: Missing")
    
    # Check model
    if os.path.exists('best.pt'):
        print("✅ Trained model: Found")
    else:
        print("❌ Trained model: Missing")

def main():
    """Main demo function"""
    print("🌿 Medicinal Plant Recognition System - Demo")
    print("=" * 50)
    
    # Run system check
    run_system_check()
    
    # Demo database functionality
    demo_database()
    
    # Demo search functionality
    demo_search()
    
    # Try to load and demo the model
    model = load_demo_model()
    
    if model:
        # Look for sample images in the test directory
        test_images = []
        if os.path.exists('test/images'):
            test_images = [f for f in os.listdir('test/images') 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        
        if test_images:
            # Use first test image
            sample_image = os.path.join('test/images', test_images[0])
            demo_prediction(model, sample_image)
        else:
            # Create a sample image
            sample_image = create_sample_test_image()
            print("\n⚠️  Using sample image for demo")
            print("   For real testing, use actual plant photos")
    
    print("\n🎉 Demo completed!")
    print("\n📋 Next steps:")
    print("   1. Train the model: python train_model.py")
    print("   2. Run the web app: streamlit run app.py")
    print("   3. Or use quick start: python run.py")

if __name__ == "__main__":
    main()
