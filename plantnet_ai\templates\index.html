{% extends "base.html" %}

{% block title %}PlantNet AI - Advanced Plant Identification System{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-gradient-success text-white py-5">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    Identify Any Plant with AI
                </h1>
                <p class="lead mb-4">
                    World-class plant identification system powered by state-of-the-art computer vision. 
                    Upload photos of leaves, flowers, fruits, or bark to instantly identify plant species.
                </p>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="{{ url_for('identify_page') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-camera me-2"></i>Start Identifying
                    </a>
                    <a href="{{ url_for('search_page') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-search me-2"></i>Browse Species
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="hero-image-container">
                    <img src="{{ url_for('static', filename='images/hero-plant.jpg') }}" 
                         alt="Plant identification" 
                         class="img-fluid rounded-3 shadow-lg"
                         style="max-height: 400px;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">How It Works</h2>
                <p class="lead text-muted">
                    Our AI model analyzes plant images using advanced computer vision techniques
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-card text-center p-4 h-100">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-upload fa-3x text-success"></i>
                    </div>
                    <h4>1. Upload Images</h4>
                    <p class="text-muted">
                        Upload clear photos of different plant parts: leaves, flowers, fruits, bark, or the whole plant.
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card text-center p-4 h-100">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-tags fa-3x text-success"></i>
                    </div>
                    <h4>2. Tag Organs</h4>
                    <p class="text-muted">
                        Specify which plant part each image shows to help our AI model make more accurate predictions.
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card text-center p-4 h-100">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-brain fa-3x text-success"></i>
                    </div>
                    <h4>3. Get Results</h4>
                    <p class="text-muted">
                        Receive instant identification results with confidence scores and detailed species information.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technology Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="display-6 fw-bold mb-4">Powered by Advanced AI</h2>
                <p class="lead mb-4">
                    Our system uses EfficientNet, a state-of-the-art convolutional neural network 
                    architecture specifically optimized for plant species classification.
                </p>
                
                <div class="row g-3">
                    <div class="col-sm-6">
                        <div class="tech-feature">
                            <i class="fas fa-eye text-success me-2"></i>
                            <strong>Computer Vision</strong>
                            <p class="small text-muted mb-0">Advanced image analysis and feature extraction</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="tech-feature">
                            <i class="fas fa-brain text-success me-2"></i>
                            <strong>Deep Learning</strong>
                            <p class="small text-muted mb-0">EfficientNet CNN architecture</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="tech-feature">
                            <i class="fas fa-leaf text-success me-2"></i>
                            <strong>Organ-Aware</strong>
                            <p class="small text-muted mb-0">Specialized for different plant parts</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="tech-feature">
                            <i class="fas fa-globe text-success me-2"></i>
                            <strong>Global Coverage</strong>
                            <p class="small text-muted mb-0">Worldwide plant species database</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="tech-diagram bg-white p-4 rounded-3 shadow">
                    <h5 class="text-center mb-3">Model Architecture</h5>
                    <div class="architecture-flow">
                        <div class="arch-step">
                            <div class="arch-box bg-primary text-white">Image Input</div>
                            <i class="fas fa-arrow-down text-muted"></i>
                        </div>
                        <div class="arch-step">
                            <div class="arch-box bg-info text-white">EfficientNet Backbone</div>
                            <i class="fas fa-arrow-down text-muted"></i>
                        </div>
                        <div class="arch-step">
                            <div class="arch-box bg-warning text-dark">Organ Fusion</div>
                            <i class="fas fa-arrow-down text-muted"></i>
                        </div>
                        <div class="arch-step">
                            <div class="arch-box bg-success text-white">Species Classification</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-card">
                    <h3 class="display-4 fw-bold text-success" data-counter="1000">0</h3>
                    <p class="text-muted">Plant Species</p>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-card">
                    <h3 class="display-4 fw-bold text-success" data-counter="95">0</h3>
                    <p class="text-muted">Accuracy %</p>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-card">
                    <h3 class="display-4 fw-bold text-success" data-counter="6">0</h3>
                    <p class="text-muted">Plant Organs</p>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-card">
                    <h3 class="display-4 fw-bold text-success" data-counter="24">0</h3>
                    <p class="text-muted">Hours Uptime</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-success text-white">
    <div class="container text-center">
        <h2 class="display-5 fw-bold mb-3">Ready to Identify Plants?</h2>
        <p class="lead mb-4">
            Start exploring the world of plants with our advanced AI identification system
        </p>
        <a href="{{ url_for('identify_page') }}" class="btn btn-light btn-lg">
            <i class="fas fa-camera me-2"></i>Start Now
        </a>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
.hero-section {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.min-vh-75 {
    min-height: 75vh;
}

.feature-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.tech-feature {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.tech-feature:last-child {
    border-bottom: none;
}

.architecture-flow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.arch-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.arch-box {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    min-width: 150px;
}

.stat-card h3 {
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }
    
    .architecture-flow {
        gap: 0.5rem;
    }
    
    .arch-box {
        min-width: 120px;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Counter animation
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('[data-counter]');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-counter'));
        const duration = 2000; // 2 seconds
        const step = target / (duration / 16); // 60fps
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 16);
    };
    
    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(counter => {
        observer.observe(counter);
    });
});
</script>
{% endblock %}
