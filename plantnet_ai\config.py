"""
Configuration settings for PlantNet AI System
"""

import os
from pathlib import Path

# Base directory
BASE_DIR = Path(__file__).parent.absolute()

class Config:
    """Base configuration class"""
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'plantnet-ai-secret-key-2024'
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    # Database settings
    MONGODB_URI = os.environ.get('MONGODB_URI') or 'mongodb://localhost:27017/plantnet_ai'
    DATABASE_NAME = 'plantnet_ai'
    
    # Model settings
    MODEL_PATH = BASE_DIR / 'models' / 'trained_models'
    MODEL_NAME = 'efficientnet_plant_classifier.h5'
    MODEL_CONFIG_PATH = BASE_DIR / 'models' / 'model_config.json'
    
    # Data paths
    DATA_DIR = BASE_DIR / 'data'
    RAW_DATA_DIR = DATA_DIR / 'raw'
    PROCESSED_DATA_DIR = DATA_DIR / 'processed'
    ANNOTATIONS_DIR = DATA_DIR / 'annotations'
    
    # Upload settings
    UPLOAD_FOLDER = BASE_DIR / 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    
    # Model inference settings
    IMAGE_SIZE = (224, 224)  # EfficientNet input size
    BATCH_SIZE = 32
    TOP_K_PREDICTIONS = 10  # Number of top predictions to return
    CONFIDENCE_THRESHOLD = 0.01  # Minimum confidence to include in results
    
    # Plant organs supported
    SUPPORTED_ORGANS = [
        'leaf',
        'flower', 
        'fruit',
        'bark',
        'stem',
        'whole_plant'
    ]
    
    # Geographic projects
    SUPPORTED_PROJECTS = [
        'world-flora',
        'medicinal-plants',
        'indian-flora',
        'european-flora'
    ]

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Production database
    MONGODB_URI = os.environ.get('MONGODB_URI') or 'mongodb://localhost:27017/plantnet_ai_prod'

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    
    # Test database
    MONGODB_URI = os.environ.get('TEST_MONGODB_URI') or 'mongodb://localhost:27017/plantnet_ai_test'

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# Model configuration
MODEL_CONFIG = {
    'architecture': 'EfficientNet-B2',
    'input_shape': (224, 224, 3),
    'num_classes': 100,  # Will be updated based on dataset
    'dropout_rate': 0.3,
    'learning_rate': 0.001,
    'batch_size': 32,
    'epochs': 100,
    'early_stopping_patience': 10,
    'reduce_lr_patience': 5,
    'data_augmentation': {
        'rotation_range': 20,
        'width_shift_range': 0.2,
        'height_shift_range': 0.2,
        'horizontal_flip': True,
        'zoom_range': 0.2,
        'brightness_range': [0.8, 1.2]
    }
}

# Plant database schema
PLANT_SCHEMA = {
    'plant_id': str,
    'scientific_name': str,
    'family': str,
    'genus': str,
    'species': str,
    'common_names': {
        'english': str,
        'hindi': str,
        'tamil': str,
        'sanskrit': str,
        'local': [str]
    },
    'medicinal_uses': [str],
    'parts_used': [str],
    'preparation_methods': [str],
    'safety_info': {
        'warnings': [str],
        'contraindications': [str],
        'dosage': str,
        'side_effects': [str]
    },
    'botanical_info': {
        'description': str,
        'habitat': str,
        'distribution': [str],
        'flowering_season': str,
        'fruiting_season': str
    },
    'gallery': {
        'leaf_images': [str],
        'flower_images': [str],
        'fruit_images': [str],
        'bark_images': [str],
        'whole_plant_images': [str]
    },
    'verification': {
        'expert_verified': bool,
        'community_verified': bool,
        'verification_count': int,
        'last_updated': str
    },
    'metadata': {
        'created_date': str,
        'updated_date': str,
        'data_source': str,
        'quality_score': float
    }
}

# API response format
API_RESPONSE_FORMAT = {
    'success': bool,
    'message': str,
    'data': {
        'predictions': [
            {
                'scientific_name': str,
                'common_name': str,
                'confidence': float,
                'plant_id': str,
                'family': str,
                'reference_image': str
            }
        ],
        'processing_time': float,
        'model_version': str,
        'organs_detected': [str]
    },
    'metadata': {
        'timestamp': str,
        'request_id': str,
        'images_processed': int
    }
}

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'plantnet_ai.log',
            'formatter': 'default'
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'default'
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file', 'console']
    }
}

# Create necessary directories
def create_directories():
    """Create necessary directories for the project"""
    directories = [
        Config.UPLOAD_FOLDER,
        Config.MODEL_PATH,
        Config.DATA_DIR,
        Config.RAW_DATA_DIR,
        Config.PROCESSED_DATA_DIR,
        Config.ANNOTATIONS_DIR,
        BASE_DIR / 'logs',
        BASE_DIR / 'frontend' / 'static' / 'css',
        BASE_DIR / 'frontend' / 'static' / 'js',
        BASE_DIR / 'frontend' / 'static' / 'images',
        BASE_DIR / 'frontend' / 'templates'
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        
if __name__ == '__main__':
    create_directories()
    print("✅ Project directories created successfully!")
