# 🌱 AI Medicinal Plant Recognition System Configuration

# Model Paths
yolo_model_path: 'models/best.pt'
data_yaml_path: 'data.yaml'

# Dataset Paths
dataset_path: 'dataset'
augmented_path: 'augmented'
new_plants_path: 'new_plants'

# Detection Settings
confidence_threshold: 0.5

# AI API Keys (Replace with your actual keys)
gemini_api_key: 'your-gemini-api-key-here'
replicate_api_key: 'your-replicate-api-key-here'

# Image Generation Settings
images_per_plant: 100        # Total images per plant after augmentation
synthetic_images: 20         # Number of AI-generated base images

# Training Settings
training_epochs: 50
batch_size: 16
image_size: 640

# Web Interface Settings
upload_max_size: 16777216    # 16MB in bytes
allowed_extensions: ['jpg', 'jpeg', 'png', 'bmp', 'webp']

# Logging
log_level: 'INFO'
log_file: 'logs/plant_ai.log'
