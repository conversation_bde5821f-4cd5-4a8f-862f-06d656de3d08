# Enhanced Global Plant AI Recognition System Requirements

# Core Flask and web framework
flask>=2.3.0
flask-cors>=4.0.0
werkzeug>=2.3.0

# Image processing
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# Database and storage
pymongo>=4.5.0
gridfs>=0.1.0
python-dotenv>=1.0.0

# HTTP requests for API calls
requests>=2.31.0
aiohttp>=3.8.0

# Async support
asyncio-compat>=0.1.0

# Scientific computing
scipy>=1.11.0
scikit-image>=0.21.0

# Machine learning (optional for local models)
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0

# Data handling
pandas>=2.0.0
pyyaml>=6.0.0

# Utilities
hashlib2>=1.0.0
pathlib2>=2.3.0
tqdm>=4.65.0

# Logging and monitoring
colorlog>=6.7.0

# Image format support
webp>=0.1.0

# Optional: Advanced AI APIs (uncomment if you have API keys)
# google-cloud-vision>=3.4.0
# plantnet-python>=1.0.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
