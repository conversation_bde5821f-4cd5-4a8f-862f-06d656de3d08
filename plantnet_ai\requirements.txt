# PlantNet AI - World-Class Plant Identification System
# Core Dependencies for Model-First Architecture

# Web Framework
flask>=2.3.0
flask-cors>=4.0.0
werkzeug>=2.3.0

# Deep Learning & AI
tensorflow>=2.13.0
keras>=2.13.0
torch>=2.0.0
torchvision>=0.15.0
efficientnet-pytorch>=0.7.1

# Image Processing
Pillow>=10.0.0
opencv-python>=4.8.0
scikit-image>=0.21.0
albumentations>=1.3.0

# Data Science & ML
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
scipy>=1.11.0

# Database
pymongo>=4.5.0
gridfs>=0.1.0

# Data Handling
h5py>=3.9.0
tqdm>=4.65.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Configuration & Environment
python-dotenv>=1.0.0
pyyaml>=6.0.0
configparser>=5.3.0

# API & Web
requests>=2.31.0
flask-restful>=0.3.10
marshmallow>=3.20.0

# Utilities
pathlib2>=2.3.0
uuid>=1.30
datetime>=5.2
hashlib2>=1.0.0

# Logging & Monitoring
colorlog>=6.7.0
python-json-logger>=2.0.0

# Development & Testing
pytest>=7.4.0
pytest-flask>=1.2.0
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0

# Data Augmentation
imgaug>=0.4.0
torchvision>=0.15.0

# Model Optimization
onnx>=1.14.0
tensorboard>=2.13.0

# Deployment
gunicorn>=21.2.0
waitress>=2.1.0

# Optional: Advanced Features
# fastapi>=0.103.0  # Alternative to Flask
# uvicorn>=0.23.0   # ASGI server
# redis>=4.6.0      # Caching
# celery>=5.3.0     # Background tasks
