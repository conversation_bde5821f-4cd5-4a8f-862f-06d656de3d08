"""
Plant Inference Engine
High-performance inference system for plant species identification
"""

import os
import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
import logging
import time
from PIL import Image
import cv2

import tensorflow as tf
from tensorflow import keras

from .efficientnet_model import EfficientNetPlantClassifier, create_organ_encoding

logger = logging.getLogger(__name__)

class PlantInferenceEngine:
    """
    High-performance inference engine for plant species identification
    Supports single and batch predictions with confidence scoring
    """
    
    def __init__(self, 
                 model_path: str,
                 config_path: Optional[str] = None,
                 encoders_path: Optional[str] = None):
        """
        Initialize the inference engine
        
        Args:
            model_path: Path to the trained model file
            config_path: Path to model configuration file
            encoders_path: Path to label encoders file
        """
        self.model_path = model_path
        self.config_path = config_path or model_path.replace('.h5', '_config.json')
        self.encoders_path = encoders_path
        
        # Model and configuration
        self.model = None
        self.config = {}
        self.class_names = []
        self.organ_names = []
        self.image_size = (224, 224)
        
        # Performance tracking
        self.inference_times = []
        
        # Load model and configuration
        self._load_model_and_config()
        
        logger.info(f"✅ PlantInferenceEngine initialized with model: {model_path}")
    
    def _load_model_and_config(self) -> None:
        """Load the trained model and configuration"""
        try:
            # Load model
            logger.info(f"📥 Loading model from {self.model_path}")
            self.model = keras.models.load_model(self.model_path)
            
            # Load configuration
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    self.config = json.load(f)
                
                self.image_size = tuple(self.config.get('input_shape', [224, 224])[:2])
                logger.info(f"✅ Configuration loaded from {self.config_path}")
            else:
                logger.warning(f"⚠️ Configuration file not found: {self.config_path}")
            
            # Load encoders if available
            if self.encoders_path and os.path.exists(self.encoders_path):
                with open(self.encoders_path, 'r') as f:
                    encoders = json.load(f)
                
                self.class_names = encoders.get('species_classes', [])
                self.organ_names = encoders.get('organ_classes', [])
                logger.info(f"✅ Encoders loaded: {len(self.class_names)} species, {len(self.organ_names)} organs")
            else:
                logger.warning(f"⚠️ Encoders file not found: {self.encoders_path}")
                # Use default organ names
                self.organ_names = ['leaf', 'flower', 'fruit', 'bark', 'stem', 'whole_plant']
            
        except Exception as e:
            logger.error(f"❌ Failed to load model and configuration: {e}")
            raise
    
    def preprocess_image(self, image: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        Preprocess image for model inference
        
        Args:
            image: Image as file path, numpy array, or PIL Image
            
        Returns:
            Preprocessed image array
        """
        try:
            # Load image if it's a file path
            if isinstance(image, str):
                img = cv2.imread(image)
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            elif isinstance(image, Image.Image):
                img = np.array(image.convert('RGB'))
            elif isinstance(image, np.ndarray):
                img = image.copy()
                if len(img.shape) == 3 and img.shape[2] == 3:
                    # Assume BGR and convert to RGB
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            else:
                raise ValueError(f"Unsupported image type: {type(image)}")
            
            # Resize image
            img = cv2.resize(img, self.image_size)
            
            # Normalize to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            # Apply ImageNet normalization
            mean = np.array([0.485, 0.456, 0.406])
            std = np.array([0.229, 0.224, 0.225])
            img = (img - mean) / std
            
            return img
            
        except Exception as e:
            logger.error(f"❌ Failed to preprocess image: {e}")
            raise
    
    def predict_single(self, 
                      image: Union[str, np.ndarray, Image.Image],
                      organ: str,
                      top_k: int = 10,
                      confidence_threshold: float = 0.01) -> List[Dict]:
        """
        Predict plant species for a single image
        
        Args:
            image: Input image
            organ: Plant organ type (leaf, flower, fruit, etc.)
            top_k: Number of top predictions to return
            confidence_threshold: Minimum confidence threshold
            
        Returns:
            List of prediction dictionaries
        """
        start_time = time.time()
        
        try:
            # Preprocess image
            processed_image = self.preprocess_image(image)
            processed_image = np.expand_dims(processed_image, axis=0)  # Add batch dimension
            
            # Create organ encoding
            organ_encoding = create_organ_encoding(organ, self.organ_names)
            organ_encoding = np.expand_dims(organ_encoding, axis=0)  # Add batch dimension
            
            # Make prediction
            predictions = self.model.predict([processed_image, organ_encoding], verbose=0)
            prediction_probs = predictions[0]  # Remove batch dimension
            
            # Get top-k predictions
            top_indices = np.argsort(prediction_probs)[-top_k:][::-1]
            
            results = []
            for idx in top_indices:
                confidence = float(prediction_probs[idx])
                
                if confidence >= confidence_threshold:
                    result = {
                        'class_index': int(idx),
                        'confidence': confidence,
                        'scientific_name': self.class_names[idx] if idx < len(self.class_names) else f'Species_{idx}',
                        'organ': organ
                    }
                    results.append(result)
            
            # Track inference time
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            
            logger.debug(f"🔍 Single prediction completed in {inference_time:.3f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Single prediction failed: {e}")
            raise
    
    def predict_batch(self, 
                     images: List[Union[str, np.ndarray, Image.Image]],
                     organs: List[str],
                     top_k: int = 10,
                     confidence_threshold: float = 0.01) -> List[List[Dict]]:
        """
        Predict plant species for a batch of images
        
        Args:
            images: List of input images
            organs: List of organ types corresponding to each image
            top_k: Number of top predictions to return per image
            confidence_threshold: Minimum confidence threshold
            
        Returns:
            List of prediction lists (one per image)
        """
        start_time = time.time()
        
        try:
            if len(images) != len(organs):
                raise ValueError("Number of images must match number of organs")
            
            # Preprocess all images
            processed_images = []
            organ_encodings = []
            
            for image, organ in zip(images, organs):
                processed_image = self.preprocess_image(image)
                processed_images.append(processed_image)
                
                organ_encoding = create_organ_encoding(organ, self.organ_names)
                organ_encodings.append(organ_encoding)
            
            # Convert to numpy arrays
            batch_images = np.array(processed_images)
            batch_organs = np.array(organ_encodings)
            
            # Make batch prediction
            predictions = self.model.predict([batch_images, batch_organs], verbose=0)
            
            # Process results for each image
            batch_results = []
            for i, prediction_probs in enumerate(predictions):
                # Get top-k predictions for this image
                top_indices = np.argsort(prediction_probs)[-top_k:][::-1]
                
                image_results = []
                for idx in top_indices:
                    confidence = float(prediction_probs[idx])
                    
                    if confidence >= confidence_threshold:
                        result = {
                            'class_index': int(idx),
                            'confidence': confidence,
                            'scientific_name': self.class_names[idx] if idx < len(self.class_names) else f'Species_{idx}',
                            'organ': organs[i]
                        }
                        image_results.append(result)
                
                batch_results.append(image_results)
            
            # Track inference time
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            
            logger.debug(f"🔍 Batch prediction ({len(images)} images) completed in {inference_time:.3f}s")
            
            return batch_results
            
        except Exception as e:
            logger.error(f"❌ Batch prediction failed: {e}")
            raise
    
    def predict_multi_organ(self, 
                           images: List[Union[str, np.ndarray, Image.Image]],
                           organs: List[str],
                           fusion_method: str = 'weighted_average') -> List[Dict]:
        """
        Predict plant species using multiple images of different organs
        
        Args:
            images: List of images showing different plant organs
            organs: List of organ types for each image
            fusion_method: Method to fuse predictions ('weighted_average', 'max_confidence', 'voting')
            
        Returns:
            Fused prediction results
        """
        try:
            # Get predictions for each image
            individual_predictions = self.predict_batch(images, organs)
            
            if not individual_predictions:
                return []
            
            # Initialize combined scores
            num_classes = len(self.class_names) if self.class_names else max(
                max(pred['class_index'] for pred in preds) for preds in individual_predictions if preds
            ) + 1
            
            combined_scores = np.zeros(num_classes)
            organ_weights = {'leaf': 1.0, 'flower': 1.2, 'fruit': 1.1, 'bark': 0.8, 'stem': 0.7, 'whole_plant': 0.9}
            
            # Fuse predictions based on method
            if fusion_method == 'weighted_average':
                total_weight = 0
                for predictions, organ in zip(individual_predictions, organs):
                    weight = organ_weights.get(organ, 1.0)
                    for pred in predictions:
                        combined_scores[pred['class_index']] += pred['confidence'] * weight
                    total_weight += weight
                
                if total_weight > 0:
                    combined_scores /= total_weight
            
            elif fusion_method == 'max_confidence':
                for predictions in individual_predictions:
                    for pred in predictions:
                        combined_scores[pred['class_index']] = max(
                            combined_scores[pred['class_index']], 
                            pred['confidence']
                        )
            
            elif fusion_method == 'voting':
                for predictions in individual_predictions:
                    if predictions:  # If there are predictions for this image
                        best_pred = max(predictions, key=lambda x: x['confidence'])
                        combined_scores[best_pred['class_index']] += 1
                
                # Normalize by number of images
                combined_scores /= len(images)
            
            # Get top predictions from combined scores
            top_indices = np.argsort(combined_scores)[-10:][::-1]
            
            fused_results = []
            for idx in top_indices:
                confidence = float(combined_scores[idx])
                if confidence > 0.01:  # Minimum threshold
                    result = {
                        'class_index': int(idx),
                        'confidence': confidence,
                        'scientific_name': self.class_names[idx] if idx < len(self.class_names) else f'Species_{idx}',
                        'fusion_method': fusion_method,
                        'organs_used': organs
                    }
                    fused_results.append(result)
            
            logger.debug(f"🔗 Multi-organ prediction fused using {fusion_method}")
            
            return fused_results
            
        except Exception as e:
            logger.error(f"❌ Multi-organ prediction failed: {e}")
            raise
    
    def get_performance_stats(self) -> Dict:
        """Get inference performance statistics"""
        if not self.inference_times:
            return {'message': 'No inference times recorded'}
        
        times = np.array(self.inference_times)
        
        return {
            'total_inferences': len(times),
            'average_time': float(np.mean(times)),
            'median_time': float(np.median(times)),
            'min_time': float(np.min(times)),
            'max_time': float(np.max(times)),
            'std_time': float(np.std(times)),
            'throughput_per_second': 1.0 / float(np.mean(times)) if np.mean(times) > 0 else 0
        }
    
    def warm_up(self, num_warmup: int = 5) -> None:
        """Warm up the model with dummy predictions"""
        logger.info(f"🔥 Warming up model with {num_warmup} dummy predictions...")
        
        dummy_image = np.random.rand(*self.image_size, 3).astype(np.float32)
        
        for _ in range(num_warmup):
            self.predict_single(dummy_image, 'leaf', top_k=1)
        
        # Clear warmup times from performance stats
        self.inference_times = []
        
        logger.info("✅ Model warmup completed")

if __name__ == '__main__':
    # Test inference engine (requires a trained model)
    try:
        engine = PlantInferenceEngine('models/trained_models/test_model.h5')
        print("✅ PlantInferenceEngine test successful!")
        print(f"Performance stats: {engine.get_performance_stats()}")
    except Exception as e:
        print(f"⚠️ Test requires a trained model: {e}")
