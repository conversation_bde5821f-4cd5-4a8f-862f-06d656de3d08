"""
Plant Dataset Loader and Management System
Handles loading, preprocessing, and augmentation of plant image datasets
"""

import os
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Tuple, List, Dict, Optional, Generator
import logging
from PIL import Image
import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import albumentations as A
import cv2

logger = logging.getLogger(__name__)

class PlantDataLoader:
    """
    Comprehensive data loader for plant identification datasets
    Supports organ-specific labeling and advanced augmentation
    """
    
    def __init__(self, 
                 data_dir: str,
                 image_size: Tuple[int, int] = (224, 224),
                 batch_size: int = 32,
                 validation_split: float = 0.2,
                 test_split: float = 0.1):
        """
        Initialize the plant data loader
        
        Args:
            data_dir: Root directory containing plant image data
            image_size: Target image size for model input
            batch_size: Batch size for training
            validation_split: Fraction of data for validation
            test_split: Fraction of data for testing
        """
        self.data_dir = Path(data_dir)
        self.image_size = image_size
        self.batch_size = batch_size
        self.validation_split = validation_split
        self.test_split = test_split
        
        # Data structures
        self.species_encoder = LabelEncoder()
        self.organ_encoder = LabelEncoder()
        self.dataset_info = {}
        self.class_names = []
        self.organ_names = []
        
        # Augmentation pipeline
        self.augmentation_pipeline = self._create_augmentation_pipeline()
        
        logger.info(f"✅ PlantDataLoader initialized for {data_dir}")
    
    def _create_augmentation_pipeline(self) -> A.Compose:
        """Create advanced augmentation pipeline using Albumentations"""
        return A.Compose([
            A.RandomRotate90(p=0.5),
            A.Flip(p=0.5),
            A.Transpose(p=0.5),
            A.OneOf([
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.5),
                A.GaussianBlur(blur_limit=3, p=0.5),
            ], p=0.3),
            A.OneOf([
                A.MotionBlur(blur_limit=3, p=0.5),
                A.MedianBlur(blur_limit=3, p=0.5),
            ], p=0.3),
            A.ShiftScaleRotate(
                shift_limit=0.0625, 
                scale_limit=0.1, 
                rotate_limit=15, 
                border_mode=cv2.BORDER_REFLECT_101, 
                p=0.8
            ),
            A.OneOf([
                A.OpticalDistortion(distort_limit=0.05, shift_limit=0.05, p=0.5),
                A.GridDistortion(num_steps=5, distort_limit=0.05, p=0.5),
                A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.5)
            ], p=0.3),
            A.OneOf([
                A.CLAHE(clip_limit=2, p=0.5),
                A.Sharpen(p=0.5),
                A.Emboss(p=0.5),
            ], p=0.3),
            A.HueSaturationValue(
                hue_shift_limit=20, 
                sat_shift_limit=30, 
                val_shift_limit=20, 
                p=0.5
            ),
            A.RandomBrightnessContrast(
                brightness_limit=0.2, 
                contrast_limit=0.2, 
                p=0.5
            ),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225],
                max_pixel_value=255.0,
                p=1.0
            )
        ])
    
    def scan_dataset(self) -> Dict:
        """
        Scan the dataset directory and build metadata
        Expected structure:
        data_dir/
        ├── species_1/
        │   ├── leaf/
        │   ├── flower/
        │   └── fruit/
        └── species_2/
            ├── leaf/
            └── flower/
        
        Returns:
            Dataset information dictionary
        """
        logger.info("🔍 Scanning dataset directory...")
        
        dataset_info = {
            'species': {},
            'total_images': 0,
            'species_count': 0,
            'organ_distribution': {},
            'images_per_species': {}
        }
        
        species_dirs = [d for d in self.data_dir.iterdir() if d.is_dir()]
        
        for species_dir in species_dirs:
            species_name = species_dir.name
            species_info = {
                'organs': {},
                'total_images': 0
            }
            
            # Scan organ directories within species
            organ_dirs = [d for d in species_dir.iterdir() if d.is_dir()]
            
            for organ_dir in organ_dirs:
                organ_name = organ_dir.name
                
                # Count images in organ directory
                image_files = []
                for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.webp']:
                    image_files.extend(organ_dir.glob(ext))
                    image_files.extend(organ_dir.glob(ext.upper()))
                
                image_count = len(image_files)
                
                if image_count > 0:
                    species_info['organs'][organ_name] = {
                        'image_count': image_count,
                        'image_paths': [str(p) for p in image_files]
                    }
                    species_info['total_images'] += image_count
                    
                    # Update organ distribution
                    if organ_name not in dataset_info['organ_distribution']:
                        dataset_info['organ_distribution'][organ_name] = 0
                    dataset_info['organ_distribution'][organ_name] += image_count
            
            if species_info['total_images'] > 0:
                dataset_info['species'][species_name] = species_info
                dataset_info['total_images'] += species_info['total_images']
                dataset_info['images_per_species'][species_name] = species_info['total_images']
        
        dataset_info['species_count'] = len(dataset_info['species'])
        
        # Store dataset info
        self.dataset_info = dataset_info
        self.class_names = list(dataset_info['species'].keys())
        self.organ_names = list(dataset_info['organ_distribution'].keys())
        
        # Fit encoders
        if self.class_names:
            self.species_encoder.fit(self.class_names)
        if self.organ_names:
            self.organ_encoder.fit(self.organ_names)
        
        logger.info(f"✅ Dataset scan complete:")
        logger.info(f"   📊 Species: {dataset_info['species_count']}")
        logger.info(f"   🖼️ Total images: {dataset_info['total_images']}")
        logger.info(f"   🌿 Organs: {list(dataset_info['organ_distribution'].keys())}")
        
        return dataset_info
    
    def create_dataframe(self) -> pd.DataFrame:
        """
        Create a pandas DataFrame with all image information
        
        Returns:
            DataFrame with columns: image_path, species, organ, species_encoded, organ_encoded
        """
        if not self.dataset_info:
            self.scan_dataset()
        
        data_rows = []
        
        for species_name, species_info in self.dataset_info['species'].items():
            for organ_name, organ_info in species_info['organs'].items():
                for image_path in organ_info['image_paths']:
                    data_rows.append({
                        'image_path': image_path,
                        'species': species_name,
                        'organ': organ_name,
                        'species_encoded': self.species_encoder.transform([species_name])[0],
                        'organ_encoded': self.organ_encoder.transform([organ_name])[0]
                    })
        
        df = pd.DataFrame(data_rows)
        logger.info(f"✅ Created DataFrame with {len(df)} samples")
        
        return df
    
    def split_dataset(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Split dataset into train, validation, and test sets
        
        Args:
            df: DataFrame with image information
            
        Returns:
            Tuple of (train_df, val_df, test_df)
        """
        # Stratified split by species to ensure balanced representation
        train_df, temp_df = train_test_split(
            df, 
            test_size=(self.validation_split + self.test_split),
            stratify=df['species'],
            random_state=42
        )
        
        # Split temp into validation and test
        val_size = self.validation_split / (self.validation_split + self.test_split)
        val_df, test_df = train_test_split(
            temp_df,
            test_size=(1 - val_size),
            stratify=temp_df['species'],
            random_state=42
        )
        
        logger.info(f"✅ Dataset split:")
        logger.info(f"   🚂 Train: {len(train_df)} samples")
        logger.info(f"   ✅ Validation: {len(val_df)} samples")
        logger.info(f"   🧪 Test: {len(test_df)} samples")
        
        return train_df, val_df, test_df
    
    def load_and_preprocess_image(self, image_path: str, augment: bool = False) -> np.ndarray:
        """
        Load and preprocess a single image
        
        Args:
            image_path: Path to the image file
            augment: Whether to apply augmentation
            
        Returns:
            Preprocessed image array
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Resize image
            image = cv2.resize(image, self.image_size)
            
            # Apply augmentation if requested
            if augment:
                augmented = self.augmentation_pipeline(image=image)
                image = augmented['image']
            else:
                # Just normalize without augmentation
                image = image.astype(np.float32) / 255.0
                # Apply ImageNet normalization
                mean = np.array([0.485, 0.456, 0.406])
                std = np.array([0.229, 0.224, 0.225])
                image = (image - mean) / std
            
            return image
            
        except Exception as e:
            logger.error(f"❌ Failed to load image {image_path}: {e}")
            # Return a black image as fallback
            return np.zeros((*self.image_size, 3), dtype=np.float32)
    
    def create_tf_dataset(self, df: pd.DataFrame, augment: bool = False) -> tf.data.Dataset:
        """
        Create a TensorFlow dataset from DataFrame
        
        Args:
            df: DataFrame with image information
            augment: Whether to apply augmentation
            
        Returns:
            TensorFlow dataset
        """
        def load_image_and_labels(image_path, species_encoded, organ_encoded):
            # Load and preprocess image
            image = tf.py_function(
                func=lambda path: self.load_and_preprocess_image(path.numpy().decode('utf-8'), augment),
                inp=[image_path],
                Tout=tf.float32
            )
            image.set_shape((*self.image_size, 3))
            
            # Create one-hot encodings
            species_onehot = tf.one_hot(species_encoded, len(self.class_names))
            organ_onehot = tf.one_hot(organ_encoded, len(self.organ_names))
            
            return (image, organ_onehot), species_onehot
        
        # Create dataset from DataFrame
        dataset = tf.data.Dataset.from_tensor_slices({
            'image_path': df['image_path'].values,
            'species_encoded': df['species_encoded'].values,
            'organ_encoded': df['organ_encoded'].values
        })
        
        # Map the loading function
        dataset = dataset.map(
            lambda x: load_image_and_labels(x['image_path'], x['species_encoded'], x['organ_encoded']),
            num_parallel_calls=tf.data.AUTOTUNE
        )
        
        # Batch and prefetch
        dataset = dataset.batch(self.batch_size)
        dataset = dataset.prefetch(tf.data.AUTOTUNE)
        
        return dataset
    
    def get_class_weights(self, df: pd.DataFrame) -> Dict[int, float]:
        """
        Calculate class weights for handling imbalanced datasets
        
        Args:
            df: DataFrame with image information
            
        Returns:
            Dictionary mapping class indices to weights
        """
        from sklearn.utils.class_weight import compute_class_weight
        
        class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(df['species_encoded']),
            y=df['species_encoded']
        )
        
        return {i: weight for i, weight in enumerate(class_weights)}
    
    def save_encoders(self, filepath: str) -> None:
        """Save label encoders for later use"""
        encoders = {
            'species_classes': self.species_encoder.classes_.tolist(),
            'organ_classes': self.organ_encoder.classes_.tolist()
        }
        
        with open(filepath, 'w') as f:
            json.dump(encoders, f, indent=2)
        
        logger.info(f"✅ Encoders saved to {filepath}")
    
    def load_encoders(self, filepath: str) -> None:
        """Load label encoders from file"""
        with open(filepath, 'r') as f:
            encoders = json.load(f)
        
        self.species_encoder.classes_ = np.array(encoders['species_classes'])
        self.organ_encoder.classes_ = np.array(encoders['organ_classes'])
        self.class_names = encoders['species_classes']
        self.organ_names = encoders['organ_classes']
        
        logger.info(f"✅ Encoders loaded from {filepath}")

if __name__ == '__main__':
    # Test data loader
    data_loader = PlantDataLoader('data/raw')
    dataset_info = data_loader.scan_dataset()
    print(f"✅ Found {dataset_info['species_count']} species with {dataset_info['total_images']} images")
