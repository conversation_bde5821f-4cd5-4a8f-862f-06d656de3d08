"""
AI Dataset Generator for Medicinal Plant Recognition
Generates synthetic images using AI and applies comprehensive augmentations
"""

import os
import sys
import yaml
import random
import logging
import requests
import numpy as np
from pathlib import Path
from datetime import datetime
import json
import time
from typing import List, Dict, Tuple
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AIDatasetGenerator:
    """AI-powered dataset generator for medicinal plants"""
    
    def __init__(self, data_yaml_path: str = "data.yaml"):
        self.data_yaml_path = data_yaml_path
        self.load_dataset_config()
        self.setup_directories()
        self.generated_count = 0
        self.augmented_count = 0
        
    def load_dataset_config(self):
        """Load dataset configuration from data.yaml"""
        try:
            with open(self.data_yaml_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            self.num_classes = self.config['nc']
            self.class_names = self.config['names']
            
            logger.info(f"Loaded dataset config: {self.num_classes} classes")
            logger.info(f"Classes: {list(self.class_names.values())[:10]}...")
            
        except Exception as e:
            logger.error(f"Failed to load dataset config: {e}")
            raise
    
    def setup_directories(self):
        """Setup directories for generated images"""
        self.base_dir = Path(".")
        self.generated_dir = self.base_dir / "generated_images"
        self.augmented_dir = self.base_dir / "augmented_images"
        
        # Create directories
        for class_name in self.class_names.values():
            if class_name == '-':  # Skip empty class
                continue
                
            class_dir = self.generated_dir / class_name
            class_dir.mkdir(parents=True, exist_ok=True)
            
            aug_dir = self.augmented_dir / class_name
            aug_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Created directories for {len(self.class_names)} classes")
    
    def generate_ai_images_stable_diffusion(self, class_name: str, num_images: int = 15) -> List[str]:
        """Generate images using Stable Diffusion API (Hugging Face)"""
        if class_name == '-':
            return []
        
        logger.info(f"Generating {num_images} AI images for {class_name}")
        
        # Hugging Face Inference API
        API_URL = "https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5"
        
        # You'll need to get a free API key from Hugging Face
        headers = {
            "Authorization": "Bearer YOUR_HUGGING_FACE_API_KEY_HERE"
        }
        
        generated_files = []
        
        # Create detailed prompts for medicinal plants
        prompts = self.create_plant_prompts(class_name, num_images)
        
        for i, prompt in enumerate(prompts):
            try:
                # Add delay to respect API limits
                if i > 0:
                    time.sleep(2)
                
                payload = {
                    "inputs": prompt,
                    "parameters": {
                        "num_inference_steps": 50,
                        "guidance_scale": 7.5,
                        "width": 512,
                        "height": 512
                    }
                }
                
                response = requests.post(API_URL, headers=headers, json=payload)
                
                if response.status_code == 200:
                    # Save generated image
                    filename = f"{class_name}_ai_generated_{i+1:03d}.jpg"
                    filepath = self.generated_dir / class_name / filename
                    
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    generated_files.append(str(filepath))
                    self.generated_count += 1
                    
                    logger.info(f"Generated: {filename}")
                    
                else:
                    logger.warning(f"API request failed: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error generating image {i+1}: {e}")
                continue
        
        return generated_files
    
    def create_plant_prompts(self, class_name: str, num_prompts: int) -> List[str]:
        """Create detailed prompts for plant image generation"""
        
        # Base prompt templates
        base_prompts = [
            f"High quality photograph of {class_name} medicinal plant, detailed leaves, natural lighting, botanical photography",
            f"Professional botanical illustration of {class_name} plant, scientific accuracy, clear details, white background",
            f"Close-up macro photography of {class_name} medicinal herb, sharp focus, natural environment",
            f"{class_name} plant in natural habitat, outdoor photography, good lighting, medicinal herb",
            f"Detailed view of {class_name} medicinal plant leaves and stems, botanical specimen, high resolution",
            f"Traditional medicinal plant {class_name}, ayurvedic herb, natural photography, clear details",
            f"{class_name} plant growing in garden, medicinal herb cultivation, natural lighting",
            f"Scientific botanical photograph of {class_name}, medicinal plant identification, clear features",
            f"Fresh {class_name} medicinal herb, detailed plant structure, professional photography",
            f"{class_name} plant specimen, botanical documentation, high quality image, natural colors"
        ]
        
        # Style variations
        styles = [
            ", photorealistic, 4k resolution",
            ", botanical art style, detailed illustration",
            ", macro photography, shallow depth of field",
            ", natural lighting, outdoor setting",
            ", studio photography, clean background",
            ", scientific documentation style",
            ", traditional herbal medicine context",
            ", garden photography, natural environment",
            ", close-up detail, sharp focus",
            ", professional botanical photography"
        ]
        
        # Quality enhancers
        quality_terms = [
            ", high quality, detailed, sharp",
            ", professional photography, well lit",
            ", botanical accuracy, clear details",
            ", natural colors, realistic",
            ", high resolution, crisp image",
            ", scientific quality, precise",
            ", medicinal plant identification",
            ", herbal medicine, traditional",
            ", plant taxonomy, botanical",
            ", nature photography, authentic"
        ]
        
        prompts = []
        for i in range(num_prompts):
            base = random.choice(base_prompts)
            style = random.choice(styles)
            quality = random.choice(quality_terms)
            
            prompt = base + style + quality
            prompts.append(prompt)
        
        return prompts
    
    def generate_ai_images_local(self, class_name: str, num_images: int = 15) -> List[str]:
        """Generate images using local AI models (fallback method)"""
        if class_name == '-':
            return []
        
        logger.info(f"Using local generation for {class_name} (fallback method)")
        
        # This is a placeholder for local AI generation
        # You can integrate with local Stable Diffusion, DALL-E mini, etc.
        
        generated_files = []
        
        try:
            # Import local generation libraries
            from PIL import Image, ImageDraw, ImageFont
            import random
            
            for i in range(num_images):
                # Create a synthetic plant-like image (placeholder)
                img = self.create_synthetic_plant_image(class_name)
                
                filename = f"{class_name}_synthetic_{i+1:03d}.jpg"
                filepath = self.generated_dir / class_name / filename
                
                img.save(filepath, 'JPEG', quality=95)
                generated_files.append(str(filepath))
                self.generated_count += 1
                
                logger.info(f"Generated synthetic: {filename}")
                
        except Exception as e:
            logger.error(f"Local generation failed: {e}")
        
        return generated_files
    
    def create_synthetic_plant_image(self, class_name: str) -> 'Image':
        """Create synthetic plant-like images (basic implementation)"""
        from PIL import Image, ImageDraw
        import random
        
        # Create base image
        width, height = 512, 512
        img = Image.new('RGB', (width, height), color=(240, 248, 240))  # Light green background
        draw = ImageDraw.Draw(img)
        
        # Generate plant-like patterns
        colors = [
            (34, 139, 34),   # Forest green
            (50, 205, 50),   # Lime green
            (0, 128, 0),     # Green
            (107, 142, 35),  # Olive drab
            (85, 107, 47),   # Dark olive green
        ]
        
        # Draw stem
        stem_color = (101, 67, 33)  # Brown
        stem_x = width // 2
        draw.line([(stem_x, height), (stem_x, height // 3)], fill=stem_color, width=8)
        
        # Draw leaves
        num_leaves = random.randint(5, 12)
        for i in range(num_leaves):
            leaf_color = random.choice(colors)
            
            # Leaf position
            x = stem_x + random.randint(-100, 100)
            y = random.randint(height // 4, height - 50)
            
            # Leaf shape (ellipse)
            leaf_width = random.randint(30, 80)
            leaf_height = random.randint(20, 60)
            
            draw.ellipse([
                x - leaf_width//2, y - leaf_height//2,
                x + leaf_width//2, y + leaf_height//2
            ], fill=leaf_color)
            
            # Leaf veins
            vein_color = tuple(max(0, c - 30) for c in leaf_color)
            draw.line([(x, y - leaf_height//2), (x, y + leaf_height//2)], 
                     fill=vein_color, width=2)
        
        # Add some texture/noise
        for _ in range(100):
            x = random.randint(0, width)
            y = random.randint(0, height)
            noise_color = (random.randint(200, 255), random.randint(240, 255), random.randint(200, 255))
            draw.point((x, y), fill=noise_color)
        
        return img
    
    def apply_comprehensive_augmentations(self, image_path: str, class_name: str, num_augmentations: int = 8) -> List[str]:
        """Apply comprehensive augmentations to increase dataset diversity"""
        try:
            from PIL import Image, ImageEnhance, ImageFilter, ImageOps
            import random
            
            # Load original image
            original_img = Image.open(image_path)
            augmented_files = []
            
            # Define augmentation techniques
            augmentations = [
                self.rotate_image,
                self.flip_image,
                self.adjust_brightness,
                self.adjust_contrast,
                self.adjust_saturation,
                self.add_blur,
                self.add_noise,
                self.crop_and_resize,
                self.adjust_hue,
                self.add_gaussian_blur,
                self.adjust_sharpness,
                self.color_jitter
            ]
            
            # Apply random augmentations
            for i in range(num_augmentations):
                try:
                    # Choose random augmentation
                    aug_func = random.choice(augmentations)
                    augmented_img = aug_func(original_img.copy())
                    
                    # Save augmented image
                    base_name = Path(image_path).stem
                    filename = f"{base_name}_aug_{i+1:02d}.jpg"
                    filepath = self.augmented_dir / class_name / filename
                    
                    augmented_img.save(filepath, 'JPEG', quality=95)
                    augmented_files.append(str(filepath))
                    self.augmented_count += 1
                    
                except Exception as e:
                    logger.warning(f"Augmentation {i+1} failed: {e}")
                    continue
            
            return augmented_files
            
        except Exception as e:
            logger.error(f"Augmentation failed for {image_path}: {e}")
            return []
    
    def rotate_image(self, img: 'Image') -> 'Image':
        """Rotate image by random angle"""
        angle = random.uniform(-30, 30)
        return img.rotate(angle, expand=True, fillcolor=(255, 255, 255))
    
    def flip_image(self, img: 'Image') -> 'Image':
        """Flip image horizontally or vertically"""
        if random.choice([True, False]):
            return img.transpose(Image.FLIP_LEFT_RIGHT)
        else:
            return img.transpose(Image.FLIP_TOP_BOTTOM)
    
    def adjust_brightness(self, img: 'Image') -> 'Image':
        """Adjust image brightness"""
        from PIL import ImageEnhance
        factor = random.uniform(0.7, 1.3)
        enhancer = ImageEnhance.Brightness(img)
        return enhancer.enhance(factor)
    
    def adjust_contrast(self, img: 'Image') -> 'Image':
        """Adjust image contrast"""
        from PIL import ImageEnhance
        factor = random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Contrast(img)
        return enhancer.enhance(factor)
    
    def adjust_saturation(self, img: 'Image') -> 'Image':
        """Adjust color saturation"""
        from PIL import ImageEnhance
        factor = random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Color(img)
        return enhancer.enhance(factor)
    
    def add_blur(self, img: 'Image') -> 'Image':
        """Add slight blur"""
        from PIL import ImageFilter
        radius = random.uniform(0.5, 2.0)
        return img.filter(ImageFilter.GaussianBlur(radius=radius))
    
    def add_noise(self, img: 'Image') -> 'Image':
        """Add random noise"""
        import numpy as np
        
        # Convert to numpy array
        img_array = np.array(img)
        
        # Add noise
        noise = np.random.normal(0, 10, img_array.shape)
        noisy_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
        
        return Image.fromarray(noisy_array)
    
    def crop_and_resize(self, img: 'Image') -> 'Image':
        """Random crop and resize"""
        width, height = img.size
        
        # Random crop
        crop_factor = random.uniform(0.8, 0.95)
        new_width = int(width * crop_factor)
        new_height = int(height * crop_factor)
        
        left = random.randint(0, width - new_width)
        top = random.randint(0, height - new_height)
        
        cropped = img.crop((left, top, left + new_width, top + new_height))
        
        # Resize back to original size
        return cropped.resize((width, height), Image.LANCZOS)
    
    def adjust_hue(self, img: 'Image') -> 'Image':
        """Adjust hue"""
        import colorsys
        import numpy as np
        
        # Convert to HSV and adjust hue
        img_array = np.array(img)
        hsv = np.array([colorsys.rgb_to_hsv(r/255, g/255, b/255) 
                       for r, g, b in img_array.reshape(-1, 3)])
        
        # Adjust hue
        hue_shift = random.uniform(-0.1, 0.1)
        hsv[:, 0] = (hsv[:, 0] + hue_shift) % 1.0
        
        # Convert back to RGB
        rgb = np.array([colorsys.hsv_to_rgb(h, s, v) for h, s, v in hsv])
        rgb_array = (rgb * 255).astype(np.uint8).reshape(img_array.shape)
        
        return Image.fromarray(rgb_array)
    
    def add_gaussian_blur(self, img: 'Image') -> 'Image':
        """Add Gaussian blur"""
        from PIL import ImageFilter
        radius = random.uniform(0.3, 1.5)
        return img.filter(ImageFilter.GaussianBlur(radius=radius))
    
    def adjust_sharpness(self, img: 'Image') -> 'Image':
        """Adjust image sharpness"""
        from PIL import ImageEnhance
        factor = random.uniform(0.8, 1.5)
        enhancer = ImageEnhance.Sharpness(img)
        return enhancer.enhance(factor)
    
    def color_jitter(self, img: 'Image') -> 'Image':
        """Apply color jittering"""
        from PIL import ImageEnhance
        
        # Random adjustments
        brightness = random.uniform(0.9, 1.1)
        contrast = random.uniform(0.9, 1.1)
        saturation = random.uniform(0.9, 1.1)
        
        # Apply adjustments
        img = ImageEnhance.Brightness(img).enhance(brightness)
        img = ImageEnhance.Contrast(img).enhance(contrast)
        img = ImageEnhance.Color(img).enhance(saturation)

        return img

    def process_existing_images(self, class_name: str, max_per_class: int = 50) -> List[str]:
        """Process existing images and apply augmentations"""
        if class_name == '-':
            return []

        # Find existing images
        train_dir = Path("train/images")
        existing_images = []

        # Look for images of this class
        for img_file in train_dir.glob("*.jpg"):
            if class_name.lower() in img_file.name.lower():
                existing_images.append(str(img_file))

        logger.info(f"Found {len(existing_images)} existing images for {class_name}")

        # Limit number of images to process
        if len(existing_images) > max_per_class:
            existing_images = random.sample(existing_images, max_per_class)

        # Apply augmentations to existing images
        all_augmented = []
        for img_path in existing_images:
            augmented = self.apply_comprehensive_augmentations(img_path, class_name, num_augmentations=5)
            all_augmented.extend(augmented)

        return all_augmented

    def generate_complete_dataset(self, images_per_class: int = 15, augmentations_per_image: int = 8):
        """Generate complete dataset with AI images and augmentations"""
        logger.info("🚀 Starting complete dataset generation")
        logger.info(f"Target: {images_per_class} AI images + augmentations per class")

        start_time = datetime.now()

        # Process each class
        for class_id, class_name in self.class_names.items():
            if class_name == '-':
                continue

            logger.info(f"\n📸 Processing class: {class_name} (ID: {class_id})")

            try:
                # Step 1: Generate AI images
                logger.info(f"🤖 Generating {images_per_class} AI images...")
                ai_images = self.generate_ai_images_local(class_name, images_per_class)

                # Step 2: Apply augmentations to AI images
                logger.info(f"🔄 Applying augmentations to AI images...")
                for ai_img in ai_images:
                    self.apply_comprehensive_augmentations(ai_img, class_name, augmentations_per_image)

                # Step 3: Process existing images
                logger.info(f"📁 Processing existing images...")
                self.process_existing_images(class_name, max_per_class=20)

                logger.info(f"✅ Completed {class_name}")

            except Exception as e:
                logger.error(f"❌ Failed to process {class_name}: {e}")
                continue

        end_time = datetime.now()
        duration = end_time - start_time

        logger.info(f"\n🎉 Dataset generation completed!")
        logger.info(f"⏱️ Total time: {duration}")
        logger.info(f"📊 Generated images: {self.generated_count}")
        logger.info(f"🔄 Augmented images: {self.augmented_count}")
        logger.info(f"📈 Total new images: {self.generated_count + self.augmented_count}")

    def create_training_dataset(self, output_dir: str = "enhanced_dataset"):
        """Create enhanced training dataset combining original + generated + augmented"""
        logger.info("📦 Creating enhanced training dataset")

        output_path = Path(output_dir)
        train_images_dir = output_path / "train" / "images"
        train_labels_dir = output_path / "train" / "labels"

        # Create directories
        train_images_dir.mkdir(parents=True, exist_ok=True)
        train_labels_dir.mkdir(parents=True, exist_ok=True)

        total_images = 0

        for class_id, class_name in self.class_names.items():
            if class_name == '-':
                continue

            logger.info(f"Processing {class_name}...")

            # Copy original images
            original_dir = Path("train/images")
            for img_file in original_dir.glob("*.jpg"):
                if class_name.lower() in img_file.name.lower():
                    # Copy image
                    dest_img = train_images_dir / img_file.name
                    dest_img.write_bytes(img_file.read_bytes())

                    # Copy corresponding label if exists
                    label_file = Path("train/labels") / (img_file.stem + ".txt")
                    if label_file.exists():
                        dest_label = train_labels_dir / label_file.name
                        dest_label.write_text(label_file.read_text())
                    else:
                        # Create basic label file
                        dest_label = train_labels_dir / (img_file.stem + ".txt")
                        # YOLO format: class_id x_center y_center width height
                        dest_label.write_text(f"{class_id} 0.5 0.5 0.8 0.8\n")

                    total_images += 1

            # Copy generated images
            gen_dir = self.generated_dir / class_name
            if gen_dir.exists():
                for img_file in gen_dir.glob("*.jpg"):
                    # Copy image
                    dest_img = train_images_dir / img_file.name
                    dest_img.write_bytes(img_file.read_bytes())

                    # Create label file
                    dest_label = train_labels_dir / (img_file.stem + ".txt")
                    dest_label.write_text(f"{class_id} 0.5 0.5 0.8 0.8\n")

                    total_images += 1

            # Copy augmented images
            aug_dir = self.augmented_dir / class_name
            if aug_dir.exists():
                for img_file in aug_dir.glob("*.jpg"):
                    # Copy image
                    dest_img = train_images_dir / img_file.name
                    dest_img.write_bytes(img_file.read_bytes())

                    # Create label file
                    dest_label = train_labels_dir / (img_file.stem + ".txt")
                    dest_label.write_text(f"{class_id} 0.5 0.5 0.8 0.8\n")

                    total_images += 1

        # Create enhanced data.yaml
        enhanced_config = self.config.copy()
        enhanced_config['train'] = f"{output_dir}/train/images"
        enhanced_config['val'] = f"{output_dir}/valid/images"
        enhanced_config['test'] = f"{output_dir}/test/images"

        with open(output_path / "data.yaml", 'w') as f:
            yaml.dump(enhanced_config, f, default_flow_style=False)

        logger.info(f"✅ Enhanced dataset created with {total_images} images")
        logger.info(f"📁 Location: {output_path}")

        return str(output_path)

    def generate_statistics_report(self):
        """Generate detailed statistics report"""
        logger.info("📊 Generating statistics report")

        stats = {
            "generation_date": datetime.now().isoformat(),
            "total_classes": len([name for name in self.class_names.values() if name != '-']),
            "generated_images": self.generated_count,
            "augmented_images": self.augmented_count,
            "total_new_images": self.generated_count + self.augmented_count,
            "classes": {}
        }

        # Count images per class
        for class_name in self.class_names.values():
            if class_name == '-':
                continue

            class_stats = {
                "original_images": 0,
                "generated_images": 0,
                "augmented_images": 0
            }

            # Count original images
            train_dir = Path("train/images")
            for img_file in train_dir.glob("*.jpg"):
                if class_name.lower() in img_file.name.lower():
                    class_stats["original_images"] += 1

            # Count generated images
            gen_dir = self.generated_dir / class_name
            if gen_dir.exists():
                class_stats["generated_images"] = len(list(gen_dir.glob("*.jpg")))

            # Count augmented images
            aug_dir = self.augmented_dir / class_name
            if aug_dir.exists():
                class_stats["augmented_images"] = len(list(aug_dir.glob("*.jpg")))

            class_stats["total_images"] = sum(class_stats.values())
            stats["classes"][class_name] = class_stats

        # Save statistics
        stats_file = Path("dataset_generation_stats.json")
        with open(stats_file, 'w') as f:
            json.dump(stats, f, indent=2)

        logger.info(f"📈 Statistics saved to: {stats_file}")

        # Print summary
        print("\n" + "="*60)
        print("📊 DATASET GENERATION SUMMARY")
        print("="*60)
        print(f"🏷️ Total Classes: {stats['total_classes']}")
        print(f"🤖 Generated Images: {stats['generated_images']}")
        print(f"🔄 Augmented Images: {stats['augmented_images']}")
        print(f"📈 Total New Images: {stats['total_new_images']}")
        print("\n📋 Top 10 Classes by Total Images:")

        # Sort classes by total images
        sorted_classes = sorted(
            stats["classes"].items(),
            key=lambda x: x[1]["total_images"],
            reverse=True
        )

        for i, (class_name, class_stats) in enumerate(sorted_classes[:10]):
            print(f"{i+1:2d}. {class_name:20s} - {class_stats['total_images']:3d} images")

        print("="*60)

        return stats


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='AI Dataset Generator for Medicinal Plants')
    parser.add_argument('--data', type=str, default='data.yaml', help='Path to data.yaml file')
    parser.add_argument('--images-per-class', type=int, default=15, help='Number of AI images to generate per class')
    parser.add_argument('--augmentations', type=int, default=8, help='Number of augmentations per image')
    parser.add_argument('--output-dir', type=str, default='enhanced_dataset', help='Output directory for enhanced dataset')
    parser.add_argument('--stats-only', action='store_true', help='Generate statistics report only')

    args = parser.parse_args()

    print("🌿" * 30)
    print("🌿 AI DATASET GENERATOR FOR MEDICINAL PLANTS 🌿")
    print("🌿" * 30)
    print()

    try:
        # Initialize generator
        generator = AIDatasetGenerator(args.data)

        if args.stats_only:
            # Generate statistics only
            generator.generate_statistics_report()
        else:
            # Full dataset generation
            generator.generate_complete_dataset(
                images_per_class=args.images_per_class,
                augmentations_per_image=args.augmentations
            )

            # Create enhanced dataset
            enhanced_path = generator.create_training_dataset(args.output_dir)

            # Generate statistics
            generator.generate_statistics_report()

            print(f"\n🎉 Dataset generation completed!")
            print(f"📁 Enhanced dataset: {enhanced_path}")
            print(f"📊 Statistics: dataset_generation_stats.json")

    except Exception as e:
        logger.error(f"❌ Dataset generation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
