#!/usr/bin/env python3
"""
Test script to check backend functionality
"""

import sys
import os
import traceback

# Add backend to path
sys.path.append('medicinal-plant-detection/backend')

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        import flask
        print("✅ Flask imported successfully")
    except Exception as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        from flask_cors import CORS
        print("✅ Flask-CORS imported successfully")
    except Exception as e:
        print(f"❌ Flask-CORS import failed: {e}")
        return False
    
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics imported successfully")
    except Exception as e:
        print(f"❌ Ultralytics import failed: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV imported successfully")
    except Exception as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL imported successfully")
    except Exception as e:
        print(f"❌ PIL import failed: {e}")
        return False
    
    try:
        import pymongo
        print("✅ PyMongo imported successfully")
    except Exception as e:
        print(f"❌ PyMongo import failed: {e}")
        return False
    
    return True

def test_database():
    """Test database connection"""
    print("\nTesting database connection...")
    
    try:
        from database import get_db
        db = get_db()
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        traceback.print_exc()
        return False

def test_model():
    """Test model loading"""
    print("\nTesting model loading...")
    
    try:
        from ultralytics import YOLO
        model = YOLO('yolov8n.pt')
        print("✅ Model loaded successfully")
        return True
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Test Flask app creation"""
    print("\nTesting Flask app creation...")
    
    try:
        # Change to backend directory
        os.chdir('medicinal-plant-detection/backend')
        
        # Import app module
        import app
        print("✅ App module imported successfully")
        
        # Check if app is created
        if hasattr(app, 'app'):
            print("✅ Flask app created successfully")
            return True
        else:
            print("❌ Flask app not found in module")
            return False
            
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Backend Testing Script")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Please install missing dependencies.")
        return
    
    # Test database
    if not test_database():
        print("\n❌ Database tests failed. Check MongoDB connection.")
        return
    
    # Test model
    if not test_model():
        print("\n❌ Model tests failed. Check YOLO installation.")
        return
    
    # Test app creation
    if not test_app_creation():
        print("\n❌ App creation tests failed.")
        return
    
    print("\n🎉 All tests passed! Backend should work correctly.")
    print("\nTo start the backend manually:")
    print("cd medicinal-plant-detection/backend")
    print("python app.py")

if __name__ == "__main__":
    main()
