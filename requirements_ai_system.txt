# 🌱 AI Medicinal Plant Recognition System Requirements

# Core ML and Computer Vision
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0
opencv-python>=4.8.0
albumentations>=1.3.0
Pillow>=10.0.0

# AI and Generation
google-generativeai>=0.3.0
replicate>=0.15.0
diffusers>=0.21.0
transformers>=4.35.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
scipy>=1.11.0

# Web Framework
flask>=2.3.0
flask-cors>=4.0.0
werkzeug>=2.3.0

# Configuration and Utilities
pyyaml>=6.0.0
python-dotenv>=1.0.0
requests>=2.31.0
tqdm>=4.65.0

# Image Processing
scikit-image>=0.21.0
imageio>=2.31.0

# Optional: Advanced Features
# fastapi>=0.103.0      # Alternative to Flask
# uvicorn>=0.23.0       # ASGI server
# celery>=5.3.0         # Background tasks
# redis>=4.6.0          # Caching
