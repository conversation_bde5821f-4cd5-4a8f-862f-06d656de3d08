#!/usr/bin/env python3
"""
🌱 Simple AI Medicinal Plant Recognition Demo
Lightweight demo without complex logging
"""

import os
import json
import yaml
import random
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import shutil

# Basic libraries
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter

# Web framework
from flask import Flask, request, jsonify, render_template_string
from werkzeug.utils import secure_filename

class SimplePlantAI:
    """Simple AI Plant Recognition System"""
    
    def __init__(self):
        """Initialize the system"""
        self.setup_directories()
        self.known_plants = self.load_known_plants()
        print("🌱 Simple Plant AI initialized successfully")
    
    def setup_directories(self):
        """Create necessary directories"""
        directories = ['new_plants', 'uploads', 'generated_images', 'logs']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def load_known_plants(self) -> Dict:
        """Load known plants database"""
        return {
            "aloe_vera": {
                "common_name": "<PERSON><PERSON> Vera",
                "scientific_name": "Aloe vera",
                "local_name": "Ghritkumari",
                "family": "Asphodelaceae",
                "medicinal_uses": ["Skin healing", "Digestive health", "Anti-inflammatory"],
                "description": "Succulent plant known for its healing gel"
            },
            "tulsi": {
                "common_name": "Holy Basil",
                "scientific_name": "Ocimum tenuiflorum",
                "local_name": "Tulsi",
                "family": "Lamiaceae",
                "medicinal_uses": ["Respiratory health", "Stress relief", "Immunity booster"],
                "description": "Sacred plant with numerous health benefits"
            },
            "neem": {
                "common_name": "Neem",
                "scientific_name": "Azadirachta indica",
                "local_name": "Neem",
                "family": "Meliaceae",
                "medicinal_uses": ["Antibacterial", "Antifungal", "Skin conditions"],
                "description": "Versatile medicinal tree with antimicrobial properties"
            }
        }
    
    def detect_known_plant(self, image_path: str) -> Tuple[bool, Dict]:
        """Simulate plant detection"""
        print(f"🔍 Analyzing image: {image_path}")
        time.sleep(random.uniform(0.5, 1.5))
        
        # 60% chance of detecting a known plant
        if random.random() < 0.6:
            plant_key = random.choice(list(self.known_plants.keys()))
            plant_info = self.known_plants[plant_key]
            confidence = random.uniform(0.75, 0.95)
            
            print(f"✅ Detected: {plant_info['common_name']} ({confidence:.2f})")
            return True, {
                "plant_info": plant_info,
                "confidence": confidence
            }
        else:
            print("❓ Unknown plant - will generate new information")
            return False, {}
    
    def generate_plant_info(self, image_path: str) -> Dict:
        """Generate new plant information"""
        print(f"🧠 Generating plant information for {image_path}")
        time.sleep(random.uniform(2.0, 3.0))
        
        plant_names = ["Ashwagandha", "Brahmi", "Giloy", "Amla", "Fenugreek"]
        families = ["Solanaceae", "Plantaginaceae", "Menispermaceae", "Phyllanthaceae", "Fabaceae"]
        
        idx = random.randint(0, len(plant_names) - 1)
        
        plant_info = {
            "common_name": plant_names[idx],
            "scientific_name": f"Generated {plant_names[idx].lower()}",
            "local_name": plant_names[idx],
            "family": families[idx],
            "medicinal_uses": ["Stress relief", "Immunity booster", "Digestive health"],
            "description": f"AI-generated medicinal plant information for {plant_names[idx]}",
            "discovery_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        print(f"✅ Generated info for: {plant_info['common_name']}")
        return plant_info
    
    def create_synthetic_images(self, plant_info: Dict, num_images: int = 10) -> List[str]:
        """Create synthetic placeholder images"""
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        print(f"🎨 Creating {num_images} synthetic images for {plant_name}")
        
        gen_dir = Path('generated_images') / plant_name
        gen_dir.mkdir(parents=True, exist_ok=True)
        
        generated_paths = []
        
        for i in range(num_images):
            # Create colored placeholder image
            img_size = (224, 224)
            green_colors = [(34, 139, 34), (50, 205, 50), (107, 142, 35)]
            color = random.choice(green_colors)
            
            img = Image.new('RGB', img_size, color)
            
            # Add some variation
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(random.uniform(0.8, 1.2))
            
            img_path = gen_dir / f"synthetic_{i:03d}.jpg"
            img.save(img_path, 'JPEG', quality=85)
            generated_paths.append(str(img_path))
            
            time.sleep(0.05)  # Simulate generation time
        
        print(f"✅ Created {len(generated_paths)} synthetic images")
        return generated_paths
    
    def create_dataset(self, plant_info: Dict, synthetic_images: List[str]) -> str:
        """Create augmented dataset"""
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        print(f"📈 Creating dataset for {plant_name}")
        
        dataset_dir = Path('new_plants') / plant_name
        dataset_dir.mkdir(parents=True, exist_ok=True)
        
        # Save plant info
        info_path = dataset_dir / "plant_info.json"
        with open(info_path, 'w') as f:
            json.dump(plant_info, f, indent=2)
        
        # Copy and augment images
        total_images = 0
        target_images = 30  # Reduced for demo
        
        # Copy originals
        for i, img_path in enumerate(synthetic_images):
            if os.path.exists(img_path):
                dest_path = dataset_dir / f"original_{i:03d}.jpg"
                shutil.copy2(img_path, dest_path)
                total_images += 1
        
        # Create augmented versions
        while total_images < target_images:
            for img_path in synthetic_images:
                if total_images >= target_images:
                    break
                
                try:
                    img = Image.open(img_path)
                    
                    # Simple augmentations
                    if random.random() > 0.5:
                        img = img.transpose(Image.FLIP_LEFT_RIGHT)
                    
                    if random.random() > 0.5:
                        enhancer = ImageEnhance.Brightness(img)
                        img = enhancer.enhance(random.uniform(0.7, 1.3))
                    
                    aug_path = dataset_dir / f"augmented_{total_images:04d}.jpg"
                    img.save(aug_path, 'JPEG', quality=85)
                    total_images += 1
                    
                except Exception as e:
                    print(f"⚠️ Augmentation failed: {e}")
                    continue
        
        print(f"✅ Created dataset with {total_images} images")
        return str(dataset_dir)
    
    def process_image(self, image_path: str) -> Dict:
        """Main processing pipeline"""
        print(f"🌟 Processing plant image: {image_path}")
        start_time = time.time()
        
        # Step 1: Try to detect known plant
        is_known, detection_result = self.detect_known_plant(image_path)
        
        if is_known:
            processing_time = time.time() - start_time
            return {
                "status": "known_plant",
                "plant_info": detection_result["plant_info"],
                "confidence": detection_result["confidence"],
                "processing_time": processing_time,
                "message": "Plant identified from existing database"
            }
        else:
            # Step 2: Generate new plant info
            plant_info = self.generate_plant_info(image_path)
            
            # Step 3: Create synthetic images
            synthetic_images = self.create_synthetic_images(plant_info, 8)
            
            # Step 4: Create dataset
            dataset_path = self.create_dataset(plant_info, synthetic_images)
            
            processing_time = time.time() - start_time
            
            return {
                "status": "new_plant_generated",
                "plant_info": plant_info,
                "dataset_path": dataset_path,
                "synthetic_images_count": len(synthetic_images),
                "augmented_images_count": 30,
                "processing_time": processing_time,
                "message": f"New plant '{plant_info['common_name']}' added to dataset"
            }

# Flask Web App
app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024

# Initialize AI system
plant_ai = SimplePlantAI()

# Simple HTML template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>🌱 AI Plant Recognition Demo</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f0f8f0; }
        .header { text-align: center; background: #4CAF50; color: white; padding: 30px; border-radius: 10px; margin-bottom: 20px; }
        .upload-area { border: 3px dashed #4CAF50; padding: 40px; text-align: center; background: white; border-radius: 10px; cursor: pointer; }
        .upload-area:hover { background: #f9f9f9; }
        .result { background: white; padding: 20px; border-radius: 10px; margin-top: 20px; border-left: 5px solid #4CAF50; }
        .loading { display: none; text-align: center; padding: 20px; background: white; border-radius: 10px; }
        .plant-info { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .medicinal-uses { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
        .confidence { font-weight: bold; color: #28a745; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #4CAF50; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 20px auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌱 AI Medicinal Plant Recognition</h1>
        <p>Simple Demo - Self-Learning Plant Identification</p>
    </div>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <h3>📸 Upload Plant Image</h3>
        <p>Click here to upload your plant image</p>
        <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="uploadImage()">
    </div>
    
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <h3>🧠 AI Processing...</h3>
        <p>Analyzing plant and generating information...</p>
    </div>
    
    <div id="results"></div>
    
    <script>
        function uploadImage() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;
            
            const formData = new FormData();
            formData.append('image', file);
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').innerHTML = '';
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                displayResults(data);
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').innerHTML = '<div class="result"><h3>❌ Error</h3><p>' + error + '</p></div>';
            });
        }
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            let html = '<div class="result">';
            
            if (data.status === 'known_plant') {
                html += '<h3>✅ Known Plant Identified</h3>';
                html += `<p class="confidence">Confidence: ${(data.confidence * 100).toFixed(1)}%</p>`;
            } else {
                html += '<h3>🆕 New Plant Discovered & Added to Dataset</h3>';
                html += `<p>Generated ${data.synthetic_images_count} synthetic images and ${data.augmented_images_count} total training images</p>`;
            }
            
            const plantInfo = data.plant_info;
            html += `
                <div class="plant-info">
                    <h4>🌿 ${plantInfo.common_name}</h4>
                    <p><strong>Scientific Name:</strong> ${plantInfo.scientific_name}</p>
                    <p><strong>Local Name:</strong> ${plantInfo.local_name}</p>
                    <p><strong>Family:</strong> ${plantInfo.family}</p>
                    <p><strong>Description:</strong> ${plantInfo.description}</p>
                </div>
            `;
            
            if (plantInfo.medicinal_uses && plantInfo.medicinal_uses.length > 0) {
                html += '<div class="medicinal-uses"><h4>💊 Medicinal Uses:</h4><ul>';
                plantInfo.medicinal_uses.forEach(use => {
                    html += `<li>${use}</li>`;
                });
                html += '</ul></div>';
            }
            
            html += `<p>⏱️ Processing time: ${data.processing_time.toFixed(2)} seconds</p>`;
            html += `<p><em>${data.message}</em></p>`;
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return HTML_TEMPLATE

@app.route('/upload', methods=['POST'])
def upload_image():
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if file:
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Process the image
            result = plant_ai.process_image(filepath)
            
            # Clean up
            try:
                os.remove(filepath)
            except:
                pass
            
            return jsonify(result)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🌱 AI Medicinal Plant Recognition System - Simple Demo")
    print("=" * 60)
    print("🚀 Features:")
    print("  • Plant detection simulation")
    print("  • AI plant identification")
    print("  • Synthetic image generation")
    print("  • Dataset creation and augmentation")
    print("=" * 60)
    print("📱 Open your browser and go to: http://localhost:5000")
    print("🌿 Upload plant images to test the system!")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
