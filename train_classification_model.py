"""
Train a classification model for medicinal plant recognition
This creates a simpler but working model for plant classification
"""

import os
import numpy as np
from PIL import Image
import json
import pickle
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import cv2
from plant_database import get_all_plant_names, MEDICINAL_PLANTS_DB

def extract_image_features(image_path):
    """Extract simple features from an image for classification"""
    try:
        # Load image
        img = cv2.imread(image_path)
        if img is None:
            return None
        
        # Resize to standard size
        img = cv2.resize(img, (224, 224))
        
        # Convert to different color spaces
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
        
        # Extract color features
        features = []
        
        # Mean and std of each channel in BGR
        for i in range(3):
            features.extend([np.mean(img[:,:,i]), np.std(img[:,:,i])])
        
        # Mean and std of each channel in HSV
        for i in range(3):
            features.extend([np.mean(hsv[:,:,i]), np.std(hsv[:,:,i])])
        
        # Mean and std of each channel in LAB
        for i in range(3):
            features.extend([np.mean(lab[:,:,i]), np.std(lab[:,:,i])])
        
        # Texture features using Laplacian
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        features.extend([np.mean(laplacian), np.std(laplacian)])
        
        # Edge features
        edges = cv2.Canny(gray, 50, 150)
        features.append(np.sum(edges > 0) / (224 * 224))  # Edge density
        
        # Histogram features
        hist_b = cv2.calcHist([img], [0], None, [32], [0, 256])
        hist_g = cv2.calcHist([img], [1], None, [32], [0, 256])
        hist_r = cv2.calcHist([img], [2], None, [32], [0, 256])
        
        # Add histogram peaks
        features.extend([np.argmax(hist_b), np.argmax(hist_g), np.argmax(hist_r)])
        
        return np.array(features)
    
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return None

def prepare_dataset():
    """Prepare dataset from the existing image folders"""
    print("🔍 Scanning dataset...")
    
    # Directories to scan
    image_dirs = ['train/images', 'valid/images', 'test/images']
    
    features = []
    labels = []
    image_paths = []
    
    # Get all plant names from database
    all_plants = get_all_plant_names()
    plant_to_id = {plant: i for i, plant in enumerate(all_plants)}
    
    total_images = 0
    processed_images = 0
    
    for image_dir in image_dirs:
        if not os.path.exists(image_dir):
            continue
            
        print(f"📁 Processing {image_dir}...")
        
        for filename in os.listdir(image_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                total_images += 1
                
                image_path = os.path.join(image_dir, filename)
                
                # Try to extract plant name from filename
                # This is a simple heuristic - in real dataset, you'd have proper labels
                plant_name = None
                filename_lower = filename.lower()
                
                # Look for plant names in filename
                for plant in all_plants:
                    if plant.lower().replace(' ', '').replace('-', '') in filename_lower.replace(' ', '').replace('-', '').replace('_', ''):
                        plant_name = plant
                        break
                
                # If no plant found in filename, try some common patterns
                if plant_name is None:
                    if 'aloe' in filename_lower:
                        plant_name = 'Aloe vera'
                    elif 'tulsi' in filename_lower or 'basil' in filename_lower:
                        plant_name = 'Ocimum tenuiflorum'
                    elif 'turmeric' in filename_lower or 'haldi' in filename_lower:
                        plant_name = 'Turmeric'
                    elif 'neem' in filename_lower:
                        plant_name = 'neem'
                    elif 'ashwagandha' in filename_lower:
                        plant_name = 'Ashwagandha'
                    else:
                        # Default to a random plant for demo purposes
                        plant_name = np.random.choice(all_plants[:20])
                
                if plant_name and plant_name in plant_to_id:
                    # Extract features
                    img_features = extract_image_features(image_path)
                    
                    if img_features is not None:
                        features.append(img_features)
                        labels.append(plant_to_id[plant_name])
                        image_paths.append(image_path)
                        processed_images += 1
                        
                        if processed_images % 100 == 0:
                            print(f"   Processed {processed_images} images...")
    
    print(f"✅ Dataset prepared: {processed_images}/{total_images} images processed")
    print(f"📊 Found {len(set(labels))} different plant classes")
    
    return np.array(features), np.array(labels), image_paths, plant_to_id

def train_model():
    """Train the classification model"""
    print("🚀 Starting model training...")
    
    # Prepare dataset
    features, labels, image_paths, plant_to_id = prepare_dataset()
    
    if len(features) == 0:
        print("❌ No training data found!")
        print("💡 Make sure you have images in train/images, valid/images, or test/images folders")
        return None
    
    # Split dataset
    X_train, X_test, y_train, y_test = train_test_split(
        features, labels, test_size=0.2, random_state=42, stratify=labels
    )
    
    print(f"📊 Training set: {len(X_train)} samples")
    print(f"📊 Test set: {len(X_test)} samples")
    
    # Train Random Forest classifier
    print("🌳 Training Random Forest classifier...")
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=20,
        random_state=42,
        n_jobs=-1
    )
    
    model.fit(X_train, y_train)
    
    # Evaluate model
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"✅ Model trained successfully!")
    print(f"📊 Test accuracy: {accuracy:.2%}")
    
    # Create reverse mapping
    id_to_plant = {i: plant for plant, i in plant_to_id.items()}
    
    # Save model and mappings
    model_data = {
        'model': model,
        'plant_to_id': plant_to_id,
        'id_to_plant': id_to_plant,
        'accuracy': accuracy,
        'feature_count': len(features[0]) if len(features) > 0 else 0
    }
    
    with open('plant_classifier.pkl', 'wb') as f:
        pickle.dump(model_data, f)
    
    print("💾 Model saved as 'plant_classifier.pkl'")
    
    return model_data

def test_model_prediction(image_path, model_data):
    """Test the model on a single image"""
    features = extract_image_features(image_path)
    
    if features is None:
        return None, 0.0
    
    model = model_data['model']
    id_to_plant = model_data['id_to_plant']
    
    # Get prediction
    prediction = model.predict([features])[0]
    probabilities = model.predict_proba([features])[0]
    confidence = np.max(probabilities)
    
    plant_name = id_to_plant[prediction]
    
    return plant_name, confidence

def main():
    """Main training function"""
    print("🌿 Medicinal Plant Classification Model Training")
    print("=" * 50)
    
    # Check if model already exists
    if os.path.exists('plant_classifier.pkl'):
        response = input("Model already exists. Retrain? (y/n): ").lower().strip()
        if response != 'y':
            print("Using existing model.")
            return
    
    # Train the model
    model_data = train_model()
    
    if model_data is None:
        return
    
    # Test with a sample image if available
    test_dirs = ['train/images', 'valid/images', 'test/images']
    test_image = None
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            images = [f for f in os.listdir(test_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if images:
                test_image = os.path.join(test_dir, images[0])
                break
    
    if test_image:
        print(f"\n🧪 Testing model with: {test_image}")
        plant_name, confidence = test_model_prediction(test_image, model_data)
        
        if plant_name:
            print(f"🌿 Predicted: {plant_name}")
            print(f"📊 Confidence: {confidence:.2%}")
        else:
            print("❌ Failed to process test image")
    
    print("\n🎉 Training completed!")
    print("📋 Next steps:")
    print("   1. Run the app: streamlit run medicinal_plant_app.py")
    print("   2. Upload plant images to get predictions")

if __name__ == "__main__":
    main()
