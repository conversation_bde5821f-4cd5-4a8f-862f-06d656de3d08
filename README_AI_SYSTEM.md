# 🌱 AI Medicinal Plant Recognition System

**Self-Learning Plant Identification with Auto-Dataset Generation**

A complete AI system that can detect known plants and automatically generate datasets for unknown plants using advanced AI models.

---

## 🎯 System Overview

### What This System Does

1. **🔍 Detect Known Plants** → Uses your trained YOLO model to identify plants in your dataset
2. **🧠 Identify Unknown Plants** → Uses Gemini Vision AI to analyze and identify new plants
3. **🎨 Generate Synthetic Images** → Creates 20+ AI-generated images using Stable Diffusion
4. **📈 Data Augmentation** → Applies advanced augmentation to reach 100+ images per plant
5. **🔄 Auto-Dataset Expansion** → Automatically adds new plants to your training dataset
6. **🏋️ Model Retraining** → Periodically retrains YOLO to include new plants

### Key Features

- **Self-Learning**: System improves automatically as it encounters new plants
- **AI-Powered**: Uses Gemini Vision + Stable Diffusion for plant identification and image generation
- **Complete Pipeline**: From detection to dataset creation to model retraining
- **Web Interface**: Easy-to-use web interface for uploading and testing
- **Production Ready**: Robust error handling and logging

---

## 🚀 Quick Start

### 1. Setup

```bash
# Run the setup script
python setup_ai_system.py

# This will:
# - Create all necessary directories
# - Install required packages
# - Setup configuration files
# - Prompt for API keys
```

### 2. Configure API Keys

Get your API keys:
- **Gemini API**: https://makersuite.google.com/app/apikey
- **Replicate API**: https://replicate.com/account/api-tokens

The setup script will prompt you to enter these keys.

### 3. Run the System

```bash
python auto_augment_and_generate.py
```

### 4. Test the System

1. Open your browser to: `http://localhost:5000`
2. Upload a plant image
3. Watch the AI system work its magic!

---

## 📋 System Architecture

```
🌱 AI Plant Recognition System
│
├── 🔍 YOLO Detection (Known Plants)
│   ├── Load trained model
│   ├── Detect plant in image
│   └── Return plant information
│
├── 🧠 AI Identification (Unknown Plants)
│   ├── Gemini Vision Analysis
│   ├── Generate plant information
│   ├── Create synthetic images
│   ├── Apply data augmentation
│   ├── Update dataset
│   └── Schedule retraining
│
└── 🌐 Web Interface
    ├── Image upload
    ├── Real-time processing
    └── Results display
```

---

## 🔧 Configuration

Edit `config.yaml` to customize:

```yaml
# Model Settings
yolo_model_path: 'models/best.pt'
confidence_threshold: 0.5

# AI Generation
images_per_plant: 100
synthetic_images: 20

# API Keys
gemini_api_key: 'your-key-here'
replicate_api_key: 'your-key-here'
```

---

## 📁 Project Structure

```
/plant_ai_project/
├── auto_augment_and_generate.py    # Main AI system
├── config.yaml                     # Configuration
├── setup_ai_system.py             # Setup script
├── requirements_ai_system.txt      # Dependencies
│
├── dataset/                        # YOLO training data
│   ├── train/images/
│   ├── train/labels/
│   ├── val/images/
│   └── val/labels/
│
├── new_plants/                     # Auto-generated plants
│   ├── plant_name_1/
│   │   ├── plant_info.json
│   │   ├── original_001.jpg
│   │   └── augmented_*.jpg
│   └── plant_name_2/
│
├── models/                         # Trained models
├── logs/                          # System logs
└── generated_images/              # AI-generated images
```

---

## 🌟 How It Works

### For Known Plants:
1. Upload image → YOLO detects plant → Return stored information

### For Unknown Plants:
1. Upload image → YOLO fails to detect
2. Gemini Vision analyzes image → Identifies plant species
3. Gemini Text generates detailed plant information
4. Stable Diffusion creates 20 synthetic images
5. Albumentations applies augmentation → 100+ total images
6. System creates new dataset folder with all images
7. Updates `data.yaml` with new plant class
8. Optionally retrains YOLO model

---

## 🎮 Web Interface Features

- **📸 Drag & Drop Upload**: Easy image uploading
- **⚡ Real-time Processing**: Live status updates
- **📊 Detailed Results**: Plant information, confidence scores
- **🔄 Processing Stats**: Timing and performance metrics
- **📈 System Stats**: Dataset size, model status

---

## 🧪 API Endpoints

```http
GET  /              # Web interface
POST /upload        # Upload and process plant image
POST /retrain       # Trigger model retraining
GET  /stats         # Get system statistics
```

---

## 🔬 Advanced Features

### Data Augmentation Pipeline
- Horizontal/Vertical flips
- Rotation (±30°)
- Brightness/Contrast adjustment
- Hue/Saturation/Value shifts
- Gaussian blur
- Random crops and resizing
- Elastic transformations
- Grid distortions

### AI Models Used
- **YOLO v8**: Object detection for known plants
- **Gemini Vision**: Plant identification from images
- **Gemini Pro**: Plant information generation
- **Stable Diffusion**: Synthetic image generation

---

## 🚀 Production Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t plant-ai-system .

# Run container
docker run -p 5000:5000 -v ./data:/app/data plant-ai-system
```

### Environment Variables
```bash
export GEMINI_API_KEY="your-gemini-key"
export REPLICATE_API_KEY="your-replicate-key"
export FLASK_ENV="production"
```

---

## 📊 Performance Metrics

- **Detection Speed**: < 2 seconds for known plants
- **AI Generation**: 30-60 seconds for new plants
- **Dataset Creation**: 100+ augmented images per plant
- **Model Accuracy**: Improves with each new plant added

---

## 🛠️ Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure valid Gemini and Replicate API keys
2. **YOLO Model Missing**: Train your custom model or use pretrained
3. **Memory Issues**: Reduce `images_per_plant` in config
4. **Slow Generation**: Check internet connection for API calls

### Logs
Check `logs/plant_ai.log` for detailed error information.

---

## 🔮 Future Enhancements

- **Mobile App**: React Native mobile application
- **Real-time Camera**: Live plant identification
- **Community Features**: User contributions and verification
- **Advanced Models**: Integration with latest vision transformers
- **Offline Mode**: Edge computing for remote areas

---

## 📝 License

MIT License - Feel free to use and modify for your projects!

---

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make your changes
4. Submit pull request

---

**Built with ❤️ for botanical research and AI education**
