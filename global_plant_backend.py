#!/usr/bin/env python3
"""
Global Plant AI Backend - Simplified Version
Identifies plants globally and learns from uploads
"""

import os
import sys
import io
import json
import base64
import hashlib
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
from werkzeug.utils import secure_filename
from PIL import Image
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

class SimpleGlobalPlantAI:
    """Simplified global plant AI system"""
    
    def __init__(self):
        self.plant_database = {}
        self.load_plant_knowledge()
    
    def load_plant_knowledge(self):
        """Load comprehensive plant knowledge"""
        self.plant_database = {
            "neem": {
                "plant_name": "Neem",
                "scientific_name": "Azadirachta indica",
                "local_names": {"hindi": "नीम", "sanskrit": "निम्ब", "tamil": "வேம்பு"},
                "medicinal_uses": ["Antibacterial", "Antifungal", "Blood purifier", "Skin disorders"],
                "family": "Meliaceae",
                "parts_used": ["Leaves", "Bark", "Seeds", "Oil"],
                "preparation": ["Decoction", "Paste", "Oil extraction"]
            },
            "tulsi": {
                "plant_name": "Tulsi",
                "scientific_name": "Ocimum tenuiflorum",
                "local_names": {"hindi": "तुलसी", "sanskrit": "तुलसी", "tamil": "துளசி"},
                "medicinal_uses": ["Respiratory disorders", "Stress relief", "Immunity booster", "Fever"],
                "family": "Lamiaceae",
                "parts_used": ["Leaves", "Seeds"],
                "preparation": ["Tea", "Fresh consumption", "Decoction"]
            },
            "aloe_vera": {
                "plant_name": "Aloe Vera",
                "scientific_name": "Aloe barbadensis",
                "local_names": {"hindi": "घृतकुमारी", "sanskrit": "घृतकुमारी", "tamil": "கற்றாழை"},
                "medicinal_uses": ["Skin healing", "Digestive health", "Anti-inflammatory", "Burns"],
                "family": "Asphodelaceae",
                "parts_used": ["Gel", "Leaves"],
                "preparation": ["Fresh gel", "Juice", "Topical application"]
            },
            "turmeric": {
                "plant_name": "Turmeric",
                "scientific_name": "Curcuma longa",
                "local_names": {"hindi": "हल्दी", "sanskrit": "हरिद्रा", "tamil": "மஞ்சள்"},
                "medicinal_uses": ["Anti-inflammatory", "Antioxidant", "Wound healing", "Digestive aid"],
                "family": "Zingiberaceae",
                "parts_used": ["Rhizome"],
                "preparation": ["Powder", "Paste", "Decoction"]
            },
            "ginger": {
                "plant_name": "Ginger",
                "scientific_name": "Zingiber officinale",
                "local_names": {"hindi": "अदरक", "sanskrit": "आर्द्रक", "tamil": "இஞ்சி"},
                "medicinal_uses": ["Digestive aid", "Anti-nausea", "Anti-inflammatory", "Cold relief"],
                "family": "Zingiberaceae",
                "parts_used": ["Rhizome"],
                "preparation": ["Fresh", "Tea", "Powder"]
            }
        }
    
    def identify_plant_with_apis(self, image_data: bytes) -> Dict:
        """Try to identify plant using external APIs"""
        try:
            # Try PlantNet API (free but requires registration)
            plantnet_result = self.try_plantnet_api(image_data)
            if plantnet_result:
                return plantnet_result
            
            # Try Google Vision API for general plant detection
            google_result = self.try_google_vision_simulation(image_data)
            if google_result:
                return google_result
            
            # Fallback to local analysis
            return self.analyze_image_locally(image_data)
            
        except Exception as e:
            logger.error(f"API identification failed: {e}")
            return self.analyze_image_locally(image_data)
    
    def try_plantnet_api(self, image_data: bytes) -> Optional[Dict]:
        """Try PlantNet API (simulation for demo)"""
        # This would use real PlantNet API with proper API key
        # For demo, we'll simulate the response
        
        import random
        
        # Simulate API response based on image analysis
        confidence = random.uniform(0.6, 0.95)
        
        # Pick a random plant from our database for demo
        plant_key = random.choice(list(self.plant_database.keys()))
        plant_info = self.plant_database[plant_key].copy()
        
        plant_info.update({
            "confidence": confidence,
            "source": "PlantNet API (Simulated)",
            "api_used": True
        })
        
        return plant_info
    
    def try_google_vision_simulation(self, image_data: bytes) -> Optional[Dict]:
        """Simulate Google Vision API response"""
        try:
            # Analyze image colors to make educated guesses
            image = Image.open(io.BytesIO(image_data))
            img_array = np.array(image)
            
            # Simple color analysis
            avg_color = np.mean(img_array, axis=(0, 1))
            
            # Make educated guesses based on color
            if len(avg_color) >= 3:
                green_dominant = avg_color[1] > avg_color[0] and avg_color[1] > avg_color[2]
                
                if green_dominant:
                    # Likely a green plant
                    plant_candidates = ["neem", "tulsi", "aloe_vera"]
                else:
                    # Could be turmeric, ginger, etc.
                    plant_candidates = ["turmeric", "ginger"]
                
                import random
                selected_plant = random.choice(plant_candidates)
                plant_info = self.plant_database[selected_plant].copy()
                
                plant_info.update({
                    "confidence": random.uniform(0.5, 0.8),
                    "source": "Google Vision (Simulated)",
                    "api_used": True,
                    "analysis": f"Color analysis: Green dominant = {green_dominant}"
                })
                
                return plant_info
            
            return None
            
        except Exception as e:
            logger.error(f"Google Vision simulation failed: {e}")
            return None
    
    def analyze_image_locally(self, image_data: bytes) -> Dict:
        """Local image analysis fallback"""
        try:
            image = Image.open(io.BytesIO(image_data))
            
            # Simple local analysis
            import random
            
            # For demo, randomly select a plant with lower confidence
            plant_key = random.choice(list(self.plant_database.keys()))
            plant_info = self.plant_database[plant_key].copy()
            
            plant_info.update({
                "confidence": random.uniform(0.3, 0.6),
                "source": "Local Analysis",
                "api_used": False,
                "analysis": "Basic local image analysis performed"
            })
            
            return plant_info
            
        except Exception as e:
            logger.error(f"Local analysis failed: {e}")
            return self.create_unknown_result()
    
    def create_unknown_result(self) -> Dict:
        """Create result for unknown plants"""
        return {
            "plant_name": "Unknown Plant",
            "scientific_name": "Unknown species",
            "local_names": {},
            "medicinal_uses": ["Please consult a botanist or plant expert"],
            "family": "Unknown",
            "parts_used": ["Consult expert"],
            "preparation": ["Consult expert"],
            "confidence": 0.0,
            "source": "Fallback",
            "api_used": False,
            "safety_warning": "Plant could not be identified. Do not use medicinally."
        }
    
    def enhance_with_global_info(self, plant_info: Dict) -> Dict:
        """Enhance plant info with global medicinal knowledge"""
        enhanced = plant_info.copy()
        
        # Add safety information
        enhanced["safety_info"] = {
            "warning": "Always consult a healthcare professional before using any plant medicinally",
            "identification_confidence": plant_info.get("confidence", 0),
            "verified": plant_info.get("confidence", 0) > 0.8,
            "source_reliability": "High" if plant_info.get("api_used") else "Medium"
        }
        
        # Add global usage information
        enhanced["global_usage"] = {
            "traditional_systems": ["Ayurveda", "Traditional Chinese Medicine", "Folk Medicine"],
            "modern_research": "Consult scientific literature for current research",
            "cultivation": "Widely cultivated in tropical and subtropical regions"
        }
        
        return enhanced

# Initialize global AI
global_ai = SimpleGlobalPlantAI()

# Simple in-memory database for learning
learning_database = {
    "plants": [],
    "statistics": {
        "total_identifications": 0,
        "successful_identifications": 0,
        "plants_added": 0
    }
}

def add_to_learning_database(plant_info: Dict, image_data: bytes, filename: str):
    """Add plant to learning database"""
    try:
        # Create image hash for deduplication
        image_hash = hashlib.md5(image_data).hexdigest()
        
        # Check if already exists
        for existing in learning_database["plants"]:
            if existing.get("image_hash") == image_hash:
                existing["identification_count"] = existing.get("identification_count", 0) + 1
                existing["last_seen"] = datetime.now().isoformat()
                return "Updated existing plant record"
        
        # Add new plant
        plant_record = {
            **plant_info,
            "image_hash": image_hash,
            "filename": filename,
            "added_date": datetime.now().isoformat(),
            "identification_count": 1,
            "auto_added": True
        }
        
        learning_database["plants"].append(plant_record)
        learning_database["statistics"]["plants_added"] += 1
        
        return "Added new plant to learning database"
        
    except Exception as e:
        logger.error(f"Failed to add to learning database: {e}")
        return "Failed to add to database"

# HTML Template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌿 Global Plant AI Recognition</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            background: white; padding: 2rem; border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px; margin: 0 auto;
        }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 1rem; font-size: 2.5rem; }
        .subtitle { color: #7f8c8d; text-align: center; font-size: 1.1rem; margin-bottom: 2rem; }
        .features {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem; margin-bottom: 2rem;
        }
        .feature {
            background: #f8f9fa; padding: 1rem; border-radius: 8px; text-align: center;
        }
        .upload-area {
            border: 3px dashed #3498db; border-radius: 10px; padding: 2rem;
            margin: 2rem 0; background: #f8f9fa; cursor: pointer;
            transition: all 0.3s ease; text-align: center;
        }
        .upload-area:hover { border-color: #2980b9; background: #e3f2fd; }
        .upload-area.dragover { border-color: #27ae60; background: #d5f4e6; }
        #fileInput { display: none; }
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9); color: white;
            border: none; padding: 12px 30px; border-radius: 25px;
            cursor: pointer; font-size: 1rem; transition: all 0.3s ease; margin: 0.5rem;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4); }
        .result { margin-top: 2rem; padding: 1.5rem; background: #f8f9fa; border-radius: 10px; display: none; }
        .result.show { display: block; }
        .plant-info { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-top: 1rem; }
        .info-section { background: white; padding: 1rem; border-radius: 8px; border-left: 4px solid #27ae60; }
        .info-section h4 { color: #27ae60; margin-bottom: 0.5rem; }
        .confidence-bar { background: #ecf0f1; height: 20px; border-radius: 10px; overflow: hidden; margin: 1rem 0; }
        .confidence-fill { height: 100%; background: linear-gradient(45deg, #27ae60, #2ecc71); transition: width 0.5s ease; }
        .loading { display: none; text-align: center; color: #3498db; font-size: 1.1rem; margin-top: 1rem; }
        .loading.show { display: block; }
        .preview-image { max-width: 100%; max-height: 300px; border-radius: 10px; margin-top: 1rem; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .status { padding: 1rem; margin: 1rem 0; border-radius: 8px; font-weight: bold; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌿 Global Plant AI Recognition</h1>
        <p class="subtitle">Upload any plant image for global AI identification</p>
        
        <div class="features">
            <div class="feature">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🌍</div>
                <strong>Global Database</strong>
                <p>Identifies plants worldwide</p>
            </div>
            <div class="feature">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🤖</div>
                <strong>AI Powered</strong>
                <p>Multiple AI systems</p>
            </div>
            <div class="feature">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">📚</div>
                <strong>Auto Learning</strong>
                <p>Grows with each upload</p>
            </div>
            <div class="feature">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">💊</div>
                <strong>Medicinal Info</strong>
                <p>Traditional uses & safety</p>
            </div>
        </div>
        
        <div class="status success">
            ✅ Global Plant AI System is active and ready!
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📸</div>
            <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">Upload Any Plant Image</div>
            <p>AI will identify it globally and add to learning database</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>
        
        <div style="text-align: center;">
            <button class="btn" onclick="analyzeImage()">🔍 Identify Plant Globally</button>
            <button class="btn" onclick="clearResults()">🗑️ Clear</button>
            <button class="btn" onclick="showStats()">📊 Statistics</button>
        </div>
        
        <div class="loading" id="loading">
            <div style="font-size: 2rem; margin-bottom: 1rem;">🔄</div>
            <div>Analyzing with global AI systems...</div>
        </div>
        
        <div class="result" id="result">
            <h3>🌱 Global Plant Identification Results</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                displayImagePreview(file);
            }
        });
        
        function displayImagePreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const uploadArea = document.querySelector('.upload-area');
                uploadArea.innerHTML = `
                    <img src="${e.target.result}" class="preview-image" alt="Preview">
                    <p style="margin-top: 1rem;"><strong>File:</strong> ${file.name}</p>
                `;
            };
            reader.readAsDataURL(file);
        }
        
        async function analyzeImage() {
            if (!selectedFile) {
                alert('Please select an image first!');
                return;
            }
            
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            loading.classList.add('show');
            result.classList.remove('show');
            
            const formData = new FormData();
            formData.append('image', selectedFile);
            
            try {
                const response = await fetch('/identify-global', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                loading.classList.remove('show');
                
                if (data.success) {
                    displayResults(data);
                } else {
                    displayError(data.error || 'Identification failed');
                }
                
            } catch (error) {
                loading.classList.remove('show');
                displayError('Connection error: ' + error.message);
            }
        }
        
        function displayResults(data) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            const plant = data.plant_info;
            
            resultContent.innerHTML = `
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: ${(plant.confidence * 100)}%"></div>
                </div>
                <div style="text-align: center; margin-bottom: 1rem;">
                    <strong>Confidence: ${(plant.confidence * 100).toFixed(1)}% | Source: ${plant.source}</strong>
                </div>
                
                <div class="plant-info">
                    <div class="info-section">
                        <h4>🌿 Plant Information</h4>
                        <p><strong>Name:</strong> ${plant.plant_name}</p>
                        <p><strong>Scientific:</strong> ${plant.scientific_name}</p>
                        <p><strong>Family:</strong> ${plant.family || 'Unknown'}</p>
                    </div>
                    
                    <div class="info-section">
                        <h4>🏥 Medicinal Uses</h4>
                        <ul>
                            ${plant.medicinal_uses.map(use => `<li>${use}</li>`).join('')}
                        </ul>
                    </div>
                    
                    <div class="info-section">
                        <h4>🌍 Local Names</h4>
                        ${Object.entries(plant.local_names || {}).map(([lang, name]) => 
                            `<p><strong>${lang}:</strong> ${name}</p>`
                        ).join('') || '<p>No local names available</p>'}
                    </div>
                    
                    <div class="info-section">
                        <h4>⚠️ Safety Information</h4>
                        <p>${plant.safety_info?.warning || 'Always consult experts before medicinal use'}</p>
                        <p><strong>Reliability:</strong> ${plant.safety_info?.source_reliability || 'Medium'}</p>
                    </div>
                </div>
                
                <div style="margin-top: 1rem; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                    <h4>📚 Database Status:</h4>
                    <p>${data.database_action}</p>
                    <p><strong>Processing Time:</strong> ${data.processing_time.toFixed(2)} seconds</p>
                </div>
            `;
            
            result.classList.add('show');
        }
        
        function displayError(message) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `
                <div class="status error">
                    ❌ Error: ${message}
                </div>
            `;
            
            result.classList.add('show');
        }
        
        async function showStats() {
            try {
                const response = await fetch('/stats');
                const data = await response.json();
                
                if (data.success) {
                    alert(`Database Statistics:
Total Plants: ${data.stats.plants_added}
Total Identifications: ${data.stats.total_identifications}
Success Rate: ${((data.stats.successful_identifications / data.stats.total_identifications) * 100).toFixed(1)}%`);
                }
            } catch (error) {
                alert('Failed to load statistics');
            }
        }
        
        function clearResults() {
            selectedFile = null;
            document.getElementById('result').classList.remove('show');
            document.getElementById('loading').classList.remove('show');
            
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">📸</div>
                <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">Upload Any Plant Image</div>
                <p>AI will identify it globally and add to learning database</p>
                <input type="file" id="fileInput" accept="image/*">
            `;
            
            document.getElementById('fileInput').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    selectedFile = file;
                    displayImagePreview(file);
                }
            });
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy',
        'message': 'Global Plant AI Recognition System is running',
        'version': '2.0.0',
        'features': ['Global identification', 'Auto-learning', 'Medicinal info']
    })

@app.route('/identify-global', methods=['POST'])
def identify_global():
    start_time = datetime.now()
    
    try:
        if 'image' not in request.files:
            return jsonify({'success': False, 'error': 'No image file provided'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        # Read image data
        image_data = file.read()
        filename = secure_filename(file.filename)
        
        # Update statistics
        learning_database["statistics"]["total_identifications"] += 1
        
        # Identify plant using global AI
        plant_info = global_ai.identify_plant_with_apis(image_data)
        
        # Enhance with global information
        enhanced_info = global_ai.enhance_with_global_info(plant_info)
        
        # Add to learning database if confidence is reasonable
        if enhanced_info.get('confidence', 0) > 0.3:
            database_action = add_to_learning_database(enhanced_info, image_data, filename)
            learning_database["statistics"]["successful_identifications"] += 1
        else:
            database_action = "Low confidence - not added to database"
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return jsonify({
            'success': True,
            'plant_info': enhanced_info,
            'database_action': database_action,
            'processing_time': processing_time,
            'message': 'Global plant identification completed'
        })
        
    except Exception as e:
        logger.error(f"Identification error: {e}")
        return jsonify({'success': False, 'error': f'Processing error: {str(e)}'}), 500

@app.route('/stats')
def stats():
    try:
        stats = learning_database["statistics"].copy()
        stats["database_size"] = len(learning_database["plants"])
        
        return jsonify({
            'success': True,
            'stats': stats,
            'message': 'Statistics retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    print("🌿" * 60)
    print("🌿 GLOBAL PLANT AI RECOGNITION SYSTEM")
    print("🌿" * 60)
    print()
    print("🚀 Features:")
    print("   🌍 Global plant identification")
    print("   🤖 Multiple AI systems simulation")
    print("   📚 Auto-learning database")
    print("   💊 Medicinal plant information")
    print("   🔍 Real-time plant analysis")
    print()
    print("🌐 Open your browser: http://127.0.0.1:5000")
    print("📱 Upload ANY plant image for global identification!")
    print()
    
    app.run(host='127.0.0.1', port=5000, debug=True)
