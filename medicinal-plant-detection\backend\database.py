"""
MongoDB Database Manager for Medicinal Plant Recognition System
Handles all database operations including plant data and predictions
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, DuplicateKeyError
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    logging.warning("PyMongo not installed. Install with: pip install pymongo")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MedicinalPlantDB:
    """MongoDB database manager for medicinal plants"""
    
    def __init__(self):
        """Initialize database connection"""
        self.client = None
        self.db = None
        self.plants_collection = None
        self.predictions_collection = None
        self.connected = False
        
        if PYMONGO_AVAILABLE:
            self.connect()
        else:
            logger.warning("PyMongo not available. Using fallback mode.")
    
    def connect(self):
        """Connect to MongoDB"""
        try:
            mongodb_uri = os.getenv('MONGODB_URI')
            database_name = os.getenv('DATABASE_NAME', 'medicinal_plants_db')
            
            if not mongodb_uri:
                logger.error("MONGODB_URI not found in environment variables")
                return False
            
            # Create MongoDB client
            self.client = MongoClient(mongodb_uri, serverSelectionTimeoutMS=5000)
            
            # Test connection
            self.client.admin.command('ping')
            
            # Get database and collections
            self.db = self.client[database_name]
            self.plants_collection = self.db[os.getenv('COLLECTION_NAME', 'plants')]
            self.predictions_collection = self.db[os.getenv('PREDICTIONS_COLLECTION', 'predictions')]
            
            self.connected = True
            logger.info(f"Successfully connected to MongoDB: {database_name}")
            
            # Create indexes
            self.create_indexes()
            
            return True
            
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            self.connected = False
            return False
    
    def create_indexes(self):
        """Create database indexes for better performance"""
        try:
            if not self.connected:
                return
            
            # Plants collection indexes
            self.plants_collection.create_index("plant_name", unique=True)
            self.plants_collection.create_index("scientific_name")
            self.plants_collection.create_index([("medicinal_uses", "text")])
            
            # Predictions collection indexes
            self.predictions_collection.create_index("timestamp")
            self.predictions_collection.create_index("predicted_plant")
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
    
    def get_plant_info(self, plant_name: str) -> Optional[Dict[str, Any]]:
        """Get plant information by name"""
        try:
            if not self.connected:
                return self._get_fallback_plant_info(plant_name)
            
            result = self.plants_collection.find_one({"plant_name": plant_name})
            
            if result:
                result.pop('_id', None)  # Remove MongoDB ObjectId
                return result
            else:
                return self._get_fallback_plant_info(plant_name)
                
        except Exception as e:
            logger.error(f"Error retrieving plant: {e}")
            return self._get_fallback_plant_info(plant_name)
    
    def _get_fallback_plant_info(self, plant_name: str) -> Dict[str, Any]:
        """Fallback plant information when database is not available"""
        plant_database = {
            "Neem": {
                "plant_name": "Neem",
                "scientific_name": "Azadirachta indica",
                "local_name": "Margosa Tree",
                "medicinal_uses": "Antibacterial, antifungal, skin disorders, diabetes management",
                "description": "Bitter tree with powerful antimicrobial properties"
            },
            "Tulsi": {
                "plant_name": "Tulsi",
                "scientific_name": "Ocimum tenuiflorum",
                "local_name": "Holy Basil",
                "medicinal_uses": "Respiratory problems, immunity booster, stress relief, fever reduction",
                "description": "Sacred herb with adaptogenic and respiratory benefits"
            },
            "AloeVera": {
                "plant_name": "AloeVera",
                "scientific_name": "Aloe barbadensis",
                "local_name": "Ghritkumari",
                "medicinal_uses": "Skin healing, burns treatment, digestive aid, anti-inflammatory",
                "description": "Cooling and healing properties for skin and internal use"
            },
            "Ashwagandha": {
                "plant_name": "Ashwagandha",
                "scientific_name": "Withania somnifera",
                "local_name": "Indian Winter Cherry",
                "medicinal_uses": "Stress relief, energy booster, immune support, sleep aid",
                "description": "Powerful adaptogenic herb for stress and vitality"
            },
            "Peppermint": {
                "plant_name": "Peppermint",
                "scientific_name": "Mentha piperita",
                "local_name": "Pudina",
                "medicinal_uses": "Digestive aid, respiratory relief, headache relief, cooling effect",
                "description": "Aromatic herb with cooling and digestive properties"
            },
            "Turmeric": {
                "plant_name": "Turmeric",
                "scientific_name": "Curcuma longa",
                "local_name": "Haldi",
                "medicinal_uses": "Anti-inflammatory, wound healing, immunity booster, joint pain relief",
                "description": "Golden spice with powerful anti-inflammatory effects"
            },
            "Ginger": {
                "plant_name": "Ginger",
                "scientific_name": "Zingiber officinale",
                "local_name": "Adrak",
                "medicinal_uses": "Nausea relief, digestive aid, anti-inflammatory, cold treatment",
                "description": "Warming root with digestive and anti-inflammatory properties"
            },
            "Basil": {
                "plant_name": "Basil",
                "scientific_name": "Ocimum basilicum",
                "local_name": "Sweet Basil",
                "medicinal_uses": "Stress relief, digestive aid, respiratory health, antimicrobial",
                "description": "Aromatic herb with culinary and medicinal uses"
            },
            "Lavender": {
                "plant_name": "Lavender",
                "scientific_name": "Lavandula angustifolia",
                "local_name": "English Lavender",
                "medicinal_uses": "Relaxation, sleep aid, anxiety relief, skin care",
                "description": "Fragrant herb with calming and soothing properties"
            },
            "Rosemary": {
                "plant_name": "Rosemary",
                "scientific_name": "Rosmarinus officinalis",
                "local_name": "Gulmehandi",
                "medicinal_uses": "Memory enhancement, circulation improvement, antioxidant, hair growth",
                "description": "Aromatic herb with cognitive and circulatory benefits"
            }
        }
        
        return plant_database.get(plant_name, {
            "plant_name": plant_name,
            "scientific_name": "Unknown",
            "local_name": "Unknown",
            "medicinal_uses": "Information not available",
            "description": "Plant information not found in database"
        })
    
    def log_prediction(self, prediction_data: Dict[str, Any]) -> bool:
        """Log plant prediction for analytics"""
        try:
            if not self.connected:
                logger.info(f"Prediction logged (offline): {prediction_data.get('predicted_plant', 'Unknown')}")
                return True
            
            prediction_data['timestamp'] = datetime.utcnow()
            
            result = self.predictions_collection.insert_one(prediction_data)
            
            if result.inserted_id:
                logger.info("Prediction logged successfully")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error logging prediction: {e}")
            return False
    
    def get_prediction_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get prediction statistics for the last N days"""
        try:
            if not self.connected:
                return {
                    "total_predictions": 0,
                    "top_plants": [],
                    "avg_confidence": 0.0,
                    "period_days": days,
                    "status": "offline"
                }
            
            from datetime import timedelta
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Aggregate predictions
            pipeline = [
                {"$match": {"timestamp": {"$gte": start_date}}},
                {"$group": {
                    "_id": "$predicted_plant",
                    "count": {"$sum": 1},
                    "avg_confidence": {"$avg": "$confidence"}
                }},
                {"$sort": {"count": -1}},
                {"$limit": 10}
            ]
            
            results = list(self.predictions_collection.aggregate(pipeline))
            
            total_predictions = self.predictions_collection.count_documents(
                {"timestamp": {"$gte": start_date}}
            )
            
            # Calculate overall average confidence
            avg_confidence_pipeline = [
                {"$match": {"timestamp": {"$gte": start_date}}},
                {"$group": {"_id": None, "avg_confidence": {"$avg": "$confidence"}}}
            ]
            
            avg_result = list(self.predictions_collection.aggregate(avg_confidence_pipeline))
            avg_confidence = avg_result[0]["avg_confidence"] if avg_result else 0.0
            
            return {
                "total_predictions": total_predictions,
                "top_plants": results,
                "avg_confidence": round(avg_confidence, 3),
                "period_days": days,
                "status": "connected"
            }
            
        except Exception as e:
            logger.error(f"Error getting prediction stats: {e}")
            return {"error": str(e), "status": "error"}
    
    def populate_plants_database(self):
        """Populate the database with initial plant data"""
        try:
            if not self.connected:
                logger.warning("Database not connected. Cannot populate.")
                return False
            
            # Sample plant data
            plants_data = [
                {
                    "plant_name": "Neem",
                    "scientific_name": "Azadirachta indica",
                    "local_name": "Margosa Tree",
                    "medicinal_uses": "Antibacterial, antifungal, skin disorders, diabetes management",
                    "description": "Bitter tree with powerful antimicrobial properties",
                    "created_at": datetime.utcnow()
                },
                {
                    "plant_name": "Tulsi",
                    "scientific_name": "Ocimum tenuiflorum",
                    "local_name": "Holy Basil",
                    "medicinal_uses": "Respiratory problems, immunity booster, stress relief, fever reduction",
                    "description": "Sacred herb with adaptogenic and respiratory benefits",
                    "created_at": datetime.utcnow()
                },
                {
                    "plant_name": "AloeVera",
                    "scientific_name": "Aloe barbadensis",
                    "local_name": "Ghritkumari",
                    "medicinal_uses": "Skin healing, burns treatment, digestive aid, anti-inflammatory",
                    "description": "Cooling and healing properties for skin and internal use",
                    "created_at": datetime.utcnow()
                },
                {
                    "plant_name": "Ashwagandha",
                    "scientific_name": "Withania somnifera",
                    "local_name": "Indian Winter Cherry",
                    "medicinal_uses": "Stress relief, energy booster, immune support, sleep aid",
                    "description": "Powerful adaptogenic herb for stress and vitality",
                    "created_at": datetime.utcnow()
                },
                {
                    "plant_name": "Peppermint",
                    "scientific_name": "Mentha piperita",
                    "local_name": "Pudina",
                    "medicinal_uses": "Digestive aid, respiratory relief, headache relief, cooling effect",
                    "description": "Aromatic herb with cooling and digestive properties",
                    "created_at": datetime.utcnow()
                }
            ]
            
            # Insert plants (ignore duplicates)
            for plant in plants_data:
                try:
                    self.plants_collection.insert_one(plant)
                except DuplicateKeyError:
                    pass  # Plant already exists
            
            logger.info(f"Database populated with {len(plants_data)} plants")
            return True
            
        except Exception as e:
            logger.error(f"Error populating database: {e}")
            return False

    def add_plant_to_database(self, plant_data: dict, image_data: bytes = None) -> bool:
        """Add a new plant to the database with image storage"""
        try:
            # Check if plant already exists
            existing = self.plants_collection.find_one({
                '$or': [
                    {'scientific_name': plant_data.get('scientific_name')},
                    {'image_hash': plant_data.get('image_hash')}
                ]
            })

            if existing:
                # Update existing plant with new information
                update_data = {
                    'last_seen': datetime.utcnow(),
                    'identification_count': existing.get('identification_count', 0) + 1
                }

                # Merge common names and sources
                if plant_data.get('common_names'):
                    existing_names = set(existing.get('common_names', []))
                    new_names = set(plant_data.get('common_names', []))
                    update_data['common_names'] = list(existing_names.union(new_names))

                if plant_data.get('sources'):
                    existing_sources = set(existing.get('sources', []))
                    new_sources = set(plant_data.get('sources', []))
                    update_data['sources'] = list(existing_sources.union(new_sources))

                self.plants_collection.update_one(
                    {'_id': existing['_id']},
                    {'$set': update_data}
                )

                logger.info(f"Updated existing plant: {plant_data.get('plant_name')}")
                return True

            # Add new plant
            plant_doc = {
                **plant_data,
                'created_date': datetime.utcnow(),
                'last_seen': datetime.utcnow(),
                'identification_count': 1,
                'user_verified': False,
                'expert_verified': False
            }

            # Store image if provided
            if image_data:
                # Store in GridFS for large images
                import gridfs
                fs = gridfs.GridFS(self.db)

                image_id = fs.put(
                    image_data,
                    filename=plant_data.get('filename', 'unknown.jpg'),
                    content_type='image/jpeg',
                    metadata={
                        'plant_name': plant_data.get('plant_name'),
                        'scientific_name': plant_data.get('scientific_name'),
                        'upload_date': datetime.utcnow()
                    }
                )

                plant_doc['image_id'] = image_id

            result = self.plants_collection.insert_one(plant_doc)

            if result.inserted_id:
                logger.info(f"Added new plant to database: {plant_data.get('plant_name')}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error adding plant to database: {e}")
            return False

    def search_plants_by_name(self, query: str, limit: int = 10) -> list:
        """Search plants by name with fuzzy matching"""
        try:
            # Create text index if not exists
            try:
                self.plants_collection.create_index([
                    ('plant_name', 'text'),
                    ('scientific_name', 'text'),
                    ('common_names', 'text')
                ])
            except:
                pass  # Index might already exist

            # Search with text index
            results = self.plants_collection.find(
                {'$text': {'$search': query}},
                {'score': {'$meta': 'textScore'}}
            ).sort([('score', {'$meta': 'textScore'})]).limit(limit)

            plants = []
            for plant in results:
                plant['_id'] = str(plant['_id'])
                if 'image_id' in plant:
                    plant['image_id'] = str(plant['image_id'])
                plants.append(plant)

            return plants

        except Exception as e:
            logger.error(f"Error searching plants: {e}")
            return []

    def get_plant_image(self, image_id: str) -> bytes:
        """Retrieve plant image from GridFS"""
        try:
            import gridfs
            from bson import ObjectId

            fs = gridfs.GridFS(self.db)

            image_file = fs.get(ObjectId(image_id))
            return image_file.read()

        except Exception as e:
            logger.error(f"Error retrieving plant image: {e}")
            return None

    def get_learning_statistics(self) -> dict:
        """Get statistics about the learning database"""
        try:
            total_plants = self.plants_collection.count_documents({})
            auto_added = self.plants_collection.count_documents({'auto_added': True})
            verified = self.plants_collection.count_documents({'expert_verified': True})

            # Most identified plants
            pipeline = [
                {'$sort': {'identification_count': -1}},
                {'$limit': 10},
                {'$project': {
                    'plant_name': 1,
                    'scientific_name': 1,
                    'identification_count': 1
                }}
            ]

            top_plants = list(self.plants_collection.aggregate(pipeline))

            # Recent additions
            recent = list(self.plants_collection.find(
                {},
                {'plant_name': 1, 'scientific_name': 1, 'created_date': 1}
            ).sort('created_date', -1).limit(5))

            return {
                'total_plants': total_plants,
                'auto_added_plants': auto_added,
                'verified_plants': verified,
                'verification_rate': verified / total_plants if total_plants > 0 else 0,
                'top_identified_plants': top_plants,
                'recent_additions': recent
            }

        except Exception as e:
            logger.error(f"Error getting learning statistics: {e}")
            return {}

    def update_plant_verification(self, plant_id: str, verified: bool, expert_notes: str = None) -> bool:
        """Update plant verification status"""
        try:
            from bson import ObjectId

            update_data = {
                'expert_verified': verified,
                'verification_date': datetime.utcnow()
            }

            if expert_notes:
                update_data['expert_notes'] = expert_notes

            result = self.plants_collection.update_one(
                {'_id': ObjectId(plant_id)},
                {'$set': update_data}
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating plant verification: {e}")
            return False

    def close_connection(self):
        """Close database connection"""
        try:
            if self.client:
                self.client.close()
                self.connected = False
                logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing connection: {e}")

# Global database instance
db_manager = MedicinalPlantDB()

def get_db():
    """Get database manager instance"""
    return db_manager
