"""
API Routes for Plant Identification System
RESTful endpoints following PlantNet API design
"""

import os
import uuid
import time
import logging
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

from .preprocessing import ImagePreprocessor
from .database import PlantDatabase
from ..models.inference import PlantInferenceEngine

logger = logging.getLogger(__name__)

# Create API blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

# Global instances (will be initialized in app factory)
inference_engine = None
plant_db = None
image_preprocessor = None

def init_api_components(app):
    """Initialize API components with app context"""
    global inference_engine, plant_db, image_preprocessor
    
    try:
        # Initialize inference engine
        model_path = app.config.get('MODEL_PATH') / app.config.get('MODEL_NAME')
        if model_path.exists():
            inference_engine = PlantInferenceEngine(str(model_path))
            inference_engine.warm_up()
            logger.info("✅ Inference engine initialized")
        else:
            logger.warning(f"⚠️ Model not found at {model_path}")
        
        # Initialize database
        plant_db = PlantDatabase(app.config.get('MONGODB_URI'))
        logger.info("✅ Plant database initialized")
        
        # Initialize image preprocessor
        image_preprocessor = ImagePreprocessor(
            target_size=app.config.get('IMAGE_SIZE'),
            allowed_extensions=app.config.get('ALLOWED_EXTENSIONS')
        )
        logger.info("✅ Image preprocessor initialized")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize API components: {e}")

@api_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'components': {
            'inference_engine': inference_engine is not None,
            'database': plant_db is not None and plant_db.is_connected(),
            'preprocessor': image_preprocessor is not None
        }
    })

@api_bp.route('/identify', methods=['POST'])
def identify_plant():
    """
    Main plant identification endpoint
    
    Expected request format:
    - Content-Type: multipart/form-data
    - images: List of image files
    - organs: List of organ types (corresponding to images)
    - project: Geographic scope (optional, default: 'world-flora')
    """
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    try:
        # Validate components
        if not inference_engine:
            return jsonify({
                'success': False,
                'error': 'Inference engine not available',
                'request_id': request_id
            }), 503
        
        # Validate request
        if 'images' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No images provided',
                'request_id': request_id
            }), 400
        
        # Get uploaded files
        uploaded_files = request.files.getlist('images')
        if not uploaded_files or all(f.filename == '' for f in uploaded_files):
            return jsonify({
                'success': False,
                'error': 'No valid images uploaded',
                'request_id': request_id
            }), 400
        
        # Get organ types
        organs = request.form.getlist('organs')
        if len(organs) != len(uploaded_files):
            return jsonify({
                'success': False,
                'error': 'Number of organs must match number of images',
                'request_id': request_id
            }), 400
        
        # Get project scope
        project = request.form.get('project', 'world-flora')
        
        # Validate and preprocess images
        processed_images = []
        valid_organs = []
        
        for file, organ in zip(uploaded_files, organs):
            try:
                # Validate file
                if not image_preprocessor.is_valid_file(file):
                    continue
                
                # Validate organ type
                if organ not in current_app.config.get('SUPPORTED_ORGANS', []):
                    logger.warning(f"⚠️ Unknown organ type: {organ}")
                    organ = 'whole_plant'  # Default fallback
                
                # Preprocess image
                processed_image = image_preprocessor.preprocess_uploaded_file(file)
                processed_images.append(processed_image)
                valid_organs.append(organ)
                
            except Exception as e:
                logger.error(f"❌ Failed to process image {file.filename}: {e}")
                continue
        
        if not processed_images:
            return jsonify({
                'success': False,
                'error': 'No valid images could be processed',
                'request_id': request_id
            }), 400
        
        # Perform plant identification
        if len(processed_images) == 1:
            # Single image prediction
            predictions = inference_engine.predict_single(
                processed_images[0], 
                valid_organs[0],
                top_k=current_app.config.get('TOP_K_PREDICTIONS', 10)
            )
        else:
            # Multi-image prediction with fusion
            predictions = inference_engine.predict_multi_organ(
                processed_images,
                valid_organs,
                fusion_method='weighted_average'
            )
        
        # Enhance predictions with database information
        enhanced_predictions = []
        for pred in predictions:
            enhanced_pred = pred.copy()
            
            # Get plant information from database
            if plant_db:
                plant_info = plant_db.get_plant_by_scientific_name(
                    pred.get('scientific_name', '')
                )
                if plant_info:
                    enhanced_pred.update({
                        'plant_id': plant_info.get('plant_id'),
                        'common_name': plant_info.get('common_names', {}).get('english', ''),
                        'family': plant_info.get('family', ''),
                        'reference_image': plant_info.get('gallery', {}).get(f"{valid_organs[0]}_images", [None])[0]
                    })
            
            enhanced_predictions.append(enhanced_pred)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Prepare response
        response = {
            'success': True,
            'message': 'Plant identification completed successfully',
            'data': {
                'predictions': enhanced_predictions,
                'processing_time': round(processing_time, 3),
                'model_version': '1.0.0',
                'organs_detected': valid_organs
            },
            'metadata': {
                'timestamp': datetime.utcnow().isoformat(),
                'request_id': request_id,
                'images_processed': len(processed_images),
                'project': project
            }
        }
        
        # Log successful identification
        logger.info(f"✅ Plant identification completed: {request_id} ({processing_time:.3f}s)")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"❌ Plant identification failed: {e}")
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'request_id': request_id
        }), 500

@api_bp.route('/species/<plant_id>', methods=['GET'])
def get_species_info(plant_id: str):
    """Get detailed information about a specific plant species"""
    try:
        if not plant_db:
            return jsonify({
                'success': False,
                'error': 'Database not available'
            }), 503
        
        # Get plant information
        plant_info = plant_db.get_plant_by_id(plant_id)
        
        if not plant_info:
            return jsonify({
                'success': False,
                'error': 'Plant species not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': plant_info
        })
        
    except Exception as e:
        logger.error(f"❌ Failed to get species info: {e}")
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }), 500

@api_bp.route('/search', methods=['GET'])
def search_plants():
    """Search plants by name or characteristics"""
    try:
        if not plant_db:
            return jsonify({
                'success': False,
                'error': 'Database not available'
            }), 503
        
        # Get search parameters
        query = request.args.get('q', '').strip()
        limit = min(int(request.args.get('limit', 20)), 100)  # Max 100 results
        
        if not query:
            return jsonify({
                'success': False,
                'error': 'Search query is required'
            }), 400
        
        # Perform search
        results = plant_db.search_plants(query, limit=limit)
        
        return jsonify({
            'success': True,
            'data': {
                'results': results,
                'total': len(results),
                'query': query,
                'limit': limit
            }
        })
        
    except Exception as e:
        logger.error(f"❌ Plant search failed: {e}")
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }), 500

@api_bp.route('/organs', methods=['GET'])
def get_supported_organs():
    """Get list of supported plant organs"""
    return jsonify({
        'success': True,
        'data': {
            'organs': current_app.config.get('SUPPORTED_ORGANS', []),
            'descriptions': {
                'leaf': 'Leaf or leaves of the plant',
                'flower': 'Flower or inflorescence',
                'fruit': 'Fruit or seed structure',
                'bark': 'Bark or stem surface',
                'stem': 'Stem or branch',
                'whole_plant': 'Entire plant or habit'
            }
        }
    })

@api_bp.route('/projects', methods=['GET'])
def get_supported_projects():
    """Get list of supported geographic projects"""
    return jsonify({
        'success': True,
        'data': {
            'projects': current_app.config.get('SUPPORTED_PROJECTS', []),
            'descriptions': {
                'world-flora': 'Global plant identification',
                'medicinal-plants': 'Medicinal and therapeutic plants',
                'indian-flora': 'Plants of the Indian subcontinent',
                'european-flora': 'European plant species'
            }
        }
    })

@api_bp.route('/stats', methods=['GET'])
def get_system_stats():
    """Get system statistics and performance metrics"""
    try:
        stats = {
            'system': {
                'uptime': time.time(),
                'version': '1.0.0',
                'model_loaded': inference_engine is not None,
                'database_connected': plant_db is not None and plant_db.is_connected()
            }
        }
        
        # Add inference engine stats
        if inference_engine:
            stats['inference'] = inference_engine.get_performance_stats()
        
        # Add database stats
        if plant_db:
            stats['database'] = plant_db.get_statistics()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"❌ Failed to get system stats: {e}")
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }), 500

@api_bp.errorhandler(413)
def file_too_large(error):
    """Handle file too large error"""
    return jsonify({
        'success': False,
        'error': 'File too large. Maximum size is 16MB per file.'
    }), 413

@api_bp.errorhandler(415)
def unsupported_media_type(error):
    """Handle unsupported media type error"""
    return jsonify({
        'success': False,
        'error': 'Unsupported file type. Please upload images in JPG, PNG, or WebP format.'
    }), 415
