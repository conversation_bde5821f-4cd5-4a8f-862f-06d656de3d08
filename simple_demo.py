"""
Simple Demo for Medicinal Plant Recognition System
A basic version that works with minimal dependencies
"""

import os
import sys
from plant_database import get_plant_info, get_all_plant_names, search_plants_by_use

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("🌿 Medicinal Plant Recognition System - Simple Demo")
    print("=" * 60)
    print()

def demo_plant_database():
    """Demonstrate the plant database"""
    print("📚 Plant Database Demo")
    print("-" * 30)
    
    # Get all plants
    all_plants = get_all_plant_names()
    print(f"📊 Total plants in database: {len(all_plants)}")
    print()
    
    # Show some popular medicinal plants
    popular_plants = [
        'Aloe vera', 'Ocimum tenuiflorum', 'Turmeric', 'Ashwagandha', 
        'neem', 'Basil', 'Amla', 'Tulsi', 'Sweet Basil'
    ]
    
    print("🌿 Popular Medicinal Plants:")
    print()
    
    for i, plant in enumerate(popular_plants, 1):
        if plant in all_plants:
            info = get_plant_info(plant)
            print(f"{i}. {plant}")
            print(f"   🔬 Scientific: {info['scientific_name']}")
            print(f"   🏠 Local: {info['local_name']}")
            print(f"   💊 Uses: {info['medicinal_uses']}")
            print(f"   📝 Description: {info['description']}")
            print()

def demo_search_functionality():
    """Demonstrate search functionality"""
    print("\n🔍 Search Functionality Demo")
    print("-" * 30)
    
    search_terms = [
        'diabetes', 'skin', 'respiratory', 'fever', 'digestive', 
        'immunity', 'pain', 'inflammation'
    ]
    
    for term in search_terms:
        results = search_plants_by_use(term)
        print(f"\n🔎 Plants for '{term}': {len(results)} found")
        
        # Show top 3 results
        for i, (plant_name, info) in enumerate(results[:3], 1):
            print(f"   {i}. {plant_name} - {info['local_name']}")

def interactive_search():
    """Interactive search feature"""
    print("\n🎯 Interactive Plant Search")
    print("-" * 30)
    print("Enter a health condition or medicinal use to find relevant plants")
    print("(Type 'quit' to exit)")
    print()
    
    while True:
        try:
            query = input("🔍 Search for: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                continue
            
            results = search_plants_by_use(query)
            
            if results:
                print(f"\n✅ Found {len(results)} plants for '{query}':")
                print()
                
                for i, (plant_name, info) in enumerate(results[:5], 1):
                    print(f"{i}. 🌿 {plant_name}")
                    print(f"   Scientific: {info['scientific_name']}")
                    print(f"   Local: {info['local_name']}")
                    print(f"   Uses: {info['medicinal_uses'][:80]}...")
                    print()
                
                if len(results) > 5:
                    print(f"   ... and {len(results) - 5} more plants")
            else:
                print(f"❌ No plants found for '{query}'")
                print("💡 Try terms like: diabetes, skin, fever, cough, etc.")
            
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def plant_info_lookup():
    """Look up specific plant information"""
    print("\n📖 Plant Information Lookup")
    print("-" * 30)
    print("Enter a plant name to get detailed information")
    print("(Type 'list' to see all plants, 'quit' to exit)")
    print()
    
    all_plants = get_all_plant_names()
    
    while True:
        try:
            plant_name = input("🌿 Plant name: ").strip()
            
            if plant_name.lower() in ['quit', 'exit', 'q']:
                break
            
            if plant_name.lower() == 'list':
                print(f"\n📋 Available plants ({len(all_plants)} total):")
                for i, plant in enumerate(sorted(all_plants), 1):
                    print(f"{i:3d}. {plant}")
                    if i % 20 == 0:  # Pause every 20 items
                        input("\nPress Enter to continue...")
                print()
                continue
            
            if not plant_name:
                continue
            
            # Try exact match first
            if plant_name in all_plants:
                info = get_plant_info(plant_name)
            else:
                # Try partial match
                matches = [p for p in all_plants if plant_name.lower() in p.lower()]
                if matches:
                    plant_name = matches[0]
                    info = get_plant_info(plant_name)
                    print(f"📍 Found: {plant_name}")
                else:
                    print(f"❌ Plant '{plant_name}' not found")
                    print("💡 Try 'list' to see all available plants")
                    continue
            
            print(f"\n🌿 {plant_name}")
            print("=" * (len(plant_name) + 4))
            print(f"🔬 Scientific Name: {info['scientific_name']}")
            print(f"🏠 Local Name: {info['local_name']}")
            print(f"💊 Medicinal Uses: {info['medicinal_uses']}")
            print(f"📝 Description: {info['description']}")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main_menu():
    """Main menu for the demo"""
    while True:
        print("\n🌿 Medicinal Plant Recognition System")
        print("=" * 40)
        print("1. 📚 View Plant Database Demo")
        print("2. 🔍 Search Demo")
        print("3. 🎯 Interactive Search")
        print("4. 📖 Plant Information Lookup")
        print("5. 🚪 Exit")
        print()
        
        try:
            choice = input("Select an option (1-5): ").strip()
            
            if choice == '1':
                demo_plant_database()
            elif choice == '2':
                demo_search_functionality()
            elif choice == '3':
                interactive_search()
            elif choice == '4':
                plant_info_lookup()
            elif choice == '5':
                print("👋 Thank you for using the Medicinal Plant Recognition System!")
                break
            else:
                print("❌ Invalid choice. Please select 1-5.")
            
            input("\nPress Enter to continue...")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main function"""
    print_banner()
    
    print("🎭 Running in Simple Demo Mode")
    print("💡 This demo showcases the plant database functionality")
    print("🔧 For full AI recognition, install PyTorch and train the model")
    print()
    
    # Check if database is available
    try:
        all_plants = get_all_plant_names()
        print(f"✅ Plant database loaded successfully ({len(all_plants)} species)")
    except Exception as e:
        print(f"❌ Error loading plant database: {e}")
        return
    
    # Start main menu
    main_menu()

if __name__ == "__main__":
    main()
