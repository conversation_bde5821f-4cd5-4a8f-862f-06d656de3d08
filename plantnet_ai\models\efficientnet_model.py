"""
EfficientNet-based Plant Classification Model
State-of-the-art CNN architecture for plant species identification
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, Model
from tensorflow.keras.applications import EfficientNetB2
import numpy as np
from typing import Tuple, List, Dict, Optional
import json
import logging

logger = logging.getLogger(__name__)

class EfficientNetPlantClassifier:
    """
    EfficientNet-based plant classification model with organ-aware architecture
    """
    
    def __init__(self, 
                 num_classes: int = 100,
                 input_shape: Tuple[int, int, int] = (224, 224, 3),
                 num_organs: int = 6,
                 dropout_rate: float = 0.3,
                 learning_rate: float = 0.001):
        """
        Initialize the EfficientNet plant classifier
        
        Args:
            num_classes: Number of plant species to classify
            input_shape: Input image shape (height, width, channels)
            num_organs: Number of plant organs (leaf, flower, fruit, etc.)
            dropout_rate: Dropout rate for regularization
            learning_rate: Learning rate for optimizer
        """
        self.num_classes = num_classes
        self.input_shape = input_shape
        self.num_organs = num_organs
        self.dropout_rate = dropout_rate
        self.learning_rate = learning_rate
        self.model = None
        self.history = None
        
    def build_model(self) -> Model:
        """
        Build the EfficientNet-based model with organ awareness
        
        Returns:
            Compiled Keras model
        """
        # Image input
        image_input = keras.Input(shape=self.input_shape, name='image_input')
        
        # Organ type input (one-hot encoded)
        organ_input = keras.Input(shape=(self.num_organs,), name='organ_input')
        
        # EfficientNet backbone (pre-trained on ImageNet)
        backbone = EfficientNetB2(
            weights='imagenet',
            include_top=False,
            input_tensor=image_input,
            pooling='avg'
        )
        
        # Freeze early layers for transfer learning
        for layer in backbone.layers[:-20]:
            layer.trainable = False
            
        # Extract features from backbone
        image_features = backbone.output
        
        # Image feature processing
        x = layers.Dense(512, activation='relu', name='image_dense_1')(image_features)
        x = layers.Dropout(self.dropout_rate, name='image_dropout_1')(x)
        x = layers.Dense(256, activation='relu', name='image_dense_2')(x)
        x = layers.Dropout(self.dropout_rate, name='image_dropout_2')(x)
        
        # Organ feature processing
        organ_features = layers.Dense(64, activation='relu', name='organ_dense_1')(organ_input)
        organ_features = layers.Dropout(0.2, name='organ_dropout_1')(organ_features)
        organ_features = layers.Dense(32, activation='relu', name='organ_dense_2')(organ_features)
        
        # Combine image and organ features
        combined_features = layers.Concatenate(name='feature_fusion')([x, organ_features])
        
        # Final classification layers
        combined = layers.Dense(512, activation='relu', name='combined_dense_1')(combined_features)
        combined = layers.Dropout(self.dropout_rate, name='combined_dropout_1')(combined)
        combined = layers.Dense(256, activation='relu', name='combined_dense_2')(combined)
        combined = layers.Dropout(self.dropout_rate, name='combined_dropout_2')(combined)
        
        # Output layer with softmax for probability distribution
        predictions = layers.Dense(
            self.num_classes, 
            activation='softmax', 
            name='species_predictions'
        )(combined)
        
        # Create model
        model = Model(
            inputs=[image_input, organ_input],
            outputs=predictions,
            name='EfficientNet_Plant_Classifier'
        )
        
        # Compile model
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=self.learning_rate),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_k_categorical_accuracy']
        )
        
        self.model = model
        logger.info(f"✅ EfficientNet model built successfully with {self.num_classes} classes")
        
        return model
    
    def get_model_summary(self) -> str:
        """Get model architecture summary"""
        if self.model is None:
            self.build_model()
        
        summary_lines = []
        self.model.summary(print_fn=lambda x: summary_lines.append(x))
        return '\n'.join(summary_lines)
    
    def save_model(self, filepath: str) -> None:
        """
        Save the trained model
        
        Args:
            filepath: Path to save the model
        """
        if self.model is None:
            raise ValueError("Model not built yet. Call build_model() first.")
            
        self.model.save(filepath)
        logger.info(f"✅ Model saved to {filepath}")
        
        # Save model configuration
        config = {
            'num_classes': self.num_classes,
            'input_shape': self.input_shape,
            'num_organs': self.num_organs,
            'dropout_rate': self.dropout_rate,
            'learning_rate': self.learning_rate
        }
        
        config_path = filepath.replace('.h5', '_config.json')
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"✅ Model configuration saved to {config_path}")
    
    def load_model(self, filepath: str) -> Model:
        """
        Load a pre-trained model
        
        Args:
            filepath: Path to the saved model
            
        Returns:
            Loaded Keras model
        """
        try:
            self.model = keras.models.load_model(filepath)
            logger.info(f"✅ Model loaded from {filepath}")
            
            # Load configuration if available
            config_path = filepath.replace('.h5', '_config.json')
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    self.num_classes = config['num_classes']
                    self.input_shape = tuple(config['input_shape'])
                    self.num_organs = config['num_organs']
                    self.dropout_rate = config['dropout_rate']
                    self.learning_rate = config['learning_rate']
                logger.info(f"✅ Model configuration loaded from {config_path}")
            except FileNotFoundError:
                logger.warning(f"⚠️ Configuration file not found: {config_path}")
                
            return self.model
            
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            raise
    
    def predict(self, 
                images: np.ndarray, 
                organs: np.ndarray, 
                top_k: int = 10) -> List[Dict]:
        """
        Make predictions on input images
        
        Args:
            images: Batch of preprocessed images
            organs: Batch of one-hot encoded organ types
            top_k: Number of top predictions to return
            
        Returns:
            List of prediction dictionaries with confidence scores
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() or build_model() first.")
        
        # Get predictions
        predictions = self.model.predict([images, organs])
        
        results = []
        for i, pred in enumerate(predictions):
            # Get top-k predictions
            top_indices = np.argsort(pred)[-top_k:][::-1]
            top_confidences = pred[top_indices]
            
            batch_results = []
            for idx, confidence in zip(top_indices, top_confidences):
                batch_results.append({
                    'class_index': int(idx),
                    'confidence': float(confidence)
                })
            
            results.append(batch_results)
        
        return results
    
    def fine_tune(self, unfreeze_layers: int = 50) -> None:
        """
        Fine-tune the model by unfreezing more layers
        
        Args:
            unfreeze_layers: Number of layers to unfreeze from the end
        """
        if self.model is None:
            raise ValueError("Model not built yet. Call build_model() first.")
        
        # Unfreeze more layers for fine-tuning
        backbone = None
        for layer in self.model.layers:
            if 'efficientnet' in layer.name.lower():
                backbone = layer
                break
        
        if backbone:
            for layer in backbone.layers[-unfreeze_layers:]:
                layer.trainable = True
            
            # Recompile with lower learning rate for fine-tuning
            self.model.compile(
                optimizer=keras.optimizers.Adam(learning_rate=self.learning_rate * 0.1),
                loss='categorical_crossentropy',
                metrics=['accuracy', 'top_k_categorical_accuracy']
            )
            
            logger.info(f"✅ Model fine-tuning enabled: {unfreeze_layers} layers unfrozen")
    
    def get_feature_extractor(self) -> Model:
        """
        Get a feature extractor model (without classification head)
        
        Returns:
            Feature extraction model
        """
        if self.model is None:
            raise ValueError("Model not built yet. Call build_model() first.")
        
        # Find the feature fusion layer
        feature_layer = None
        for layer in self.model.layers:
            if layer.name == 'feature_fusion':
                feature_layer = layer
                break
        
        if feature_layer is None:
            raise ValueError("Feature fusion layer not found in model")
        
        feature_extractor = Model(
            inputs=self.model.inputs,
            outputs=feature_layer.output,
            name='Plant_Feature_Extractor'
        )
        
        return feature_extractor

def create_organ_encoding(organ_name: str, supported_organs: List[str]) -> np.ndarray:
    """
    Create one-hot encoding for organ type
    
    Args:
        organ_name: Name of the plant organ
        supported_organs: List of supported organ names
        
    Returns:
        One-hot encoded array
    """
    encoding = np.zeros(len(supported_organs))
    
    if organ_name in supported_organs:
        idx = supported_organs.index(organ_name)
        encoding[idx] = 1.0
    else:
        logger.warning(f"⚠️ Unknown organ type: {organ_name}")
        # Default to 'whole_plant' if available
        if 'whole_plant' in supported_organs:
            idx = supported_organs.index('whole_plant')
            encoding[idx] = 1.0
    
    return encoding

if __name__ == '__main__':
    # Test model creation
    model = EfficientNetPlantClassifier(num_classes=50)
    model.build_model()
    print("✅ EfficientNet model test successful!")
    print(f"Model parameters: {model.model.count_params():,}")
    print("\nModel summary:")
    print(model.get_model_summary())
