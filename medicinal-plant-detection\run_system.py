#!/usr/bin/env python3
"""
Quick start script for Medicinal Plant Recognition System
This script provides an easy way to run the system without manual setup
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    """Print startup banner"""
    print("🌿" * 50)
    print("🌿 MEDICINAL PLANT RECOGNITION SYSTEM")
    print("🌿 Quick Start Script")
    print("🌿" * 50)
    print()

def check_dependencies():
    """Check if required packages are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'flask',
        'flask_cors',
        'ultralytics',
        'opencv-python',
        'pillow',
        'pymongo',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ All packages installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install packages. Please run:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def setup_directories():
    """Create necessary directories"""
    print("📁 Setting up directories...")
    
    directories = [
        'backend/uploads',
        'backend/logs',
        'dataset/train/images',
        'dataset/train/labels',
        'dataset/valid/images',
        'dataset/valid/labels',
        'runs/detect/train/weights'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")

def download_model():
    """Download YOLOv8 model if not present"""
    print("🤖 Checking YOLOv8 model...")
    
    try:
        from ultralytics import YOLO
        
        # This will download the model if not present
        model = YOLO('yolov8n.pt')
        print("✅ YOLOv8 model ready")
        return True
    except Exception as e:
        print(f"❌ Error with model: {e}")
        return False

def start_server():
    """Start the Flask server"""
    print("🚀 Starting Flask server...")
    
    # Change to backend directory
    backend_dir = Path(__file__).parent / 'backend'
    
    try:
        # Start the Flask app
        env = os.environ.copy()
        env['PYTHONPATH'] = str(Path(__file__).parent)
        
        process = subprocess.Popen([
            sys.executable, 'app.py'
        ], cwd=backend_dir, env=env)
        
        print("✅ Server starting...")
        print("🌐 Server will be available at: http://127.0.0.1:5000")
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Try to open browser
        try:
            webbrowser.open('http://127.0.0.1:5000')
            print("🌍 Browser opened automatically")
        except:
            print("🌍 Please open your browser and go to: http://127.0.0.1:5000")
        
        print("\n📋 System is ready!")
        print("📸 Upload a plant image to test the system")
        print("⌨️ Press Ctrl+C to stop the server")
        
        # Wait for the process
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping server...")
            process.terminate()
            process.wait()
            print("✅ Server stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return False

def main():
    """Main function"""
    print_banner()
    
    # Check if we're in the right directory
    if not Path('backend/app.py').exists():
        print("❌ Please run this script from the medicinal-plant-detection directory")
        print("Current directory should contain: backend/, frontend/, dataset/")
        return False
    
    steps = [
        ("Checking dependencies", check_dependencies),
        ("Setting up directories", setup_directories),
        ("Downloading model", download_model),
        ("Starting server", start_server)
    ]
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        
        if not step_function():
            print(f"❌ Failed at: {step_name}")
            return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
