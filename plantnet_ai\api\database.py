"""
Plant Database Management System
MongoDB-based storage for comprehensive plant information
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import json

from pymongo import MongoClient, ASCENDING, TEXT
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
import gridfs
from bson import ObjectId

logger = logging.getLogger(__name__)

class PlantDatabase:
    """
    Comprehensive plant database management system
    Handles plant information, images, and user interactions
    """
    
    def __init__(self, mongodb_uri: str, database_name: str = 'plantnet_ai'):
        """
        Initialize the plant database
        
        Args:
            mongodb_uri: MongoDB connection URI
            database_name: Name of the database
        """
        self.mongodb_uri = mongodb_uri
        self.database_name = database_name
        self.client = None
        self.db = None
        self.fs = None
        
        # Collections
        self.plants_collection = None
        self.identifications_collection = None
        self.user_feedback_collection = None
        
        # Connect to database
        self._connect()
        
        # Initialize collections and indexes
        self._initialize_collections()
        
        logger.info(f"✅ PlantDatabase initialized for {database_name}")
    
    def _connect(self) -> None:
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(
                self.mongodb_uri,
                serverSelectionTimeoutMS=5000,  # 5 second timeout
                connectTimeoutMS=10000,  # 10 second connection timeout
                socketTimeoutMS=20000,   # 20 second socket timeout
            )
            
            # Test connection
            self.client.admin.command('ping')
            
            self.db = self.client[self.database_name]
            self.fs = gridfs.GridFS(self.db)
            
            logger.info("✅ Connected to MongoDB")
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"❌ Failed to connect to MongoDB: {e}")
            raise
    
    def _initialize_collections(self) -> None:
        """Initialize collections and create indexes"""
        try:
            # Plants collection
            self.plants_collection = self.db.plants
            
            # Create indexes for plants
            self.plants_collection.create_index([('scientific_name', ASCENDING)], unique=True)
            self.plants_collection.create_index([('plant_id', ASCENDING)], unique=True)
            self.plants_collection.create_index([
                ('scientific_name', TEXT),
                ('common_names.english', TEXT),
                ('common_names.hindi', TEXT),
                ('family', TEXT)
            ])
            
            # Identifications collection (for tracking API usage)
            self.identifications_collection = self.db.identifications
            self.identifications_collection.create_index([('timestamp', ASCENDING)])
            self.identifications_collection.create_index([('request_id', ASCENDING)])
            
            # User feedback collection (for community verification)
            self.user_feedback_collection = self.db.user_feedback
            self.user_feedback_collection.create_index([('plant_id', ASCENDING)])
            self.user_feedback_collection.create_index([('timestamp', ASCENDING)])
            
            logger.info("✅ Database collections and indexes initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize collections: {e}")
            raise
    
    def is_connected(self) -> bool:
        """Check if database is connected"""
        try:
            if self.client:
                self.client.admin.command('ping')
                return True
            return False
        except:
            return False
    
    def insert_plant(self, plant_data: Dict) -> str:
        """
        Insert a new plant into the database
        
        Args:
            plant_data: Plant information dictionary
            
        Returns:
            Inserted plant ID
        """
        try:
            # Add metadata
            plant_data['created_date'] = datetime.utcnow().isoformat()
            plant_data['updated_date'] = datetime.utcnow().isoformat()
            
            # Generate plant_id if not provided
            if 'plant_id' not in plant_data:
                plant_data['plant_id'] = str(ObjectId())
            
            # Insert plant
            result = self.plants_collection.insert_one(plant_data)
            
            logger.info(f"✅ Plant inserted: {plant_data.get('scientific_name')}")
            
            return plant_data['plant_id']
            
        except Exception as e:
            logger.error(f"❌ Failed to insert plant: {e}")
            raise
    
    def get_plant_by_id(self, plant_id: str) -> Optional[Dict]:
        """
        Get plant information by plant ID
        
        Args:
            plant_id: Plant identifier
            
        Returns:
            Plant information dictionary or None
        """
        try:
            plant = self.plants_collection.find_one({'plant_id': plant_id})
            
            if plant:
                # Remove MongoDB ObjectId for JSON serialization
                plant.pop('_id', None)
                return plant
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get plant by ID: {e}")
            return None
    
    def get_plant_by_scientific_name(self, scientific_name: str) -> Optional[Dict]:
        """
        Get plant information by scientific name
        
        Args:
            scientific_name: Scientific name of the plant
            
        Returns:
            Plant information dictionary or None
        """
        try:
            plant = self.plants_collection.find_one({'scientific_name': scientific_name})
            
            if plant:
                # Remove MongoDB ObjectId for JSON serialization
                plant.pop('_id', None)
                return plant
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get plant by scientific name: {e}")
            return None
    
    def search_plants(self, query: str, limit: int = 20) -> List[Dict]:
        """
        Search plants by text query
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching plants
        """
        try:
            # Text search
            cursor = self.plants_collection.find(
                {'$text': {'$search': query}},
                {'score': {'$meta': 'textScore'}}
            ).sort([('score', {'$meta': 'textScore'})]).limit(limit)
            
            results = []
            for plant in cursor:
                plant.pop('_id', None)  # Remove ObjectId
                results.append(plant)
            
            logger.debug(f"🔍 Plant search: '{query}' returned {len(results)} results")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Plant search failed: {e}")
            return []
    
    def update_plant(self, plant_id: str, update_data: Dict) -> bool:
        """
        Update plant information
        
        Args:
            plant_id: Plant identifier
            update_data: Data to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Add update timestamp
            update_data['updated_date'] = datetime.utcnow().isoformat()
            
            result = self.plants_collection.update_one(
                {'plant_id': plant_id},
                {'$set': update_data}
            )
            
            if result.modified_count > 0:
                logger.info(f"✅ Plant updated: {plant_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to update plant: {e}")
            return False
    
    def log_identification(self, identification_data: Dict) -> str:
        """
        Log an identification request for analytics
        
        Args:
            identification_data: Identification request data
            
        Returns:
            Log entry ID
        """
        try:
            # Add timestamp
            identification_data['timestamp'] = datetime.utcnow().isoformat()
            
            result = self.identifications_collection.insert_one(identification_data)
            
            logger.debug(f"📊 Identification logged: {identification_data.get('request_id')}")
            
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Failed to log identification: {e}")
            return ""
    
    def add_user_feedback(self, feedback_data: Dict) -> str:
        """
        Add user feedback for community verification
        
        Args:
            feedback_data: User feedback data
            
        Returns:
            Feedback entry ID
        """
        try:
            # Add timestamp
            feedback_data['timestamp'] = datetime.utcnow().isoformat()
            
            result = self.user_feedback_collection.insert_one(feedback_data)
            
            logger.info(f"✅ User feedback added for plant: {feedback_data.get('plant_id')}")
            
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ Failed to add user feedback: {e}")
            return ""
    
    def get_plant_feedback(self, plant_id: str) -> List[Dict]:
        """
        Get user feedback for a specific plant
        
        Args:
            plant_id: Plant identifier
            
        Returns:
            List of feedback entries
        """
        try:
            cursor = self.user_feedback_collection.find(
                {'plant_id': plant_id}
            ).sort('timestamp', -1)
            
            feedback_list = []
            for feedback in cursor:
                feedback.pop('_id', None)  # Remove ObjectId
                feedback_list.append(feedback)
            
            return feedback_list
            
        except Exception as e:
            logger.error(f"❌ Failed to get plant feedback: {e}")
            return []
    
    def get_statistics(self) -> Dict:
        """
        Get database statistics
        
        Returns:
            Statistics dictionary
        """
        try:
            stats = {
                'total_plants': self.plants_collection.count_documents({}),
                'verified_plants': self.plants_collection.count_documents({'verification.expert_verified': True}),
                'total_identifications': self.identifications_collection.count_documents({}),
                'total_feedback': self.user_feedback_collection.count_documents({}),
                'database_size_mb': self.db.command('dbStats')['dataSize'] / (1024 * 1024)
            }
            
            # Get recent activity
            recent_identifications = self.identifications_collection.count_documents({
                'timestamp': {'$gte': (datetime.utcnow().replace(hour=0, minute=0, second=0)).isoformat()}
            })
            stats['identifications_today'] = recent_identifications
            
            # Get top families
            pipeline = [
                {'$group': {'_id': '$family', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}},
                {'$limit': 10}
            ]
            top_families = list(self.plants_collection.aggregate(pipeline))
            stats['top_families'] = top_families
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get statistics: {e}")
            return {}
    
    def store_image(self, image_data: bytes, filename: str, metadata: Dict = None) -> str:
        """
        Store image in GridFS
        
        Args:
            image_data: Image binary data
            filename: Original filename
            metadata: Additional metadata
            
        Returns:
            GridFS file ID
        """
        try:
            file_id = self.fs.put(
                image_data,
                filename=filename,
                metadata=metadata or {}
            )
            
            logger.debug(f"💾 Image stored: {filename}")
            
            return str(file_id)
            
        except Exception as e:
            logger.error(f"❌ Failed to store image: {e}")
            return ""
    
    def get_image(self, file_id: str) -> Optional[bytes]:
        """
        Retrieve image from GridFS
        
        Args:
            file_id: GridFS file ID
            
        Returns:
            Image binary data or None
        """
        try:
            file_obj = self.fs.get(ObjectId(file_id))
            return file_obj.read()
            
        except Exception as e:
            logger.error(f"❌ Failed to get image: {e}")
            return None
    
    def close_connection(self) -> None:
        """Close database connection"""
        try:
            if self.client:
                self.client.close()
                logger.info("✅ Database connection closed")
        except Exception as e:
            logger.error(f"❌ Error closing database connection: {e}")

if __name__ == '__main__':
    # Test database connection
    try:
        db = PlantDatabase('mongodb://localhost:27017/plantnet_ai_test')
        print("✅ PlantDatabase test successful!")
        print(f"Connected: {db.is_connected()}")
        print(f"Statistics: {db.get_statistics()}")
        db.close_connection()
    except Exception as e:
        print(f"⚠️ Database test failed: {e}")
