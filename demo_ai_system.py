#!/usr/bin/env python3
"""
🌱 AI Medicinal Plant Recognition System - Demo Version
Self-learning system that simulates plant identification and dataset generation
Works without PyTorch/YOLO dependencies for demonstration
"""

import os
import json
import yaml
import random
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import shutil

# Basic libraries (available in current environment)
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import requests

# Web framework
from flask import Flask, request, jsonify, render_template_string
from werkzeug.utils import secure_filename

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/plant_ai_demo.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DemoMedicinalPlantAI:
    """
    🧠 Demo Self-Learning Medicinal Plant Recognition System
    Simulates the complete AI pipeline without heavy dependencies
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the demo AI system"""
        self.config = self.load_config(config_path)
        self.setup_directories()
        
        # Mock plant database
        self.known_plants = self.load_known_plants()
        self.plant_families = [
            "Asphodelaceae", "Lamiaceae", "Meliaceae", "Zingiberaceae", 
            "Fabaceae", "Rosaceae", "Asteraceae", "Solanaceae"
        ]
        
        logger.info("🌱 Demo MedicinalPlantAI system initialized successfully")
    
    def load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        default_config = {
            'confidence_threshold': 0.7,
            'images_per_plant': 50,  # Reduced for demo
            'synthetic_images': 10,   # Reduced for demo
            'known_plant_threshold': 0.8
        }
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = yaml.safe_load(f)
                    if user_config:
                        default_config.update(user_config)
            except Exception as e:
                logger.warning(f"⚠️ Could not load config: {e}")
        
        return default_config
    
    def setup_directories(self):
        """Create necessary directories"""
        directories = [
            'dataset/train/images', 'dataset/train/labels',
            'dataset/val/images', 'dataset/val/labels',
            'new_plants', 'models', 'logs', 'uploads', 
            'generated_images', 'temp'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def load_known_plants(self) -> Dict:
        """Load known plants database"""
        return {
            "aloe_vera": {
                "common_name": "Aloe Vera",
                "scientific_name": "Aloe vera",
                "local_name": "Ghritkumari",
                "family": "Asphodelaceae",
                "medicinal_uses": ["Skin healing", "Digestive health", "Anti-inflammatory", "Wound healing"],
                "description": "Succulent plant known for its healing gel and medicinal properties",
                "habitat": "Arid regions, cultivated worldwide",
                "parts_used": "Leaves (gel and latex)",
                "preparation": "Fresh gel applied topically, juice consumed orally",
                "precautions": "May cause allergic reactions in some individuals"
            },
            "tulsi": {
                "common_name": "Holy Basil",
                "scientific_name": "Ocimum tenuiflorum",
                "local_name": "Tulsi",
                "family": "Lamiaceae",
                "medicinal_uses": ["Respiratory health", "Stress relief", "Immunity booster", "Fever reduction"],
                "description": "Sacred plant in Hindu tradition with numerous health benefits",
                "habitat": "Tropical and subtropical regions",
                "parts_used": "Leaves, seeds, roots",
                "preparation": "Tea, powder, fresh leaves",
                "precautions": "May interact with blood-thinning medications"
            },
            "neem": {
                "common_name": "Neem",
                "scientific_name": "Azadirachta indica",
                "local_name": "Neem",
                "family": "Meliaceae",
                "medicinal_uses": ["Antibacterial", "Antifungal", "Skin conditions", "Dental health"],
                "description": "Versatile medicinal tree with powerful antimicrobial properties",
                "habitat": "Native to Indian subcontinent",
                "parts_used": "Leaves, bark, seeds, oil",
                "preparation": "Oil, powder, decoction, paste",
                "precautions": "Avoid during pregnancy and breastfeeding"
            },
            "turmeric": {
                "common_name": "Turmeric",
                "scientific_name": "Curcuma longa",
                "local_name": "Haldi",
                "family": "Zingiberaceae",
                "medicinal_uses": ["Anti-inflammatory", "Antioxidant", "Digestive health", "Joint pain"],
                "description": "Golden spice with powerful anti-inflammatory compounds",
                "habitat": "Tropical regions of Asia",
                "parts_used": "Rhizome (underground stem)",
                "preparation": "Powder, fresh root, extract",
                "precautions": "May increase bleeding risk, avoid with gallstones"
            },
            "ginger": {
                "common_name": "Ginger",
                "scientific_name": "Zingiber officinale",
                "local_name": "Adrak",
                "family": "Zingiberaceae",
                "medicinal_uses": ["Digestive aid", "Anti-nausea", "Anti-inflammatory", "Cold relief"],
                "description": "Aromatic root used for digestive and respiratory health",
                "habitat": "Tropical regions, widely cultivated",
                "parts_used": "Fresh or dried rhizome",
                "preparation": "Tea, powder, fresh slices, oil",
                "precautions": "May interact with blood thinners"
            }
        }
    
    def simulate_yolo_detection(self, image_path: str) -> Tuple[bool, Dict]:
        """
        🔍 Simulate YOLO detection for demo purposes
        """
        logger.info(f"🔍 Simulating YOLO detection on {image_path}")
        
        # Simulate processing time
        time.sleep(random.uniform(0.5, 1.5))
        
        # Random chance of detecting a known plant (70% chance)
        if random.random() < 0.7:
            # Randomly select a known plant
            plant_key = random.choice(list(self.known_plants.keys()))
            plant_info = self.known_plants[plant_key]
            confidence = random.uniform(0.75, 0.95)
            
            detection_info = {
                "detected": True,
                "class_name": plant_key,
                "confidence": confidence,
                "bbox": [100, 100, 400, 400],  # Mock bounding box
                "plant_info": plant_info
            }
            
            logger.info(f"✅ Plant detected: {plant_info['common_name']} (confidence: {confidence:.2f})")
            return True, detection_info
        else:
            logger.info("❓ No known plants detected - will use AI identification")
            return False, {"detected": False, "reason": "No plants detected above threshold"}
    
    def simulate_ai_plant_identification(self, image_path: str) -> Dict:
        """
        🧠 Simulate AI plant identification using mock data
        """
        logger.info(f"🧠 Simulating AI plant identification for {image_path}")
        
        # Simulate AI processing time
        time.sleep(random.uniform(2.0, 4.0))
        
        # Generate random plant information
        plant_names = [
            "Ashwagandha", "Brahmi", "Giloy", "Amla", "Fenugreek",
            "Mint", "Coriander", "Curry Leaves", "Lemon Grass", "Hibiscus",
            "Marigold", "Jasmine", "Eucalyptus", "Banyan", "Mango"
        ]
        
        scientific_prefixes = ["Withania", "Bacopa", "Tinospora", "Phyllanthus", "Trigonella",
                              "Mentha", "Coriandrum", "Murraya", "Cymbopogon", "Hibiscus"]
        
        local_names = ["Ashwagandha", "Brahmi", "Giloy", "Amla", "Methi",
                      "Pudina", "Dhania", "Kadi Patta", "Lemon Grass", "Japa"]
        
        medicinal_uses_options = [
            ["Stress relief", "Energy booster", "Immunity enhancer"],
            ["Memory enhancement", "Cognitive function", "Anxiety relief"],
            ["Fever reduction", "Immunity booster", "Liver health"],
            ["Antioxidant", "Vitamin C source", "Hair health"],
            ["Digestive health", "Blood sugar control", "Lactation support"],
            ["Digestive aid", "Respiratory health", "Cooling effect"],
            ["Digestive health", "Antioxidant", "Anti-inflammatory"],
            ["Digestive aid", "Hair health", "Antioxidant"],
            ["Digestive health", "Antimicrobial", "Stress relief"],
            ["Heart health", "Blood pressure", "Antioxidant"]
        ]
        
        # Randomly select plant characteristics
        idx = random.randint(0, len(plant_names) - 1)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        plant_info = {
            "common_name": plant_names[idx],
            "scientific_name": f"{scientific_prefixes[idx]} {plant_names[idx].lower()}",
            "local_name": local_names[idx],
            "family": random.choice(self.plant_families),
            "medicinal_uses": medicinal_uses_options[idx],
            "description": f"Medicinal plant with therapeutic properties. Identified on {datetime.now().strftime('%Y-%m-%d')}",
            "habitat": random.choice(["Tropical regions", "Subtropical areas", "Temperate zones", "Arid regions"]),
            "parts_used": random.choice(["Leaves", "Roots", "Flowers", "Seeds", "Bark", "Whole plant"]),
            "preparation": random.choice(["Tea", "Powder", "Oil", "Decoction", "Fresh consumption"]),
            "precautions": "Consult healthcare provider before use",
            "discovery_timestamp": timestamp
        }
        
        logger.info(f"✅ Generated info for: {plant_info['common_name']}")
        return plant_info
    
    def simulate_image_generation(self, plant_info: Dict, num_images: int = 10) -> List[str]:
        """
        🎨 Simulate synthetic image generation
        """
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        logger.info(f"🎨 Simulating generation of {num_images} images for {plant_name}")
        
        # Create directory for generated images
        gen_dir = Path('generated_images') / plant_name
        gen_dir.mkdir(parents=True, exist_ok=True)
        
        generated_paths = []
        
        # Simulate image generation by creating colored placeholder images
        for i in range(num_images):
            # Create a simple colored image as placeholder
            img_size = (224, 224)
            
            # Generate random colors that look plant-like
            base_color = random.choice([
                (34, 139, 34),   # Forest Green
                (50, 205, 50),   # Lime Green
                (107, 142, 35),  # Olive Drab
                (85, 107, 47),   # Dark Olive Green
                (124, 252, 0),   # Lawn Green
            ])
            
            # Add some variation
            color = tuple(max(0, min(255, c + random.randint(-30, 30))) for c in base_color)
            
            # Create image
            img = Image.new('RGB', img_size, color)
            
            # Add some texture
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(random.uniform(0.8, 1.2))
            
            # Add slight blur for organic look
            img = img.filter(ImageFilter.GaussianBlur(radius=random.uniform(0.5, 1.5)))
            
            # Save image
            img_path = gen_dir / f"generated_{i:03d}.jpg"
            img.save(img_path, 'JPEG', quality=85)
            generated_paths.append(str(img_path))
            
            # Simulate generation time
            time.sleep(0.1)
        
        logger.info(f"✅ Generated {len(generated_paths)} placeholder images for {plant_name}")
        return generated_paths

    def simulate_data_augmentation(self, plant_info: Dict, generated_images: List[str]) -> str:
        """
        📈 Simulate data augmentation and dataset creation
        """
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        logger.info(f"📈 Simulating data augmentation for {plant_name}")

        # Create dataset directory
        dataset_dir = Path('new_plants') / plant_name
        dataset_dir.mkdir(parents=True, exist_ok=True)

        # Save plant information
        info_path = dataset_dir / "plant_info.json"
        with open(info_path, 'w') as f:
            json.dump(plant_info, f, indent=2)

        # Copy original generated images
        augmented_count = 0
        target_images = self.config['images_per_plant']

        for i, img_path in enumerate(generated_images):
            if os.path.exists(img_path):
                dest_path = dataset_dir / f"original_{i:03d}.jpg"
                shutil.copy2(img_path, dest_path)
                augmented_count += 1

        # Simulate augmentation by creating variations
        while augmented_count < target_images:
            for img_path in generated_images:
                if augmented_count >= target_images:
                    break

                try:
                    # Load and modify image
                    img = Image.open(img_path)

                    # Apply random transformations
                    if random.random() > 0.5:
                        img = img.transpose(Image.FLIP_LEFT_RIGHT)

                    if random.random() > 0.5:
                        enhancer = ImageEnhance.Brightness(img)
                        img = enhancer.enhance(random.uniform(0.7, 1.3))

                    if random.random() > 0.5:
                        enhancer = ImageEnhance.Contrast(img)
                        img = enhancer.enhance(random.uniform(0.8, 1.2))

                    if random.random() > 0.5:
                        angle = random.randint(-15, 15)
                        img = img.rotate(angle, expand=False, fillcolor=(0, 128, 0))

                    # Save augmented image
                    aug_path = dataset_dir / f"augmented_{augmented_count:04d}.jpg"
                    img.save(aug_path, 'JPEG', quality=85)

                    augmented_count += 1

                except Exception as e:
                    logger.warning(f"⚠️ Augmentation failed: {e}")
                    continue

        logger.info(f"✅ Created dataset with {augmented_count} images for {plant_name}")
        return str(dataset_dir)

    def update_data_yaml(self, plant_info: Dict):
        """
        📝 Update data.yaml file with new plant class
        """
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        logger.info(f"📝 Updating data.yaml with new class: {plant_name}")

        yaml_path = Path('data.yaml')

        try:
            # Load existing YAML or create new one
            if yaml_path.exists():
                with open(yaml_path, 'r') as f:
                    data = yaml.safe_load(f) or {}
            else:
                data = {
                    'train': 'dataset/train',
                    'val': 'dataset/val',
                    'test': 'dataset/test',
                    'nc': 0,
                    'names': []
                }

            # Add new class if not already present
            if plant_name not in data.get('names', []):
                data['names'].append(plant_name)
                data['nc'] = len(data['names'])

                # Save updated YAML
                with open(yaml_path, 'w') as f:
                    yaml.dump(data, f, default_flow_style=False)

                logger.info(f"✅ Added {plant_name} to data.yaml (total classes: {data['nc']})")
            else:
                logger.info(f"ℹ️ {plant_name} already exists in data.yaml")

        except Exception as e:
            logger.error(f"❌ Failed to update data.yaml: {e}")

    def process_plant_image(self, image_path: str) -> Dict:
        """
        🌟 Main pipeline: Process plant image through complete AI system
        """
        logger.info(f"🌟 Starting complete plant processing pipeline for {image_path}")

        start_time = time.time()

        # Step 1: Try YOLO detection first
        is_detected, detection_result = self.simulate_yolo_detection(image_path)

        if is_detected:
            # Known plant detected
            logger.info("✅ Known plant detected - returning existing information")
            processing_time = time.time() - start_time

            return {
                "status": "known_plant",
                "detection_method": "yolo_simulation",
                "plant_info": detection_result["plant_info"],
                "confidence": detection_result["confidence"],
                "processing_time": processing_time,
                "message": "Plant successfully identified from existing dataset"
            }

        else:
            # Unknown plant - trigger AI generation pipeline
            logger.info("🧠 Unknown plant detected - starting AI generation pipeline")

            # Step 2: Generate plant information using AI
            plant_info = self.simulate_ai_plant_identification(image_path)

            # Step 3: Generate synthetic images
            synthetic_images = self.simulate_image_generation(plant_info, self.config['synthetic_images'])

            # Step 4: Create and augment dataset
            dataset_path = self.simulate_data_augmentation(plant_info, synthetic_images)

            # Step 5: Update data.yaml
            self.update_data_yaml(plant_info)

            processing_time = time.time() - start_time

            logger.info(f"🎉 New plant processing completed in {processing_time:.2f} seconds")

            return {
                "status": "new_plant_generated",
                "detection_method": "ai_simulation",
                "plant_info": plant_info,
                "dataset_path": dataset_path,
                "synthetic_images_count": len(synthetic_images),
                "augmented_images_count": self.config['images_per_plant'],
                "processing_time": processing_time,
                "message": f"New plant '{plant_info['common_name']}' added to dataset with {self.config['images_per_plant']} images"
            }

# 🌐 Flask Web Interface
app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize the demo AI system
demo_plant_ai = DemoMedicinalPlantAI()

# HTML template for the web interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌱 AI Medicinal Plant Recognition - Demo</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 900px; margin: 0 auto; }
        .header {
            text-align: center;
            background: rgba(255,255,255,0.95);
            color: #333;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; color: #2c5530; }
        .header p { font-size: 1.2em; color: #666; }
        .demo-badge {
            background: #ff6b6b;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            margin-top: 10px;
            display: inline-block;
        }
        .upload-area {
            border: 3px dashed #4CAF50;
            border-radius: 15px;
            padding: 50px;
            text-align: center;
            background: rgba(255,255,255,0.95);
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .upload-area:hover {
            background: rgba(255,255,255,1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .upload-area h3 { color: #2c5530; margin-bottom: 15px; font-size: 1.5em; }
        .upload-area p { color: #666; font-size: 1.1em; }
        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        .result {
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .loading h3 { color: #2c5530; margin-bottom: 15px; }
        .loading p { color: #666; }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .plant-info {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 5px solid #4CAF50;
        }
        .plant-info h4 { color: #2c5530; margin-bottom: 10px; font-size: 1.3em; }
        .medicinal-uses {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 5px solid #ffc107;
        }
        .medicinal-uses h4 { color: #856404; margin-bottom: 10px; }
        .status-known { border-left: 5px solid #28a745; }
        .status-new { border-left: 5px solid #007bff; }
        .confidence { font-weight: bold; color: #28a745; font-size: 1.1em; }
        .processing-time { color: #6c757d; font-size: 0.9em; margin-top: 15px; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: rgba(255,255,255,0.8);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e0e0e0;
        }
        .stat-number { font-size: 1.5em; font-weight: bold; color: #4CAF50; }
        .stat-label { color: #666; font-size: 0.9em; }
        .features {
            background: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .features h3 { color: #2c5530; margin-bottom: 15px; }
        .feature-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; }
        .feature-item { padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 3px solid #4CAF50; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌱 AI Medicinal Plant Recognition</h1>
            <p>Self-Learning Plant Identification with Auto-Dataset Generation</p>
            <div class="demo-badge">DEMO MODE</div>
        </div>

        <div class="features">
            <h3>🚀 System Features</h3>
            <div class="feature-list">
                <div class="feature-item">🔍 <strong>YOLO Detection</strong> - Identifies known plants instantly</div>
                <div class="feature-item">🧠 <strong>AI Identification</strong> - Analyzes unknown plants using AI</div>
                <div class="feature-item">🎨 <strong>Image Generation</strong> - Creates synthetic training images</div>
                <div class="feature-item">📈 <strong>Data Augmentation</strong> - Expands dataset automatically</div>
                <div class="feature-item">🔄 <strong>Self-Learning</strong> - Improves with each new plant</div>
                <div class="feature-item">🌐 <strong>Web Interface</strong> - Easy-to-use upload system</div>
            </div>
        </div>

        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>📸 Upload Plant Image</h3>
            <p>Click here or drag & drop your plant image to test the AI system</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="uploadImage()">
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <h3>🧠 AI Processing...</h3>
            <p>Analyzing plant image and generating information...</p>
            <p><small>This may take a few seconds as the AI identifies the plant</small></p>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function uploadImage() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) return;

            const formData = new FormData();
            formData.append('image', file);

            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').innerHTML = '';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                displayResults(data);
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').innerHTML = '<div class="result"><h3>❌ Error</h3><p>' + error + '</p></div>';
            });
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            const statusClass = data.status === 'known_plant' ? 'status-known' : 'status-new';

            let html = `<div class="result ${statusClass}">`;

            if (data.status === 'known_plant') {
                html += '<h3>✅ Known Plant Identified</h3>';
                html += `<p class="confidence">Confidence: ${(data.confidence * 100).toFixed(1)}%</p>`;
                html += '<p>This plant was found in the existing dataset.</p>';
            } else {
                html += '<h3>🆕 New Plant Discovered & Added to Dataset</h3>';
                html += '<div class="stats">';
                html += `<div class="stat-card"><div class="stat-number">${data.synthetic_images_count}</div><div class="stat-label">Synthetic Images</div></div>`;
                html += `<div class="stat-card"><div class="stat-number">${data.augmented_images_count}</div><div class="stat-label">Total Training Images</div></div>`;
                html += `<div class="stat-card"><div class="stat-number">${data.processing_time.toFixed(1)}s</div><div class="stat-label">Processing Time</div></div>`;
                html += '</div>';
            }

            const plantInfo = data.plant_info;
            html += `
                <div class="plant-info">
                    <h4>🌿 ${plantInfo.common_name}</h4>
                    <p><strong>Scientific Name:</strong> ${plantInfo.scientific_name}</p>
                    <p><strong>Local Name:</strong> ${plantInfo.local_name || 'N/A'}</p>
                    <p><strong>Family:</strong> ${plantInfo.family || 'N/A'}</p>
                    <p><strong>Habitat:</strong> ${plantInfo.habitat || 'N/A'}</p>
                    <p><strong>Parts Used:</strong> ${plantInfo.parts_used || 'N/A'}</p>
                    <p><strong>Preparation:</strong> ${plantInfo.preparation || 'N/A'}</p>
                    <p><strong>Description:</strong> ${plantInfo.description || 'N/A'}</p>
                </div>
            `;

            if (plantInfo.medicinal_uses && plantInfo.medicinal_uses.length > 0) {
                html += '<div class="medicinal-uses"><h4>💊 Medicinal Uses:</h4><ul>';
                plantInfo.medicinal_uses.forEach(use => {
                    html += `<li>${use}</li>`;
                });
                html += '</ul></div>';
            }

            if (plantInfo.precautions) {
                html += `<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 3px solid #dc3545;">`;
                html += `<strong>⚠️ Precautions:</strong> ${plantInfo.precautions}`;
                html += '</div>';
            }

            html += `<p class="processing-time">⏱️ Processing time: ${data.processing_time.toFixed(2)} seconds</p>`;
            html += `<p><em>${data.message}</em></p>`;
            html += '</div>';

            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Main page"""
    return HTML_TEMPLATE

@app.route('/upload', methods=['POST'])
def upload_image():
    """Handle image upload and processing"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if file:
            # Save uploaded file
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Process the image through AI pipeline
            result = demo_plant_ai.process_plant_image(filepath)

            # Clean up uploaded file
            try:
                os.remove(filepath)
            except:
                pass

            return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Upload processing failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/stats')
def get_stats():
    """Get system statistics"""
    try:
        # Count plants in dataset
        new_plants_dir = Path('new_plants')
        plant_count = len([d for d in new_plants_dir.iterdir() if d.is_dir()]) if new_plants_dir.exists() else 0

        # Get YAML info
        yaml_path = Path('data.yaml')
        total_classes = 0
        if yaml_path.exists():
            with open(yaml_path, 'r') as f:
                data = yaml.safe_load(f)
                total_classes = data.get('nc', 0) if data else 0

        return jsonify({
            'new_plants_discovered': plant_count,
            'total_classes': total_classes,
            'known_plants': len(demo_plant_ai.known_plants),
            'mode': 'demo'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🌱 AI Medicinal Plant Recognition System - DEMO MODE")
    print("=" * 70)
    print("🚀 Features:")
    print("  • Simulated YOLO detection for known plants")
    print("  • AI-powered identification for unknown plants")
    print("  • Automatic synthetic image generation (simulated)")
    print("  • Data augmentation and dataset expansion")
    print("  • Self-learning system demonstration")
    print("=" * 70)
    print("📱 Open your browser and go to: http://localhost:5000")
    print("🌿 Upload plant images to test the AI system!")
    print("💡 This demo simulates the complete AI pipeline without heavy dependencies")
    print("=" * 70)

    app.run(host='0.0.0.0', port=5000, debug=True)
