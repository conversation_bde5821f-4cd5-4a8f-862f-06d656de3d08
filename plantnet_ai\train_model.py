#!/usr/bin/env python3
"""
PlantNet AI Model Training Script
Train the EfficientNet-based plant classification model
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from models.efficientnet_model import EfficientNetPlantClassifier
from models.data_loader import PlantDataLoader
from models.trainer import ModelTrainer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Train PlantNet AI model')
    
    parser.add_argument('--data-dir', type=str, required=True,
                       help='Path to training data directory')
    parser.add_argument('--output-dir', type=str, default='training_output',
                       help='Output directory for training results')
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                       help='Batch size for training')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                       help='Initial learning rate')
    parser.add_argument('--fine-tune-epochs', type=int, default=50,
                       help='Number of fine-tuning epochs')
    parser.add_argument('--fine-tune-lr', type=float, default=0.0001,
                       help='Fine-tuning learning rate')
    parser.add_argument('--image-size', type=int, nargs=2, default=[224, 224],
                       help='Input image size (height width)')
    parser.add_argument('--validation-split', type=float, default=0.2,
                       help='Validation split ratio')
    parser.add_argument('--test-split', type=float, default=0.1,
                       help='Test split ratio')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume training from')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform a dry run without actual training')
    
    return parser.parse_args()

def validate_data_directory(data_dir):
    """
    Validate the data directory structure
    
    Expected structure:
    data_dir/
    ├── species_1/
    │   ├── leaf/
    │   ├── flower/
    │   └── fruit/
    └── species_2/
        ├── leaf/
        └── flower/
    """
    data_path = Path(data_dir)
    
    if not data_path.exists():
        raise ValueError(f"Data directory does not exist: {data_dir}")
    
    if not data_path.is_dir():
        raise ValueError(f"Data path is not a directory: {data_dir}")
    
    # Check for species directories
    species_dirs = [d for d in data_path.iterdir() if d.is_dir()]
    
    if len(species_dirs) == 0:
        raise ValueError(f"No species directories found in: {data_dir}")
    
    logger.info(f"✅ Found {len(species_dirs)} species directories")
    
    # Check for organ subdirectories and images
    total_images = 0
    for species_dir in species_dirs[:5]:  # Check first 5 species
        organ_dirs = [d for d in species_dir.iterdir() if d.is_dir()]
        
        if len(organ_dirs) == 0:
            logger.warning(f"⚠️ No organ directories found in: {species_dir}")
            continue
        
        for organ_dir in organ_dirs:
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.webp']:
                image_files.extend(organ_dir.glob(ext))
                image_files.extend(organ_dir.glob(ext.upper()))
            
            total_images += len(image_files)
    
    if total_images == 0:
        raise ValueError(f"No image files found in data directory: {data_dir}")
    
    logger.info(f"✅ Data directory validation passed")
    return True

def create_sample_data(data_dir, num_species=5, images_per_organ=10):
    """
    Create sample data for testing (if no real data is available)
    """
    import numpy as np
    from PIL import Image
    
    data_path = Path(data_dir)
    data_path.mkdir(parents=True, exist_ok=True)
    
    organs = ['leaf', 'flower', 'fruit']
    
    logger.info(f"🔧 Creating sample data with {num_species} species...")
    
    for i in range(num_species):
        species_name = f"sample_species_{i+1}"
        species_dir = data_path / species_name
        
        for organ in organs:
            organ_dir = species_dir / organ
            organ_dir.mkdir(parents=True, exist_ok=True)
            
            for j in range(images_per_organ):
                # Create a random colored image
                img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
                
                # Add some pattern based on species and organ
                if organ == 'leaf':
                    img_array[:, :, 1] = np.minimum(img_array[:, :, 1] + 50, 255)  # More green
                elif organ == 'flower':
                    img_array[:, :, 0] = np.minimum(img_array[:, :, 0] + 50, 255)  # More red
                elif organ == 'fruit':
                    img_array[:, :, 2] = np.minimum(img_array[:, :, 2] + 50, 255)  # More blue
                
                # Add species-specific pattern
                img_array[i*20:(i+1)*20, :, :] = 255  # White stripe
                
                img = Image.fromarray(img_array)
                img_path = organ_dir / f"{species_name}_{organ}_{j+1}.jpg"
                img.save(img_path, 'JPEG', quality=85)
    
    logger.info(f"✅ Sample data created in: {data_dir}")

def main():
    """Main training function"""
    args = parse_arguments()
    
    logger.info("🚀 Starting PlantNet AI model training...")
    logger.info(f"Arguments: {vars(args)}")
    
    try:
        # Validate or create data directory
        if not Path(args.data_dir).exists():
            logger.warning(f"⚠️ Data directory not found: {args.data_dir}")
            logger.info("🔧 Creating sample data for testing...")
            create_sample_data(args.data_dir)
        
        validate_data_directory(args.data_dir)
        
        # Initialize data loader
        logger.info("📊 Initializing data loader...")
        data_loader = PlantDataLoader(
            data_dir=args.data_dir,
            image_size=tuple(args.image_size),
            batch_size=args.batch_size,
            validation_split=args.validation_split,
            test_split=args.test_split
        )
        
        # Scan dataset
        dataset_info = data_loader.scan_dataset()
        num_classes = dataset_info['species_count']
        num_organs = len(dataset_info['organ_distribution'])
        
        logger.info(f"📈 Dataset statistics:")
        logger.info(f"   Species: {num_classes}")
        logger.info(f"   Organs: {num_organs}")
        logger.info(f"   Total images: {dataset_info['total_images']}")
        
        if args.dry_run:
            logger.info("🏃 Dry run completed successfully!")
            return
        
        # Initialize model
        logger.info("🧠 Initializing model...")
        model = EfficientNetPlantClassifier(
            num_classes=num_classes,
            num_organs=num_organs,
            input_shape=(*args.image_size, 3),
            learning_rate=args.learning_rate
        )
        
        # Build model
        model.build_model()
        
        # Initialize trainer
        logger.info("🏋️ Initializing trainer...")
        trainer = ModelTrainer(
            model=model,
            data_loader=data_loader,
            output_dir=args.output_dir
        )
        
        # Start training
        logger.info("🔥 Starting training...")
        results = trainer.train(
            epochs=args.epochs,
            initial_lr=args.learning_rate,
            fine_tune_epochs=args.fine_tune_epochs,
            fine_tune_lr=args.fine_tune_lr
        )
        
        # Save final model
        final_model_path = Path(args.output_dir) / 'models' / 'final_model.h5'
        model.save_model(str(final_model_path))
        
        # Save to config location
        config_model_path = Config.MODEL_PATH / Config.MODEL_NAME
        config_model_path.parent.mkdir(parents=True, exist_ok=True)
        model.save_model(str(config_model_path))
        
        logger.info(f"✅ Training completed successfully!")
        logger.info(f"📊 Final test accuracy: {results['test_results']['test_accuracy']:.4f}")
        logger.info(f"💾 Model saved to: {final_model_path}")
        logger.info(f"💾 Model copied to: {config_model_path}")
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise

if __name__ == '__main__':
    main()
