"""
Model Training Pipeline for Plant Classification
Handles training, validation, and model optimization
"""

import os
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.callbacks import (
    EarlyStopping, 
    ReduceLROnPlateau, 
    ModelCheckpoint,
    TensorBoard,
    CSVLogger
)
from sklearn.metrics import classification_report, confusion_matrix

from .efficientnet_model import EfficientNetPlantClassifier
from .data_loader import PlantDataLoader

logger = logging.getLogger(__name__)

class ModelTrainer:
    """
    Comprehensive training pipeline for plant classification models
    """
    
    def __init__(self, 
                 model: EfficientNetPlantClassifier,
                 data_loader: PlantDataLoader,
                 output_dir: str = 'training_output'):
        """
        Initialize the model trainer
        
        Args:
            model: EfficientNet plant classifier instance
            data_loader: Plant data loader instance
            output_dir: Directory to save training outputs
        """
        self.model = model
        self.data_loader = data_loader
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Training history
        self.history = None
        self.best_model_path = None
        
        # Create subdirectories
        self.models_dir = self.output_dir / 'models'
        self.logs_dir = self.output_dir / 'logs'
        self.plots_dir = self.output_dir / 'plots'
        self.reports_dir = self.output_dir / 'reports'
        
        for dir_path in [self.models_dir, self.logs_dir, self.plots_dir, self.reports_dir]:
            dir_path.mkdir(exist_ok=True)
        
        logger.info(f"✅ ModelTrainer initialized with output directory: {output_dir}")
    
    def prepare_callbacks(self, 
                         patience: int = 10,
                         min_lr: float = 1e-7,
                         monitor: str = 'val_accuracy') -> List[keras.callbacks.Callback]:
        """
        Prepare training callbacks
        
        Args:
            patience: Patience for early stopping
            min_lr: Minimum learning rate
            monitor: Metric to monitor
            
        Returns:
            List of Keras callbacks
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        callbacks = [
            # Early stopping
            EarlyStopping(
                monitor=monitor,
                patience=patience,
                restore_best_weights=True,
                verbose=1,
                mode='max' if 'accuracy' in monitor else 'min'
            ),
            
            # Learning rate reduction
            ReduceLROnPlateau(
                monitor=monitor,
                factor=0.5,
                patience=patience // 2,
                min_lr=min_lr,
                verbose=1,
                mode='max' if 'accuracy' in monitor else 'min'
            ),
            
            # Model checkpointing
            ModelCheckpoint(
                filepath=str(self.models_dir / f'best_model_{timestamp}.h5'),
                monitor=monitor,
                save_best_only=True,
                save_weights_only=False,
                verbose=1,
                mode='max' if 'accuracy' in monitor else 'min'
            ),
            
            # TensorBoard logging
            TensorBoard(
                log_dir=str(self.logs_dir / f'tensorboard_{timestamp}'),
                histogram_freq=1,
                write_graph=True,
                write_images=True,
                update_freq='epoch'
            ),
            
            # CSV logging
            CSVLogger(
                filename=str(self.logs_dir / f'training_log_{timestamp}.csv'),
                separator=',',
                append=False
            )
        ]
        
        # Store best model path for later use
        self.best_model_path = str(self.models_dir / f'best_model_{timestamp}.h5')
        
        return callbacks
    
    def train(self, 
              epochs: int = 100,
              initial_lr: float = 0.001,
              fine_tune_epochs: int = 50,
              fine_tune_lr: float = 0.0001) -> Dict:
        """
        Train the plant classification model
        
        Args:
            epochs: Number of initial training epochs
            initial_lr: Initial learning rate
            fine_tune_epochs: Number of fine-tuning epochs
            fine_tune_lr: Fine-tuning learning rate
            
        Returns:
            Training history and metrics
        """
        logger.info("🚀 Starting model training...")
        
        # Prepare data
        logger.info("📊 Preparing training data...")
        dataset_info = self.data_loader.scan_dataset()
        df = self.data_loader.create_dataframe()
        train_df, val_df, test_df = self.data_loader.split_dataset(df)
        
        # Update model with correct number of classes
        self.model.num_classes = len(self.data_loader.class_names)
        self.model.num_organs = len(self.data_loader.organ_names)
        
        # Build model
        if self.model.model is None:
            self.model.build_model()
        
        # Create datasets
        train_dataset = self.data_loader.create_tf_dataset(train_df, augment=True)
        val_dataset = self.data_loader.create_tf_dataset(val_df, augment=False)
        test_dataset = self.data_loader.create_tf_dataset(test_df, augment=False)
        
        # Calculate class weights
        class_weights = self.data_loader.get_class_weights(train_df)
        
        # Prepare callbacks
        callbacks = self.prepare_callbacks()
        
        # Phase 1: Initial training with frozen backbone
        logger.info("🔥 Phase 1: Initial training with frozen backbone...")
        
        # Update learning rate
        self.model.model.optimizer.learning_rate = initial_lr
        
        # Train model
        history_1 = self.model.model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=epochs,
            callbacks=callbacks,
            class_weight=class_weights,
            verbose=1
        )
        
        # Phase 2: Fine-tuning with unfrozen layers
        if fine_tune_epochs > 0:
            logger.info("🎯 Phase 2: Fine-tuning with unfrozen layers...")
            
            # Enable fine-tuning
            self.model.fine_tune(unfreeze_layers=50)
            
            # Update learning rate for fine-tuning
            self.model.model.optimizer.learning_rate = fine_tune_lr
            
            # Continue training
            history_2 = self.model.model.fit(
                train_dataset,
                validation_data=val_dataset,
                epochs=fine_tune_epochs,
                initial_epoch=len(history_1.history['loss']),
                callbacks=callbacks,
                class_weight=class_weights,
                verbose=1
            )
            
            # Combine histories
            combined_history = {}
            for key in history_1.history.keys():
                combined_history[key] = history_1.history[key] + history_2.history[key]
            
            self.history = combined_history
        else:
            self.history = history_1.history
        
        # Evaluate on test set
        logger.info("📊 Evaluating on test set...")
        test_results = self.evaluate_model(test_dataset, test_df)
        
        # Save training results
        self.save_training_results(test_results)
        
        # Generate plots
        self.plot_training_history()
        
        logger.info("✅ Training completed successfully!")
        
        return {
            'history': self.history,
            'test_results': test_results,
            'best_model_path': self.best_model_path
        }
    
    def evaluate_model(self, test_dataset: tf.data.Dataset, test_df: pd.DataFrame) -> Dict:
        """
        Evaluate the trained model on test data
        
        Args:
            test_dataset: Test dataset
            test_df: Test DataFrame
            
        Returns:
            Evaluation metrics
        """
        # Load best model
        if self.best_model_path and os.path.exists(self.best_model_path):
            best_model = keras.models.load_model(self.best_model_path)
        else:
            best_model = self.model.model
        
        # Get predictions
        logger.info("🔍 Generating predictions...")
        predictions = best_model.predict(test_dataset)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Get true labels
        true_classes = test_df['species_encoded'].values
        
        # Calculate metrics
        test_loss, test_accuracy, test_top5_accuracy = best_model.evaluate(test_dataset, verbose=0)
        
        # Classification report
        class_names = self.data_loader.class_names
        classification_rep = classification_report(
            true_classes, 
            predicted_classes,
            target_names=class_names,
            output_dict=True
        )
        
        # Confusion matrix
        conf_matrix = confusion_matrix(true_classes, predicted_classes)
        
        results = {
            'test_loss': float(test_loss),
            'test_accuracy': float(test_accuracy),
            'test_top5_accuracy': float(test_top5_accuracy),
            'classification_report': classification_rep,
            'confusion_matrix': conf_matrix.tolist(),
            'predictions': predictions.tolist(),
            'predicted_classes': predicted_classes.tolist(),
            'true_classes': true_classes.tolist()
        }
        
        logger.info(f"📊 Test Results:")
        logger.info(f"   🎯 Accuracy: {test_accuracy:.4f}")
        logger.info(f"   🏆 Top-5 Accuracy: {test_top5_accuracy:.4f}")
        logger.info(f"   📉 Loss: {test_loss:.4f}")
        
        return results
    
    def plot_training_history(self) -> None:
        """Generate and save training history plots"""
        if self.history is None:
            logger.warning("⚠️ No training history available for plotting")
            return
        
        # Set up the plotting style
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training History', fontsize=16, fontweight='bold')
        
        # Plot training & validation accuracy
        axes[0, 0].plot(self.history['accuracy'], label='Training Accuracy', linewidth=2)
        axes[0, 0].plot(self.history['val_accuracy'], label='Validation Accuracy', linewidth=2)
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Plot training & validation loss
        axes[0, 1].plot(self.history['loss'], label='Training Loss', linewidth=2)
        axes[0, 1].plot(self.history['val_loss'], label='Validation Loss', linewidth=2)
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Plot top-k accuracy if available
        if 'top_k_categorical_accuracy' in self.history:
            axes[1, 0].plot(self.history['top_k_categorical_accuracy'], label='Training Top-K', linewidth=2)
            axes[1, 0].plot(self.history['val_top_k_categorical_accuracy'], label='Validation Top-K', linewidth=2)
            axes[1, 0].set_title('Top-K Accuracy')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Top-K Accuracy')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # Plot learning rate if available
        if 'lr' in self.history:
            axes[1, 1].plot(self.history['lr'], linewidth=2, color='orange')
            axes[1, 1].set_title('Learning Rate')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Learning Rate')
            axes[1, 1].set_yscale('log')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plot_path = self.plots_dir / f'training_history_{timestamp}.png'
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"✅ Training history plot saved to {plot_path}")
    
    def save_training_results(self, test_results: Dict) -> None:
        """Save comprehensive training results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save training history
        history_path = self.reports_dir / f'training_history_{timestamp}.json'
        with open(history_path, 'w') as f:
            json.dump(self.history, f, indent=2)
        
        # Save test results
        results_path = self.reports_dir / f'test_results_{timestamp}.json'
        with open(results_path, 'w') as f:
            json.dump(test_results, f, indent=2, default=str)
        
        # Save model configuration
        config_path = self.reports_dir / f'model_config_{timestamp}.json'
        model_config = {
            'num_classes': self.model.num_classes,
            'num_organs': self.model.num_organs,
            'input_shape': self.model.input_shape,
            'dropout_rate': self.model.dropout_rate,
            'learning_rate': self.model.learning_rate,
            'class_names': self.data_loader.class_names,
            'organ_names': self.data_loader.organ_names
        }
        with open(config_path, 'w') as f:
            json.dump(model_config, f, indent=2)
        
        # Save encoders
        encoders_path = self.reports_dir / f'encoders_{timestamp}.json'
        self.data_loader.save_encoders(encoders_path)
        
        logger.info(f"✅ Training results saved to {self.reports_dir}")

if __name__ == '__main__':
    # Test trainer
    from .efficientnet_model import EfficientNetPlantClassifier
    from .data_loader import PlantDataLoader
    
    # Initialize components
    model = EfficientNetPlantClassifier(num_classes=10)
    data_loader = PlantDataLoader('data/raw')
    trainer = ModelTrainer(model, data_loader)
    
    print("✅ ModelTrainer test successful!")
