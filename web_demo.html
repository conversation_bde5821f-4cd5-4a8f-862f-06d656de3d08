<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌿 Medicinal Plant Recognition System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #2E8B57;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .plant-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .plant-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border-left: 5px solid #2E8B57;
        }
        
        .plant-card h3 {
            color: #2E8B57;
            margin-bottom: 10px;
        }
        
        .plant-info {
            margin: 8px 0;
            font-size: 0.9rem;
        }
        
        .plant-info strong {
            color: #333;
        }
        
        .search-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .search-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            margin-bottom: 20px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #2E8B57;
        }
        
        .search-results {
            margin-top: 20px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
            margin: 10px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2E8B57;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .footer {
            background: #333;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .disclaimer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .disclaimer h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .disclaimer p {
            color: #856404;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌿 Medicinal Plant Recognition System</h1>
            <p>AI-Powered Plant Identification & Medicinal Information Database</p>
        </div>
        
        <div class="content">
            <!-- Statistics Section -->
            <div class="demo-section">
                <h2>📊 System Overview</h2>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">144</div>
                        <div class="stat-label">Plant Species</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">YOLOv8</div>
                        <div class="stat-label">AI Model</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">Real-time</div>
                        <div class="stat-label">Detection</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">Web-based</div>
                        <div class="stat-label">Interface</div>
                    </div>
                </div>
            </div>
            
            <!-- Features Section -->
            <div class="demo-section">
                <h2>✨ Key Features</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>Plant Identification</h3>
                        <p>Upload plant images for instant AI-powered identification with confidence scores</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💊</div>
                        <h3>Medicinal Information</h3>
                        <p>Comprehensive database of medicinal uses, scientific names, and descriptions</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔍</div>
                        <h3>Smart Search</h3>
                        <p>Find plants by medicinal use, health condition, or plant name</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>Visual Analytics</h3>
                        <p>Interactive charts and confidence visualization for predictions</p>
                    </div>
                </div>
            </div>
            
            <!-- Sample Plants Section -->
            <div class="demo-section">
                <h2>🌿 Featured Medicinal Plants</h2>
                <div class="plant-grid">
                    <div class="plant-card">
                        <h3>🌱 Aloe Vera</h3>
                        <div class="plant-info"><strong>Scientific:</strong> Aloe barbadensis</div>
                        <div class="plant-info"><strong>Local:</strong> Ghritkumari, Aloe</div>
                        <div class="plant-info"><strong>Uses:</strong> Skin healing, burns, digestive issues, anti-inflammatory</div>
                        <div class="plant-info"><strong>Description:</strong> Cooling and healing properties for skin and internal use</div>
                    </div>
                    
                    <div class="plant-card">
                        <h3>🌿 Holy Basil (Tulsi)</h3>
                        <div class="plant-info"><strong>Scientific:</strong> Ocimum tenuiflorum</div>
                        <div class="plant-info"><strong>Local:</strong> Tulsi, Sacred Basil</div>
                        <div class="plant-info"><strong>Uses:</strong> Respiratory problems, immunity, stress relief, fever, cough</div>
                        <div class="plant-info"><strong>Description:</strong> Sacred herb with adaptogenic and respiratory benefits</div>
                    </div>
                    
                    <div class="plant-card">
                        <h3>🟡 Turmeric</h3>
                        <div class="plant-info"><strong>Scientific:</strong> Curcuma longa</div>
                        <div class="plant-info"><strong>Local:</strong> Haldi, Golden Spice</div>
                        <div class="plant-info"><strong>Uses:</strong> Anti-inflammatory, wound healing, immunity, digestive health</div>
                        <div class="plant-info"><strong>Description:</strong> Golden rhizome with powerful anti-inflammatory effects</div>
                    </div>
                    
                    <div class="plant-card">
                        <h3>🌾 Ashwagandha</h3>
                        <div class="plant-info"><strong>Scientific:</strong> Withania somnifera</div>
                        <div class="plant-info"><strong>Local:</strong> Indian Winter Cherry, Asgandh</div>
                        <div class="plant-info"><strong>Uses:</strong> Stress relief, adaptogen, strength, immunity, sleep disorders</div>
                        <div class="plant-info"><strong>Description:</strong> Powerful adaptogenic herb for stress and vitality</div>
                    </div>
                    
                    <div class="plant-card">
                        <h3>🌳 Neem</h3>
                        <div class="plant-info"><strong>Scientific:</strong> Azadirachta indica</div>
                        <div class="plant-info"><strong>Local:</strong> Neem, Margosa Tree</div>
                        <div class="plant-info"><strong>Uses:</strong> Skin diseases, diabetes, immunity, antimicrobial, dental care</div>
                        <div class="plant-info"><strong>Description:</strong> Bitter tree with powerful antimicrobial properties</div>
                    </div>
                    
                    <div class="plant-card">
                        <h3>🌿 Brahmi</h3>
                        <div class="plant-info"><strong>Scientific:</strong> Bacopa monnieri</div>
                        <div class="plant-info"><strong>Local:</strong> Brahmi, Water Hyssop</div>
                        <div class="plant-info"><strong>Uses:</strong> Memory enhancement, cognitive function, anxiety, epilepsy</div>
                        <div class="plant-info"><strong>Description:</strong> Brain tonic and nootropic herb</div>
                    </div>
                </div>
            </div>
            
            <!-- How to Use Section -->
            <div class="demo-section">
                <h2>🚀 How to Use the System</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">1️⃣</div>
                        <h3>Install Dependencies</h3>
                        <p><code>pip install -r requirements.txt</code></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">2️⃣</div>
                        <h3>Train the Model</h3>
                        <p><code>python train_model.py</code></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">3️⃣</div>
                        <h3>Run the App</h3>
                        <p><code>streamlit run app.py</code></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">4️⃣</div>
                        <h3>Upload & Identify</h3>
                        <p>Upload plant images and get instant results!</p>
                    </div>
                </div>
            </div>
            
            <!-- Disclaimer -->
            <div class="disclaimer">
                <h4>⚠️ Important Medical Disclaimer</h4>
                <p>This application is for educational and informational purposes only. It is not intended to diagnose, treat, cure, or prevent any disease. Always consult with qualified healthcare professionals before using any medicinal plants or making health-related decisions. Plant identification accuracy may vary based on image quality and conditions.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>🌿 Medicinal Plant Recognition System | Powered by YOLOv8 & Streamlit</p>
            <p>Made with 💚 for preserving traditional medicinal plant knowledge through modern AI technology</p>
        </div>
    </div>
</body>
</html>
