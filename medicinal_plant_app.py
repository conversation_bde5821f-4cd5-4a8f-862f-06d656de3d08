"""
Medicinal Plant Detection App
Upload an image and get plant identification with medicinal information
"""

import streamlit as st
import numpy as np
from PIL import Image
import os
import random
from plant_database import get_plant_info, get_all_plant_names

# Page configuration
st.set_page_config(
    page_title="🌿 Medicinal Plant Detection",
    page_icon="🌿",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for the exact look you want
st.markdown("""
<style>
    .main-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .subtitle {
        color: #7f8c8d;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }
    
    .result-container {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        padding: 2rem;
        border-radius: 15px;
        margin: 1rem 0;
        border: 1px solid #b8daff;
    }
    
    .plant-name {
        font-size: 2.2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .mock-label {
        background: #6c757d;
        color: white;
        padding: 0.2rem 0.5rem;
        border-radius: 5px;
        font-size: 0.8rem;
        margin-left: 0.5rem;
    }
    
    .plant-info {
        font-size: 1.1rem;
        margin: 0.8rem 0;
        color: #2c3e50;
    }
    
    .plant-info strong {
        color: #1a5d1a;
        font-weight: 600;
    }
    
    .medicinal-features {
        margin-top: 1rem;
    }
    
    .feature-list {
        margin-left: 1rem;
        margin-top: 0.5rem;
    }
    
    .feature-item {
        color: #2c3e50;
        margin: 0.3rem 0;
        font-size: 1rem;
    }
    
    .analyze-btn {
        background: #17a2b8;
        color: white;
        border: none;
        padding: 0.5rem 1.5rem;
        border-radius: 5px;
        font-weight: bold;
        margin-right: 1rem;
    }
    
    .clear-btn {
        background: #6c757d;
        color: white;
        border: none;
        padding: 0.5rem 1.5rem;
        border-radius: 5px;
    }
    
    .recent-section {
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }
    
    .recent-item {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #17a2b8;
    }
    
    .feedback-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 8px;
        font-weight: bold;
        float: right;
        margin-top: 1rem;
    }
    
    .edit-btn {
        background: #ffc107;
        color: #212529;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        margin-right: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def simulate_plant_detection(image):
    """
    Simulate plant detection - in real implementation, this would use YOLOv8
    For now, we'll randomly select from our database to demonstrate functionality
    """
    # Get all available plants
    all_plants = get_all_plant_names()
    
    # Remove the unknown entry
    available_plants = [plant for plant in all_plants if plant != '-']
    
    # For demo purposes, we'll simulate detection based on image characteristics
    # In real implementation, this would be YOLOv8 model prediction
    
    # Simulate some logic based on image properties
    img_array = np.array(image)
    avg_green = np.mean(img_array[:,:,1])  # Green channel average
    
    # Select plants based on "green-ness" to make it seem more realistic
    if avg_green > 120:
        # More green = more likely to be leafy plants
        preferred_plants = ['Ocimum tenuiflorum', 'Aloe vera', 'Basil', 'Sweet Basil', 
                          'Medicinal-Neem', 'neem', 'Centella asiatica', 'Bacopa monnieri']
    elif avg_green > 80:
        # Medium green = herbs and medicinal plants
        preferred_plants = ['Turmeric', 'Ashwagandha', 'Amla', 'Fenugreek', 
                          'Cinnamon', 'Cardamom', 'Thyme', 'Lavender']
    else:
        # Less green = roots, bark, or dried plants
        preferred_plants = ['Ashwagandha', 'Turmeric', 'Guggul', 'Kutki', 
                          'Saussurea Costus', 'Mulethi', 'Jatamansi']
    
    # Filter preferred plants that exist in our database
    valid_preferred = [p for p in preferred_plants if p in available_plants]
    
    if valid_preferred:
        detected_plant = random.choice(valid_preferred)
    else:
        detected_plant = random.choice(available_plants[:20])  # Top 20 plants
    
    # Simulate confidence (in real model, this would come from YOLOv8)
    confidence = random.uniform(0.75, 0.95)
    
    return detected_plant, confidence

def display_plant_result(plant_name, confidence):
    """Display the plant detection result in the desired format"""
    
    plant_info = get_plant_info(plant_name)
    
    # Create the result container
    st.markdown(f"""
    <div class="result-container">
        <div class="plant-name">
            {plant_name} <span class="mock-label">(mock)</span>
        </div>
        
        <div class="plant-info">
            <strong>Scientific Name:</strong> {plant_info['scientific_name']}
        </div>
        
        <div class="plant-info">
            <strong>Local Name:</strong> {plant_info['local_name']}
        </div>
        
        <div class="medicinal-features">
            <div class="plant-info"><strong>Medicinal Features:</strong></div>
            <div class="feature-list">
    """, unsafe_allow_html=True)
    
    # Parse medicinal uses and display as bullet points
    uses = plant_info['medicinal_uses'].split(',')
    for use in uses[:3]:  # Show top 3 uses
        use = use.strip()
        if use:
            st.markdown(f'<div class="feature-item">• {use}</div>', unsafe_allow_html=True)
    
    st.markdown("""
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Add action buttons
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("Edit Info", key="edit_btn"):
            st.info("Edit functionality would allow users to correct plant information")
    
    with col2:
        if st.button("Feedback", key="feedback_btn"):
            st.success("Thank you for your feedback!")
    
    return plant_info

def main():
    """Main application"""
    
    # Title and subtitle
    st.markdown('<div class="main-title">Medicinal Plant Detection</div>', unsafe_allow_html=True)
    st.markdown('<div class="subtitle">Upload a leaf or plant photo and get a prediction.</div>', unsafe_allow_html=True)
    
    # Create two columns for layout
    col1, col2 = st.columns([1, 1.5])
    
    with col1:
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose File",
            type=['jpg', 'jpeg', 'png'],
            help="Upload a clear image of a medicinal plant"
        )
        
        if uploaded_file is not None:
            # Display uploaded image
            image = Image.open(uploaded_file)
            st.image(image, caption=uploaded_file.name, use_column_width=True)
            
            # Action buttons
            col_btn1, col_btn2 = st.columns(2)
            
            with col_btn1:
                if st.button("Analyze", key="analyze", help="Analyze the uploaded image"):
                    # Simulate plant detection
                    with st.spinner("Analyzing plant..."):
                        detected_plant, confidence = simulate_plant_detection(image)
                        
                        # Store in session state for persistence
                        st.session_state.last_detection = {
                            'plant': detected_plant,
                            'confidence': confidence,
                            'image_name': uploaded_file.name,
                            'timestamp': 'Just now'
                        }
            
            with col_btn2:
                if st.button("Clear", key="clear"):
                    if 'last_detection' in st.session_state:
                        del st.session_state.last_detection
                    st.rerun()
        
        # Recent detections section
        st.markdown('<div class="recent-section"><h3>Recent</h3></div>', unsafe_allow_html=True)
        
        # Show recent detection if available
        if 'last_detection' in st.session_state:
            detection = st.session_state.last_detection
            st.markdown(f"""
            <div class="recent-item">
                <strong>{detection['image_name']}</strong><br>
                <small style="color: #6c757d;">{detection['timestamp']}</small><br>
                <span style="color: #2c3e50;">{detection['plant']}</span>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        # Display results if detection was performed
        if 'last_detection' in st.session_state:
            detection = st.session_state.last_detection
            plant_info = display_plant_result(detection['plant'], detection['confidence'])
            
            # Show small thumbnail of the plant
            if uploaded_file is not None:
                image = Image.open(uploaded_file)
                # Create a small thumbnail in the corner
                thumbnail = image.resize((100, 100))
                st.image(thumbnail, width=100)
        else:
            # Show placeholder or instructions
            st.markdown("""
            <div style="text-align: center; padding: 3rem; color: #6c757d;">
                <h3>🌿 Upload a plant image to get started</h3>
                <p>The AI will identify the medicinal plant and provide:</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>Plant name</li>
                    <li>Scientific name</li>
                    <li>Local name</li>
                    <li>Medicinal properties</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
