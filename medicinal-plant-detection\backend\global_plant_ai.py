"""
Global Plant AI Identification System
Uses multiple APIs and machine learning to identify any plant globally
"""

import os
import io
import json
import base64
import requests
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import hashlib
from PIL import Image
import numpy as np
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)

class GlobalPlantAI:
    """Advanced AI system for global plant identification"""
    
    def __init__(self):
        self.plantnet_api_key = os.getenv('PLANTNET_API_KEY')
        self.google_vision_api_key = os.getenv('GOOGLE_VISION_API_KEY')
        self.plant_id_api_key = os.getenv('PLANT_ID_API_KEY')
        self.inaturalist_url = os.getenv('INATURALIST_API_URL', 'https://api.inaturalist.org/v1')
        
        self.confidence_threshold = float(os.getenv('CONFIDENCE_THRESHOLD_FOR_AUTO_ADD', 0.8))
        self.enable_global_search = os.getenv('ENABLE_GLOBAL_SEARCH', 'True').lower() == 'true'
        self.auto_add_to_db = os.getenv('AUTO_ADD_TO_DATABASE', 'True').lower() == 'true'
        
        # Initialize plant knowledge base
        self.plant_knowledge = self._load_plant_knowledge()
        
    def _load_plant_knowledge(self) -> Dict:
        """Load comprehensive plant knowledge base"""
        return {
            "medicinal_properties": {
                "antibacterial": ["neem", "turmeric", "garlic", "ginger"],
                "anti_inflammatory": ["turmeric", "aloe_vera", "willow_bark"],
                "antioxidant": ["green_tea", "ginkgo", "grape_seed"],
                "digestive": ["ginger", "peppermint", "fennel"],
                "respiratory": ["eucalyptus", "tulsi", "thyme"],
                "immune_booster": ["echinacea", "elderberry", "astragalus"]
            },
            "regional_names": {
                "hindi": {},
                "sanskrit": {},
                "tamil": {},
                "bengali": {},
                "marathi": {},
                "gujarati": {}
            }
        }
    
    async def identify_plant_globally(self, image_data: bytes, filename: str) -> Dict:
        """
        Identify plant using multiple global APIs and AI services
        """
        try:
            # Convert image to different formats for APIs
            image = Image.open(io.BytesIO(image_data))
            
            # Try multiple identification methods
            results = []
            
            # Method 1: PlantNet API (Scientific database)
            if self.plantnet_api_key and self.plantnet_api_key != 'your_plantnet_api_key_here':
                plantnet_result = await self._identify_with_plantnet(image_data)
                if plantnet_result:
                    results.append(plantnet_result)
            
            # Method 2: Plant.id API (Commercial AI)
            if self.plant_id_api_key and self.plant_id_api_key != 'your_plant_id_api_key_here':
                plant_id_result = await self._identify_with_plant_id(image_data)
                if plant_id_result:
                    results.append(plant_id_result)
            
            # Method 3: iNaturalist API (Community database)
            inaturalist_result = await self._identify_with_inaturalist(image_data)
            if inaturalist_result:
                results.append(inaturalist_result)
            
            # Method 4: Google Vision API (General AI)
            if self.google_vision_api_key and self.google_vision_api_key != 'your_google_vision_api_key_here':
                google_result = await self._identify_with_google_vision(image_data)
                if google_result:
                    results.append(google_result)
            
            # Method 5: Local knowledge base matching
            local_result = await self._identify_with_local_knowledge(image)
            if local_result:
                results.append(local_result)
            
            # Combine and validate results
            final_result = self._combine_identification_results(results)
            
            # Enhance with medicinal properties
            enhanced_result = await self._enhance_with_medicinal_info(final_result)
            
            # Auto-add to database if confidence is high
            if (enhanced_result.get('confidence', 0) >= self.confidence_threshold and 
                self.auto_add_to_db):
                await self._auto_add_to_database(enhanced_result, image_data, filename)
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Global plant identification failed: {e}")
            return self._create_fallback_result()
    
    async def _identify_with_plantnet(self, image_data: bytes) -> Optional[Dict]:
        """Identify plant using PlantNet API"""
        try:
            url = "https://my-api.plantnet.org/v2/identify/weurope"
            
            files = {
                'images': ('plant.jpg', image_data, 'image/jpeg'),
                'modifiers': (None, '["crops","isolated"]'),
                'plant-details': (None, 'common-names,url')
            }
            
            params = {
                'api-key': self.plantnet_api_key,
                'lang': 'en'
            }
            
            response = requests.post(url, files=files, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('results') and len(data['results']) > 0:
                    best_match = data['results'][0]
                    
                    return {
                        'source': 'PlantNet',
                        'plant_name': best_match.get('species', {}).get('commonNames', ['Unknown'])[0],
                        'scientific_name': best_match.get('species', {}).get('scientificNameWithoutAuthor', 'Unknown'),
                        'confidence': best_match.get('score', 0),
                        'family': best_match.get('species', {}).get('family', {}).get('scientificNameWithoutAuthor', 'Unknown'),
                        'genus': best_match.get('species', {}).get('genus', {}).get('scientificNameWithoutAuthor', 'Unknown'),
                        'common_names': best_match.get('species', {}).get('commonNames', []),
                        'url': best_match.get('species', {}).get('url', '')
                    }
            
            return None
            
        except Exception as e:
            logger.warning(f"PlantNet identification failed: {e}")
            return None
    
    async def _identify_with_plant_id(self, image_data: bytes) -> Optional[Dict]:
        """Identify plant using Plant.id API"""
        try:
            # Convert image to base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            url = "https://api.plant.id/v2/identify"
            
            headers = {
                'Content-Type': 'application/json',
                'Api-Key': self.plant_id_api_key
            }
            
            data = {
                "images": [image_base64],
                "modifiers": ["crops", "similar_images"],
                "plant_language": "en",
                "plant_details": [
                    "common_names",
                    "url",
                    "name_authority",
                    "wiki_description",
                    "taxonomy"
                ]
            }
            
            response = requests.post(url, json=data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('suggestions') and len(result['suggestions']) > 0:
                    best_match = result['suggestions'][0]
                    
                    plant_details = best_match.get('plant_details', {})
                    
                    return {
                        'source': 'Plant.id',
                        'plant_name': plant_details.get('common_names', ['Unknown'])[0] if plant_details.get('common_names') else 'Unknown',
                        'scientific_name': best_match.get('plant_name', 'Unknown'),
                        'confidence': best_match.get('probability', 0),
                        'common_names': plant_details.get('common_names', []),
                        'description': plant_details.get('wiki_description', {}).get('value', ''),
                        'taxonomy': plant_details.get('taxonomy', {}),
                        'url': plant_details.get('url', '')
                    }
            
            return None
            
        except Exception as e:
            logger.warning(f"Plant.id identification failed: {e}")
            return None
    
    async def _identify_with_inaturalist(self, image_data: bytes) -> Optional[Dict]:
        """Identify plant using iNaturalist API"""
        try:
            # iNaturalist doesn't have direct image identification API
            # But we can search their database for plant information
            # This is a simplified implementation
            
            # For now, return None - would need computer vision preprocessing
            # to extract features and match against iNaturalist database
            return None
            
        except Exception as e:
            logger.warning(f"iNaturalist identification failed: {e}")
            return None
    
    async def _identify_with_google_vision(self, image_data: bytes) -> Optional[Dict]:
        """Identify plant using Google Vision API"""
        try:
            # Convert image to base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            url = f"https://vision.googleapis.com/v1/images:annotate?key={self.google_vision_api_key}"
            
            data = {
                "requests": [
                    {
                        "image": {
                            "content": image_base64
                        },
                        "features": [
                            {
                                "type": "LABEL_DETECTION",
                                "maxResults": 10
                            },
                            {
                                "type": "WEB_DETECTION",
                                "maxResults": 5
                            }
                        ]
                    }
                ]
            }
            
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('responses') and len(result['responses']) > 0:
                    annotations = result['responses'][0]
                    
                    # Extract plant-related labels
                    labels = annotations.get('labelAnnotations', [])
                    plant_labels = [label for label in labels if 
                                  any(keyword in label['description'].lower() 
                                      for keyword in ['plant', 'leaf', 'flower', 'tree', 'herb', 'botanical'])]
                    
                    if plant_labels:
                        best_label = plant_labels[0]
                        
                        return {
                            'source': 'Google Vision',
                            'plant_name': best_label['description'],
                            'scientific_name': 'Unknown',
                            'confidence': best_label['score'],
                            'labels': [label['description'] for label in plant_labels[:5]]
                        }
            
            return None
            
        except Exception as e:
            logger.warning(f"Google Vision identification failed: {e}")
            return None
    
    async def _identify_with_local_knowledge(self, image: Image.Image) -> Optional[Dict]:
        """Identify plant using local knowledge base and image analysis"""
        try:
            # Simple color and texture analysis for common medicinal plants
            # This is a basic implementation - would be enhanced with ML models
            
            # Convert to numpy array for analysis
            img_array = np.array(image)
            
            # Analyze dominant colors
            avg_color = np.mean(img_array, axis=(0, 1))
            
            # Simple heuristics for common plants
            if self._is_green_dominant(avg_color):
                # Check for specific green patterns
                if self._has_serrated_edges(img_array):
                    return {
                        'source': 'Local Knowledge',
                        'plant_name': 'Neem',
                        'scientific_name': 'Azadirachta indica',
                        'confidence': 0.6,
                        'local_name': 'Nimba',
                        'analysis': 'Green leaves with serrated edges detected'
                    }
                elif self._has_oval_leaves(img_array):
                    return {
                        'source': 'Local Knowledge',
                        'plant_name': 'Tulsi',
                        'scientific_name': 'Ocimum tenuiflorum',
                        'confidence': 0.5,
                        'local_name': 'Holy Basil',
                        'analysis': 'Oval green leaves pattern detected'
                    }
            
            return None
            
        except Exception as e:
            logger.warning(f"Local knowledge identification failed: {e}")
            return None
    
    def _is_green_dominant(self, avg_color: np.ndarray) -> bool:
        """Check if green is the dominant color"""
        if len(avg_color) >= 3:
            return avg_color[1] > avg_color[0] and avg_color[1] > avg_color[2]
        return False
    
    def _has_serrated_edges(self, img_array: np.ndarray) -> bool:
        """Simple edge detection for serrated leaves"""
        # Simplified implementation
        return True  # Placeholder
    
    def _has_oval_leaves(self, img_array: np.ndarray) -> bool:
        """Simple shape detection for oval leaves"""
        # Simplified implementation
        return True  # Placeholder
    
    def _combine_identification_results(self, results: List[Dict]) -> Dict:
        """Combine results from multiple APIs and choose the best match"""
        if not results:
            return self._create_fallback_result()
        
        # Weight results by source reliability
        source_weights = {
            'PlantNet': 0.9,
            'Plant.id': 0.8,
            'Google Vision': 0.6,
            'iNaturalist': 0.7,
            'Local Knowledge': 0.5
        }
        
        # Calculate weighted confidence scores
        weighted_results = []
        for result in results:
            weight = source_weights.get(result.get('source', ''), 0.5)
            weighted_confidence = result.get('confidence', 0) * weight
            result['weighted_confidence'] = weighted_confidence
            weighted_results.append(result)
        
        # Sort by weighted confidence
        weighted_results.sort(key=lambda x: x['weighted_confidence'], reverse=True)
        
        # Use the best result as base
        best_result = weighted_results[0]
        
        # Combine information from other sources
        combined_result = {
            'plant_name': best_result.get('plant_name', 'Unknown Plant'),
            'scientific_name': best_result.get('scientific_name', 'Unknown'),
            'confidence': best_result.get('confidence', 0),
            'primary_source': best_result.get('source', 'Unknown'),
            'all_sources': [r.get('source') for r in results],
            'common_names': [],
            'family': best_result.get('family', 'Unknown'),
            'genus': best_result.get('genus', 'Unknown'),
            'description': best_result.get('description', ''),
            'local_name': best_result.get('local_name', ''),
            'analysis_details': {
                'total_sources': len(results),
                'confidence_range': [min(r.get('confidence', 0) for r in results),
                                   max(r.get('confidence', 0) for r in results)],
                'sources_used': [r.get('source') for r in results]
            }
        }
        
        # Collect all common names
        for result in results:
            if result.get('common_names'):
                combined_result['common_names'].extend(result['common_names'])
        
        # Remove duplicates
        combined_result['common_names'] = list(set(combined_result['common_names']))
        
        return combined_result
    
    async def _enhance_with_medicinal_info(self, plant_result: Dict) -> Dict:
        """Enhance plant identification with medicinal properties"""
        try:
            plant_name = plant_result.get('plant_name', '').lower()
            scientific_name = plant_result.get('scientific_name', '').lower()
            
            # Search for medicinal properties
            medicinal_uses = []
            
            # Check against known medicinal plants
            medicinal_db = {
                'neem': {
                    'uses': ['Antibacterial', 'Antifungal', 'Blood purifier', 'Skin disorders'],
                    'local_names': {'hindi': 'नीम', 'sanskrit': 'निम्ब', 'tamil': 'வேம்பு'},
                    'parts_used': ['Leaves', 'Bark', 'Seeds', 'Oil'],
                    'preparation': ['Decoction', 'Paste', 'Oil extraction']
                },
                'tulsi': {
                    'uses': ['Respiratory disorders', 'Stress relief', 'Immunity booster', 'Fever'],
                    'local_names': {'hindi': 'तुलसी', 'sanskrit': 'तुलसी', 'tamil': 'துளசி'},
                    'parts_used': ['Leaves', 'Seeds'],
                    'preparation': ['Tea', 'Fresh consumption', 'Decoction']
                },
                'aloe vera': {
                    'uses': ['Skin healing', 'Digestive health', 'Anti-inflammatory', 'Burns'],
                    'local_names': {'hindi': 'घृतकुमारी', 'sanskrit': 'घृतकुमारी', 'tamil': 'கற்றாழை'},
                    'parts_used': ['Gel', 'Leaves'],
                    'preparation': ['Fresh gel', 'Juice', 'Topical application']
                }
            }
            
            # Find matching medicinal information
            medicinal_info = None
            for key, info in medicinal_db.items():
                if (key in plant_name or 
                    any(key in name.lower() for name in plant_result.get('common_names', []))):
                    medicinal_info = info
                    break
            
            # Enhance the result
            enhanced_result = plant_result.copy()
            
            if medicinal_info:
                enhanced_result.update({
                    'medicinal_uses': medicinal_info['uses'],
                    'local_names': medicinal_info['local_names'],
                    'parts_used': medicinal_info['parts_used'],
                    'preparation_methods': medicinal_info['preparation'],
                    'is_medicinal': True
                })
            else:
                enhanced_result.update({
                    'medicinal_uses': ['Information not available'],
                    'local_names': {},
                    'parts_used': ['Consult expert'],
                    'preparation_methods': ['Consult expert'],
                    'is_medicinal': False
                })
            
            # Add safety information
            enhanced_result['safety_info'] = {
                'warning': 'Always consult a healthcare professional before using any plant medicinally',
                'identification_confidence': plant_result.get('confidence', 0),
                'verified': plant_result.get('confidence', 0) > 0.8
            }
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Failed to enhance with medicinal info: {e}")
            return plant_result
    
    async def _auto_add_to_database(self, plant_info: Dict, image_data: bytes, filename: str):
        """Automatically add identified plant to database"""
        try:
            from database import get_db
            
            db = get_db()
            
            # Create image hash for deduplication
            image_hash = hashlib.md5(image_data).hexdigest()
            
            # Prepare plant document
            plant_doc = {
                'plant_name': plant_info.get('plant_name'),
                'scientific_name': plant_info.get('scientific_name'),
                'common_names': plant_info.get('common_names', []),
                'local_names': plant_info.get('local_names', {}),
                'medicinal_uses': plant_info.get('medicinal_uses', []),
                'parts_used': plant_info.get('parts_used', []),
                'preparation_methods': plant_info.get('preparation_methods', []),
                'family': plant_info.get('family'),
                'genus': plant_info.get('genus'),
                'description': plant_info.get('description'),
                'confidence': plant_info.get('confidence'),
                'sources': plant_info.get('all_sources', []),
                'image_hash': image_hash,
                'filename': filename,
                'added_date': datetime.now(),
                'auto_added': True,
                'verified': False
            }
            
            # Add to database
            result = db.add_plant_to_database(plant_doc, image_data)
            
            if result:
                logger.info(f"Auto-added plant to database: {plant_info.get('plant_name')}")
            
        except Exception as e:
            logger.error(f"Failed to auto-add plant to database: {e}")
    
    def _create_fallback_result(self) -> Dict:
        """Create fallback result when identification fails"""
        return {
            'plant_name': 'Unknown Plant',
            'scientific_name': 'Unknown',
            'confidence': 0.0,
            'primary_source': 'Fallback',
            'medicinal_uses': ['Please consult a botanist or plant expert'],
            'local_names': {},
            'safety_info': {
                'warning': 'Plant could not be identified. Do not use medicinally.',
                'identification_confidence': 0.0,
                'verified': False
            },
            'message': 'Unable to identify this plant. Please try with a clearer image or consult an expert.'
        }
