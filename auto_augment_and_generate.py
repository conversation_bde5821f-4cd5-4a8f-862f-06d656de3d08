#!/usr/bin/env python3
"""
🌱 AI Augmentation + Auto-Generation System for Medicinal Plant Recognition
Self-learning system that detects, generates, and expands plant datasets automatically

Features:
- YOLO detection for known plants
- AI-powered plant identification for unknown plants
- Automatic synthetic image generation
- Data augmentation and dataset expansion
- Self-learning model retraining
"""

import os
import json
import yaml
import cv2
import numpy as np
import requests
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import shutil
import time

# Core ML and Vision libraries
import torch
from ultralytics import YOLO
import albumentations as A
from PIL import Image, ImageEnhance
import google.generativeai as genai

# Web framework
from flask import Flask, request, jsonify, render_template_string
from werkzeug.utils import secure_filename

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/plant_ai.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MedicinalPlantAI:
    """
    🧠 Self-Learning Medicinal Plant Recognition System
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the AI system"""
        self.config = self.load_config(config_path)
        self.setup_directories()
        self.setup_models()
        
        # Configure Gemini AI
        genai.configure(api_key=self.config['gemini_api_key'])
        self.vision_model = genai.GenerativeModel('gemini-pro-vision')
        self.text_model = genai.GenerativeModel('gemini-pro')
        
        logger.info("🌱 MedicinalPlantAI system initialized successfully")
    
    def load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        default_config = {
            'yolo_model_path': 'models/best.pt',
            'dataset_path': 'dataset',
            'augmented_path': 'augmented',
            'new_plants_path': 'new_plants',
            'data_yaml_path': 'data.yaml',
            'confidence_threshold': 0.5,
            'gemini_api_key': 'your-gemini-api-key',
            'replicate_api_key': 'your-replicate-api-key',
            'images_per_plant': 100,
            'synthetic_images': 20
        }
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = yaml.safe_load(f)
                default_config.update(user_config)
        
        return default_config
    
    def setup_directories(self):
        """Create necessary directories"""
        directories = [
            'dataset', 'augmented', 'new_plants', 'models', 'logs',
            'uploads', 'generated_images', 'temp'
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
        
        logger.info("📁 Directory structure created")
    
    def setup_models(self):
        """Initialize YOLO model"""
        try:
            if os.path.exists(self.config['yolo_model_path']):
                self.yolo_model = YOLO(self.config['yolo_model_path'])
                logger.info(f"✅ YOLO model loaded from {self.config['yolo_model_path']}")
            else:
                # Use pretrained model as fallback
                self.yolo_model = YOLO('yolov8n.pt')
                logger.warning("⚠️ Using pretrained YOLOv8n model - train your custom model")
        except Exception as e:
            logger.error(f"❌ Failed to load YOLO model: {e}")
            self.yolo_model = None
    
    def detect_with_yolo(self, image_path: str) -> Tuple[bool, Dict]:
        """
        🔍 Detect plants using YOLO model
        
        Args:
            image_path: Path to the input image
            
        Returns:
            (is_detected, detection_info)
        """
        logger.info(f"🔍 Running YOLO detection on {image_path}")
        
        try:
            if self.yolo_model is None:
                return False, {"error": "YOLO model not available"}
            
            # Run detection
            results = self.yolo_model(image_path, conf=self.config['confidence_threshold'])
            
            if len(results[0].boxes) > 0:
                # Plant detected
                box = results[0].boxes[0]  # Get highest confidence detection
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                
                # Get class name from model
                class_name = self.yolo_model.names[class_id]
                
                detection_info = {
                    "detected": True,
                    "class_name": class_name,
                    "confidence": confidence,
                    "bbox": box.xyxy[0].tolist(),
                    "plant_info": self.get_known_plant_info(class_name)
                }
                
                logger.info(f"✅ Plant detected: {class_name} (confidence: {confidence:.2f})")
                return True, detection_info
            else:
                logger.info("❓ No known plants detected - will use AI identification")
                return False, {"detected": False, "reason": "No plants detected above threshold"}
                
        except Exception as e:
            logger.error(f"❌ YOLO detection failed: {e}")
            return False, {"error": str(e)}
    
    def get_known_plant_info(self, class_name: str) -> Dict:
        """Get information for known plants"""
        # Load from existing plant database or return basic info
        plant_info_path = Path(self.config['dataset_path']) / class_name / "plant_info.json"
        
        if plant_info_path.exists():
            with open(plant_info_path, 'r') as f:
                return json.load(f)
        
        # Return basic info if no detailed info available
        return {
            "common_name": class_name,
            "scientific_name": f"Unknown {class_name}",
            "local_name": class_name,
            "medicinal_uses": ["Information not available"],
            "description": f"Known plant: {class_name}"
        }
    
    def generate_new_plant_info(self, image_path: str) -> Dict:
        """
        🧠 Generate plant information using Gemini Vision + Text AI
        
        Args:
            image_path: Path to the unknown plant image
            
        Returns:
            Generated plant information
        """
        logger.info(f"🧠 Generating plant info using AI for {image_path}")
        
        try:
            # Load and prepare image for Gemini Vision
            image = Image.open(image_path)
            
            # Vision analysis prompt
            vision_prompt = """
            Analyze this plant image and identify:
            1. What type of plant this is
            2. Key visual characteristics
            3. Plant family if recognizable
            4. Any distinctive features
            
            Provide a detailed botanical description.
            """
            
            # Get vision analysis
            vision_response = self.vision_model.generate_content([vision_prompt, image])
            vision_analysis = vision_response.text
            
            # Generate detailed plant information
            info_prompt = f"""
            Based on this plant analysis: {vision_analysis}
            
            Generate detailed information in JSON format:
            {{
                "common_name": "Common English name",
                "scientific_name": "Scientific binomial name",
                "local_name": "Local/regional name",
                "family": "Plant family",
                "medicinal_uses": ["List of medicinal uses and benefits"],
                "description": "Detailed description",
                "habitat": "Natural habitat",
                "parts_used": "Parts used medicinally",
                "preparation": "How it's typically prepared",
                "precautions": "Any precautions or side effects"
            }}
            
            Ensure the JSON is valid and complete.
            """
            
            info_response = self.text_model.generate_content(info_prompt)
            
            # Parse JSON response
            try:
                plant_info = json.loads(info_response.text.strip())
                logger.info(f"✅ Generated info for: {plant_info.get('common_name', 'Unknown')}")
                return plant_info
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                logger.warning("⚠️ JSON parsing failed, creating fallback info")
                return self.create_fallback_plant_info(vision_analysis)
                
        except Exception as e:
            logger.error(f"❌ AI plant info generation failed: {e}")
            return self.create_fallback_plant_info("Unknown plant")
    
    def create_fallback_plant_info(self, description: str) -> Dict:
        """Create fallback plant information"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return {
            "common_name": f"Unknown_Plant_{timestamp}",
            "scientific_name": "Species unknown",
            "local_name": "Unknown",
            "family": "Unknown",
            "medicinal_uses": ["Requires further research"],
            "description": description,
            "habitat": "Unknown",
            "parts_used": "Unknown",
            "preparation": "Unknown",
            "precautions": "Consult expert before use"
        }
    
    def generate_synthetic_images(self, plant_info: Dict, num_images: int = 20) -> List[str]:
        """
        🎨 Generate synthetic images using AI (Stable Diffusion via Replicate)
        
        Args:
            plant_info: Plant information dictionary
            num_images: Number of images to generate
            
        Returns:
            List of generated image paths
        """
        logger.info(f"🎨 Generating {num_images} synthetic images for {plant_info['common_name']}")
        
        generated_paths = []
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        
        # Create directory for generated images
        gen_dir = Path('generated_images') / plant_name
        gen_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # Prepare prompts for different plant parts
            prompts = [
                f"High quality photo of {plant_info['common_name']} plant, medicinal plant, clear background, botanical photography",
                f"Close-up of {plant_info['common_name']} leaves, detailed botanical illustration style",
                f"{plant_info['common_name']} flower, macro photography, natural lighting",
                f"Full {plant_info['common_name']} plant in natural habitat, {plant_info.get('habitat', 'garden')}",
                f"Dried {plant_info['common_name']} for medicinal use, traditional medicine preparation"
            ]
            
            # Generate images using different prompts
            for i in range(num_images):
                prompt = prompts[i % len(prompts)]
                
                try:
                    # Use Replicate API for image generation
                    if self.config.get('replicate_api_key') and self.config['replicate_api_key'] != 'your-replicate-api-key':
                        image_url = self.generate_with_replicate(prompt)
                        if image_url:
                            image_path = self.download_image(image_url, gen_dir / f"generated_{i:03d}.jpg")
                            if image_path:
                                generated_paths.append(str(image_path))
                    else:
                        # Fallback: Create placeholder images
                        placeholder_path = self.create_placeholder_image(gen_dir / f"placeholder_{i:03d}.jpg", plant_info)
                        generated_paths.append(str(placeholder_path))
                        
                except Exception as e:
                    logger.warning(f"⚠️ Failed to generate image {i}: {e}")
                    continue
            
            logger.info(f"✅ Generated {len(generated_paths)} images for {plant_info['common_name']}")
            return generated_paths
            
        except Exception as e:
            logger.error(f"❌ Synthetic image generation failed: {e}")
            return []
    
    def generate_with_replicate(self, prompt: str) -> Optional[str]:
        """Generate image using Replicate API"""
        try:
            import replicate
            
            output = replicate.run(
                "stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478",
                input={"prompt": prompt}
            )
            
            return output[0] if output else None
            
        except Exception as e:
            logger.warning(f"⚠️ Replicate generation failed: {e}")
            return None
    
    def download_image(self, url: str, save_path: Path) -> Optional[Path]:
        """Download image from URL"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                f.write(response.content)
            
            return save_path
            
        except Exception as e:
            logger.warning(f"⚠️ Image download failed: {e}")
            return None
    
    def create_placeholder_image(self, save_path: Path, plant_info: Dict) -> Path:
        """Create placeholder image when generation fails"""
        # Create a simple colored rectangle as placeholder
        img = np.random.randint(50, 200, (224, 224, 3), dtype=np.uint8)
        
        # Add some texture to make it look more plant-like
        img = cv2.GaussianBlur(img, (5, 5), 0)
        
        cv2.imwrite(str(save_path), img)
        return save_path

    def create_and_augment_dataset(self, plant_info: Dict, generated_images: List[str]) -> str:
        """
        📈 Create dataset folder and apply data augmentation

        Args:
            plant_info: Plant information dictionary
            generated_images: List of generated image paths

        Returns:
            Path to the created dataset folder
        """
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        logger.info(f"📈 Creating and augmenting dataset for {plant_name}")

        # Create dataset directory
        dataset_dir = Path(self.config['new_plants_path']) / plant_name
        dataset_dir.mkdir(parents=True, exist_ok=True)

        # Save plant information
        info_path = dataset_dir / "plant_info.json"
        with open(info_path, 'w') as f:
            json.dump(plant_info, f, indent=2)

        # Define augmentation pipeline
        augmentation_pipeline = A.Compose([
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.2),
            A.Rotate(limit=30, p=0.7),
            A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.8),
            A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.6),
            A.GaussianBlur(blur_limit=(1, 3), p=0.3),
            A.RandomResizedCrop(height=224, width=224, scale=(0.8, 1.0), p=0.8),
            A.CoarseDropout(max_holes=8, max_height=16, max_width=16, p=0.3),
            A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.3),
            A.GridDistortion(p=0.3),
            A.OpticalDistortion(distort_limit=0.1, shift_limit=0.1, p=0.3)
        ])

        augmented_count = 0
        target_images = self.config['images_per_plant']

        # Copy original generated images
        for i, img_path in enumerate(generated_images):
            if os.path.exists(img_path):
                dest_path = dataset_dir / f"original_{i:03d}.jpg"
                shutil.copy2(img_path, dest_path)
                augmented_count += 1

        # Apply augmentation to reach target number
        while augmented_count < target_images:
            for img_path in generated_images:
                if augmented_count >= target_images:
                    break

                try:
                    # Load image
                    image = cv2.imread(img_path)
                    if image is None:
                        continue

                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                    # Apply augmentation
                    augmented = augmentation_pipeline(image=image)
                    augmented_image = augmented['image']

                    # Convert back to BGR for saving
                    augmented_image = cv2.cvtColor(augmented_image, cv2.COLOR_RGB2BGR)

                    # Save augmented image
                    aug_path = dataset_dir / f"augmented_{augmented_count:04d}.jpg"
                    cv2.imwrite(str(aug_path), augmented_image)

                    augmented_count += 1

                except Exception as e:
                    logger.warning(f"⚠️ Augmentation failed for {img_path}: {e}")
                    continue

        logger.info(f"✅ Created dataset with {augmented_count} images for {plant_name}")
        return str(dataset_dir)

    def update_data_yaml(self, plant_info: Dict):
        """
        📝 Update data.yaml file with new plant class

        Args:
            plant_info: Plant information dictionary
        """
        plant_name = plant_info['common_name'].replace(' ', '_').lower()
        logger.info(f"📝 Updating data.yaml with new class: {plant_name}")

        yaml_path = Path(self.config['data_yaml_path'])

        try:
            # Load existing YAML or create new one
            if yaml_path.exists():
                with open(yaml_path, 'r') as f:
                    data = yaml.safe_load(f) or {}
            else:
                data = {
                    'train': 'dataset/train',
                    'val': 'dataset/val',
                    'test': 'dataset/test',
                    'nc': 0,
                    'names': []
                }

            # Add new class if not already present
            if plant_name not in data.get('names', []):
                data['names'].append(plant_name)
                data['nc'] = len(data['names'])

                # Save updated YAML
                with open(yaml_path, 'w') as f:
                    yaml.dump(data, f, default_flow_style=False)

                logger.info(f"✅ Added {plant_name} to data.yaml (total classes: {data['nc']})")
            else:
                logger.info(f"ℹ️ {plant_name} already exists in data.yaml")

        except Exception as e:
            logger.error(f"❌ Failed to update data.yaml: {e}")

    def train_yolo_model(self, epochs: int = 50):
        """
        🏋️ Train/retrain YOLO model with updated dataset

        Args:
            epochs: Number of training epochs
        """
        logger.info(f"🏋️ Starting YOLO model training for {epochs} epochs")

        try:
            # Prepare training data
            self.prepare_training_data()

            # Initialize model for training
            if os.path.exists(self.config['yolo_model_path']):
                # Continue training from existing model
                model = YOLO(self.config['yolo_model_path'])
                logger.info("📚 Continuing training from existing model")
            else:
                # Start with pretrained model
                model = YOLO('yolov8n.pt')
                logger.info("🆕 Starting training with pretrained YOLOv8n")

            # Train the model
            results = model.train(
                data=self.config['data_yaml_path'],
                epochs=epochs,
                imgsz=640,
                batch=16,
                device='auto',
                project='runs/train',
                name='medicinal_plants',
                save=True,
                save_period=10,
                cache=True,
                augment=True,
                mosaic=0.5,
                mixup=0.1
            )

            # Save the best model
            best_model_path = Path('runs/train/medicinal_plants/weights/best.pt')
            if best_model_path.exists():
                shutil.copy2(best_model_path, self.config['yolo_model_path'])
                logger.info(f"✅ Model training completed. Best model saved to {self.config['yolo_model_path']}")

                # Reload the updated model
                self.yolo_model = YOLO(self.config['yolo_model_path'])
            else:
                logger.warning("⚠️ Training completed but best model not found")

        except Exception as e:
            logger.error(f"❌ Model training failed: {e}")

    def prepare_training_data(self):
        """Prepare training data in YOLO format"""
        logger.info("📋 Preparing training data in YOLO format")

        # Create train/val/test directories
        for split in ['train', 'val', 'test']:
            for subdir in ['images', 'labels']:
                Path(f'dataset/{split}/{subdir}').mkdir(parents=True, exist_ok=True)

        # Process new plants data
        new_plants_dir = Path(self.config['new_plants_path'])

        for plant_dir in new_plants_dir.iterdir():
            if plant_dir.is_dir():
                self.process_plant_for_training(plant_dir)

    def process_plant_for_training(self, plant_dir: Path):
        """Process individual plant directory for YOLO training"""
        plant_name = plant_dir.name

        # Get all images in the plant directory
        image_files = list(plant_dir.glob('*.jpg')) + list(plant_dir.glob('*.png'))

        if not image_files:
            return

        # Split images into train/val/test (70/20/10)
        np.random.shuffle(image_files)
        n_images = len(image_files)

        train_split = int(0.7 * n_images)
        val_split = int(0.9 * n_images)

        splits = {
            'train': image_files[:train_split],
            'val': image_files[train_split:val_split],
            'test': image_files[val_split:]
        }

        # Copy images and create labels for each split
        for split_name, split_images in splits.items():
            for img_path in split_images:
                # Copy image
                dest_img = Path(f'dataset/{split_name}/images/{img_path.name}')
                shutil.copy2(img_path, dest_img)

                # Create label file (assuming full image is the plant)
                label_path = Path(f'dataset/{split_name}/labels/{img_path.stem}.txt')

                # Get class index from data.yaml
                class_idx = self.get_class_index(plant_name)

                # Create bounding box for full image (normalized coordinates)
                with open(label_path, 'w') as f:
                    f.write(f"{class_idx} 0.5 0.5 1.0 1.0\n")  # Full image bounding box

    def get_class_index(self, plant_name: str) -> int:
        """Get class index for plant name from data.yaml"""
        try:
            with open(self.config['data_yaml_path'], 'r') as f:
                data = yaml.safe_load(f)

            names = data.get('names', [])
            if plant_name in names:
                return names.index(plant_name)
            else:
                return 0  # Default to first class

        except Exception:
            return 0

    def process_plant_image(self, image_path: str) -> Dict:
        """
        🌟 Main pipeline: Process plant image through complete AI system

        Args:
            image_path: Path to the uploaded plant image

        Returns:
            Complete processing results
        """
        logger.info(f"🌟 Starting complete plant processing pipeline for {image_path}")

        start_time = time.time()

        # Step 1: Try YOLO detection first
        is_detected, detection_result = self.detect_with_yolo(image_path)

        if is_detected:
            # Known plant detected
            logger.info("✅ Known plant detected - returning existing information")
            processing_time = time.time() - start_time

            return {
                "status": "known_plant",
                "detection_method": "yolo",
                "plant_info": detection_result["plant_info"],
                "confidence": detection_result["confidence"],
                "processing_time": processing_time,
                "message": "Plant successfully identified from existing dataset"
            }

        else:
            # Unknown plant - trigger AI generation pipeline
            logger.info("🧠 Unknown plant detected - starting AI generation pipeline")

            # Step 2: Generate plant information using AI
            plant_info = self.generate_new_plant_info(image_path)

            # Step 3: Generate synthetic images
            synthetic_images = self.generate_synthetic_images(plant_info, self.config['synthetic_images'])

            # Step 4: Create and augment dataset
            dataset_path = self.create_and_augment_dataset(plant_info, synthetic_images)

            # Step 5: Update data.yaml
            self.update_data_yaml(plant_info)

            # Step 6: Schedule model retraining (optional - can be done periodically)
            # self.train_yolo_model(epochs=10)  # Uncomment for immediate retraining

            processing_time = time.time() - start_time

            logger.info(f"🎉 New plant processing completed in {processing_time:.2f} seconds")

            return {
                "status": "new_plant_generated",
                "detection_method": "ai_generation",
                "plant_info": plant_info,
                "dataset_path": dataset_path,
                "synthetic_images_count": len(synthetic_images),
                "augmented_images_count": self.config['images_per_plant'],
                "processing_time": processing_time,
                "message": f"New plant '{plant_info['common_name']}' added to dataset with {self.config['images_per_plant']} images"
            }

# 🌐 Flask Web Interface
app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize the AI system
plant_ai = MedicinalPlantAI()

# HTML template for the web interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌱 AI Medicinal Plant Recognition</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f0f8f0; }
        .header { text-align: center; background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .upload-area { border: 3px dashed #4CAF50; border-radius: 10px; padding: 40px; text-align: center; background: white; margin-bottom: 20px; }
        .upload-area:hover { background: #f9f9f9; }
        .btn { background: #4CAF50; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #45a049; }
        .result { background: white; padding: 20px; border-radius: 10px; margin-top: 20px; border-left: 5px solid #4CAF50; }
        .loading { display: none; text-align: center; padding: 20px; }
        .plant-info { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .medicinal-uses { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status-known { border-left-color: #28a745; }
        .status-new { border-left-color: #007bff; }
        .confidence { font-weight: bold; color: #28a745; }
        .processing-time { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌱 AI Medicinal Plant Recognition System</h1>
        <p>Self-Learning Plant Identification with Auto-Dataset Generation</p>
    </div>

    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <h3>📸 Upload Plant Image</h3>
        <p>Click here or drag & drop your plant image</p>
        <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="uploadImage()">
    </div>

    <div class="loading" id="loading">
        <h3>🧠 AI Processing...</h3>
        <p>Analyzing plant image and generating information...</p>
    </div>

    <div id="results"></div>

    <script>
        function uploadImage() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) return;

            const formData = new FormData();
            formData.append('image', file);

            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').innerHTML = '';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                displayResults(data);
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').innerHTML = '<div class="result"><h3>❌ Error</h3><p>' + error + '</p></div>';
            });
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            const statusClass = data.status === 'known_plant' ? 'status-known' : 'status-new';

            let html = `<div class="result ${statusClass}">`;

            if (data.status === 'known_plant') {
                html += '<h3>✅ Known Plant Identified</h3>';
                html += `<p class="confidence">Confidence: ${(data.confidence * 100).toFixed(1)}%</p>`;
            } else {
                html += '<h3>🆕 New Plant Discovered & Added to Dataset</h3>';
                html += `<p>Generated ${data.synthetic_images_count} synthetic images and ${data.augmented_images_count} total training images</p>`;
            }

            const plantInfo = data.plant_info;
            html += `
                <div class="plant-info">
                    <h4>🌿 ${plantInfo.common_name}</h4>
                    <p><strong>Scientific Name:</strong> ${plantInfo.scientific_name}</p>
                    <p><strong>Local Name:</strong> ${plantInfo.local_name || 'N/A'}</p>
                    <p><strong>Family:</strong> ${plantInfo.family || 'N/A'}</p>
                    <p><strong>Description:</strong> ${plantInfo.description || 'N/A'}</p>
                </div>
            `;

            if (plantInfo.medicinal_uses && plantInfo.medicinal_uses.length > 0) {
                html += '<div class="medicinal-uses"><h4>💊 Medicinal Uses:</h4><ul>';
                plantInfo.medicinal_uses.forEach(use => {
                    html += `<li>${use}</li>`;
                });
                html += '</ul></div>';
            }

            html += `<p class="processing-time">⏱️ Processing time: ${data.processing_time.toFixed(2)} seconds</p>`;
            html += `<p><em>${data.message}</em></p>`;
            html += '</div>';

            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Main page"""
    return HTML_TEMPLATE

@app.route('/upload', methods=['POST'])
def upload_image():
    """Handle image upload and processing"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if file:
            # Save uploaded file
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Process the image through AI pipeline
            result = plant_ai.process_plant_image(filepath)

            # Clean up uploaded file
            try:
                os.remove(filepath)
            except:
                pass

            return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Upload processing failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/retrain', methods=['POST'])
def retrain_model():
    """Trigger model retraining"""
    try:
        epochs = request.json.get('epochs', 50)

        # Start retraining in background (in production, use Celery or similar)
        plant_ai.train_yolo_model(epochs=epochs)

        return jsonify({
            'status': 'success',
            'message': f'Model retraining completed with {epochs} epochs'
        })

    except Exception as e:
        logger.error(f"❌ Model retraining failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/stats')
def get_stats():
    """Get system statistics"""
    try:
        # Count plants in dataset
        new_plants_dir = Path(plant_ai.config['new_plants_path'])
        plant_count = len([d for d in new_plants_dir.iterdir() if d.is_dir()])

        # Get YAML info
        yaml_path = Path(plant_ai.config['data_yaml_path'])
        total_classes = 0
        if yaml_path.exists():
            with open(yaml_path, 'r') as f:
                data = yaml.safe_load(f)
                total_classes = data.get('nc', 0)

        return jsonify({
            'new_plants_discovered': plant_count,
            'total_classes': total_classes,
            'model_path': plant_ai.config['yolo_model_path'],
            'model_exists': os.path.exists(plant_ai.config['yolo_model_path'])
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🌱 Starting AI Medicinal Plant Recognition System")
    print("=" * 60)
    print("🚀 Features:")
    print("  • YOLO detection for known plants")
    print("  • AI-powered identification for unknown plants")
    print("  • Automatic synthetic image generation")
    print("  • Data augmentation and dataset expansion")
    print("  • Self-learning model retraining")
    print("=" * 60)
    print("📱 Open your browser and go to: http://localhost:5000")
    print("🌿 Upload plant images to test the system!")
    print("=" * 60)

    app.run(host='0.0.0.0', port=5000, debug=True)
