# YOLOv8 Dataset Configuration for Medicinal Plants
# Dataset: Medicinal Plant Recognition Dataset
# Classes: 20 Common Medicinal Plants

# Dataset paths (relative to this file)
path: ./  # dataset root dir
train: train/images  # train images (relative to 'path')
val: valid/images    # val images (relative to 'path')
test: test/images    # test images (optional, relative to 'path')

# Number of classes
nc: 20

# Class names (20 common medicinal plants)
names:
  0: Neem
  1: Tulsi
  2: AloeVera
  3: Ashwagandha
  4: Peppermint
  5: Turmeric
  6: <PERSON>
  7: Basil
  8: Lavender
  9: Rosemary
  10: <PERSON>hyme
  11: <PERSON>
  12: Chamomile
  13: Echinacea
  14: Ginkgo
  15: Ginseng
  16: Calendula
  17: Dandelion
  18: Nettle
  19: Elderberry

# Dataset information
dataset_info:
  description: "Medicinal Plant Recognition Dataset with 20,000 annotated images"
  version: "1.0"
  year: 2024
  contributor: "Medicinal Plant Research Team"
  url: "https://github.com/medicinal-plants/dataset"
  
# Training parameters
train_params:
  epochs: 50
  batch_size: 16
  img_size: 640
  patience: 10
  save_period: 5
