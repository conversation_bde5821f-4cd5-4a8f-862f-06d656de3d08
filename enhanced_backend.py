#!/usr/bin/env python3
"""
Enhanced Flask Backend with Global AI Plant Identification
Identifies ANY plant globally and learns from each upload
"""

import os
import sys
import io
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional
import hashlib

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
from werkzeug.utils import secure_filename
from PIL import Image
import numpy as np

# Add backend to path
sys.path.append('medicinal-plant-detection/backend')

from global_plant_ai import GlobalPlantAI
from database import get_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize AI and database
global_ai = GlobalPlantAI()
db = get_db()

# HTML template with enhanced features
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌿 Global Plant AI Recognition System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 2rem;
            margin: 2rem 0;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .upload-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }
        
        #fileInput {
            display: none;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }
        
        .result {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .result.show {
            display: block;
        }
        
        .plant-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .info-section {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }
        
        .info-section h4 {
            color: #27ae60;
            margin-bottom: 0.5rem;
        }
        
        .confidence-bar {
            background: #ecf0f1;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            transition: width 0.5s ease;
        }
        
        .sources {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .source-tag {
            background: #3498db;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #3498db;
            font-size: 1.1rem;
            margin-top: 1rem;
        }
        
        .loading.show {
            display: block;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin-top: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌿 Global Plant AI Recognition</h1>
            <p class="subtitle">Advanced AI system that identifies ANY plant globally</p>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🌍</div>
                    <strong>Global Database</strong>
                    <p>Identifies plants from around the world</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🤖</div>
                    <strong>Multiple AI APIs</strong>
                    <p>Uses PlantNet, Plant.id, Google Vision</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📚</div>
                    <strong>Auto Learning</strong>
                    <p>Automatically adds new plants to database</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">💊</div>
                    <strong>Medicinal Info</strong>
                    <p>Provides medicinal uses and properties</p>
                </div>
            </div>
        </div>
        
        <div class="status success">
            ✅ Global AI Plant Recognition System is active!
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📸</div>
            <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">Upload Any Plant Image</div>
            <p>Our AI will identify it globally and add it to the learning database</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>
        
        <div style="text-align: center;">
            <button class="btn" onclick="analyzeImage()">🔍 Identify Plant Globally</button>
            <button class="btn secondary" onclick="clearResults()">🗑️ Clear</button>
            <button class="btn secondary" onclick="showStats()">📊 View Statistics</button>
        </div>
        
        <div class="loading" id="loading">
            <div style="font-size: 2rem; margin-bottom: 1rem;">🔄</div>
            <div>Analyzing with multiple AI systems...</div>
            <div style="font-size: 0.9rem; margin-top: 0.5rem;">This may take 10-30 seconds</div>
        </div>
        
        <div class="result" id="result">
            <h3>🌱 Global Plant Identification Results</h3>
            <div id="resultContent"></div>
        </div>
        
        <div class="result" id="statsResult">
            <h3>📊 Learning Database Statistics</h3>
            <div id="statsContent"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        
        // File input handling
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                displayImagePreview(file);
            }
        });
        
        // Drag and drop handling
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                displayImagePreview(files[0]);
            }
        });
        
        function displayImagePreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const uploadArea = document.querySelector('.upload-area');
                uploadArea.innerHTML = `
                    <img src="${e.target.result}" class="preview-image" alt="Preview">
                    <p style="margin-top: 1rem;"><strong>File:</strong> ${file.name}</p>
                    <p><strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                `;
            };
            reader.readAsDataURL(file);
        }
        
        async function analyzeImage() {
            if (!selectedFile) {
                alert('Please select an image first!');
                return;
            }
            
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            const statsResult = document.getElementById('statsResult');
            
            loading.classList.add('show');
            result.classList.remove('show');
            statsResult.classList.remove('show');
            
            const formData = new FormData();
            formData.append('image', selectedFile);
            
            try {
                const response = await fetch('/identify-global', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                loading.classList.remove('show');
                
                if (data.success) {
                    displayGlobalResults(data);
                } else {
                    displayError(data.error || 'Global identification failed');
                }
                
            } catch (error) {
                loading.classList.remove('show');
                displayError('Connection error: ' + error.message);
            }
        }
        
        function displayGlobalResults(data) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            const plant = data.plant_info;
            
            resultContent.innerHTML = `
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: ${(plant.confidence * 100)}%"></div>
                </div>
                <div style="text-align: center; margin-bottom: 1rem;">
                    <strong>Confidence: ${(plant.confidence * 100).toFixed(1)}%</strong>
                </div>
                
                <div class="plant-info">
                    <div class="info-section">
                        <h4>🌿 Plant Information</h4>
                        <p><strong>Name:</strong> ${plant.plant_name}</p>
                        <p><strong>Scientific:</strong> ${plant.scientific_name}</p>
                        <p><strong>Family:</strong> ${plant.family || 'Unknown'}</p>
                        <p><strong>Genus:</strong> ${plant.genus || 'Unknown'}</p>
                    </div>
                    
                    <div class="info-section">
                        <h4>🏥 Medicinal Uses</h4>
                        <ul>
                            ${plant.medicinal_uses.map(use => `<li>${use}</li>`).join('')}
                        </ul>
                    </div>
                    
                    <div class="info-section">
                        <h4>🌍 Local Names</h4>
                        ${Object.entries(plant.local_names || {}).map(([lang, name]) => 
                            `<p><strong>${lang}:</strong> ${name}</p>`
                        ).join('') || '<p>No local names available</p>'}
                    </div>
                    
                    <div class="info-section">
                        <h4>⚠️ Safety Information</h4>
                        <p>${plant.safety_info.warning}</p>
                        <p><strong>Verified:</strong> ${plant.safety_info.verified ? 'Yes' : 'No'}</p>
                    </div>
                </div>
                
                <div style="margin-top: 1rem;">
                    <h4>🔍 AI Sources Used:</h4>
                    <div class="sources">
                        ${plant.all_sources.map(source => `<span class="source-tag">${source}</span>`).join('')}
                    </div>
                </div>
                
                <div style="margin-top: 1rem; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                    <h4>📚 Database Status:</h4>
                    <p>${data.database_action}</p>
                    <p><strong>Processing Time:</strong> ${data.processing_time.toFixed(2)} seconds</p>
                </div>
            `;
            
            result.classList.add('show');
        }
        
        async function showStats() {
            try {
                const response = await fetch('/learning-stats');
                const data = await response.json();
                
                if (data.success) {
                    displayStats(data.stats);
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }
        
        function displayStats(stats) {
            const statsResult = document.getElementById('statsResult');
            const statsContent = document.getElementById('statsContent');
            
            statsContent.innerHTML = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${stats.total_plants}</div>
                        <div class="stat-label">Total Plants</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.auto_added_plants}</div>
                        <div class="stat-label">Auto Added</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.verified_plants}</div>
                        <div class="stat-label">Verified</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${(stats.verification_rate * 100).toFixed(1)}%</div>
                        <div class="stat-label">Verification Rate</div>
                    </div>
                </div>
                
                <div style="margin-top: 2rem;">
                    <h4>🏆 Most Identified Plants:</h4>
                    <ul>
                        ${stats.top_identified_plants.map(plant => 
                            `<li><strong>${plant.plant_name}</strong> (${plant.identification_count} times)</li>`
                        ).join('')}
                    </ul>
                </div>
            `;
            
            document.getElementById('result').classList.remove('show');
            statsResult.classList.add('show');
        }
        
        function displayError(message) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `
                <div class="status error">
                    ❌ Error: ${message}
                </div>
            `;
            
            result.classList.add('show');
        }
        
        function clearResults() {
            selectedFile = null;
            document.getElementById('result').classList.remove('show');
            document.getElementById('statsResult').classList.remove('show');
            document.getElementById('loading').classList.remove('show');
            
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">📸</div>
                <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">Upload Any Plant Image</div>
                <p>Our AI will identify it globally and add it to the learning database</p>
                <input type="file" id="fileInput" accept="image/*">
            `;
            
            // Re-attach event listener
            document.getElementById('fileInput').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    selectedFile = file;
                    displayImagePreview(file);
                }
            });
        }
        
        // Load stats on page load
        window.addEventListener('load', function() {
            // Auto-load stats after a short delay
            setTimeout(showStats, 1000);
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Serve the enhanced main page"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Global Plant AI Recognition System is running',
        'version': '2.0.0',
        'features': [
            'Global plant identification',
            'Multiple AI APIs',
            'Auto-learning database',
            'Medicinal properties'
        ]
    })

@app.route('/identify-global', methods=['POST'])
async def identify_global():
    """Global plant identification endpoint"""
    start_time = datetime.now()
    
    try:
        if 'image' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No image file provided'
            }), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Read image data
        image_data = file.read()
        filename = secure_filename(file.filename)
        
        # Use global AI to identify plant
        plant_info = await global_ai.identify_plant_globally(image_data, filename)
        
        # Determine database action
        if plant_info.get('confidence', 0) >= global_ai.confidence_threshold:
            database_action = "✅ Plant automatically added to learning database"
        else:
            database_action = "⚠️ Low confidence - plant not added to database"
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return jsonify({
            'success': True,
            'plant_info': plant_info,
            'database_action': database_action,
            'processing_time': processing_time,
            'message': 'Global plant identification completed'
        })
        
    except Exception as e:
        logger.error(f"Global identification error: {e}")
        return jsonify({
            'success': False,
            'error': f'Processing error: {str(e)}'
        }), 500

@app.route('/learning-stats')
def learning_stats():
    """Get learning database statistics"""
    try:
        stats = db.get_learning_statistics()
        
        return jsonify({
            'success': True,
            'stats': stats,
            'message': 'Learning statistics retrieved successfully'
        })
        
    except Exception as e:
        logger.error(f"Stats error: {e}")
        return jsonify({
            'success': False,
            'error': f'Failed to get statistics: {str(e)}'
        }), 500

@app.route('/search-plants')
def search_plants():
    """Search plants in the database"""
    try:
        query = request.args.get('q', '')
        limit = int(request.args.get('limit', 10))
        
        if not query:
            return jsonify({
                'success': False,
                'error': 'Search query is required'
            }), 400
        
        results = db.search_plants_by_name(query, limit)
        
        return jsonify({
            'success': True,
            'results': results,
            'total': len(results),
            'query': query
        })
        
    except Exception as e:
        logger.error(f"Search error: {e}")
        return jsonify({
            'success': False,
            'error': f'Search failed: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🌿" * 60)
    print("🌿 GLOBAL PLANT AI RECOGNITION SYSTEM")
    print("🌿 Enhanced Backend with Global Identification")
    print("🌿" * 60)
    print()
    print("🚀 Features:")
    print("   🌍 Global plant identification using multiple AI APIs")
    print("   🤖 PlantNet, Plant.id, Google Vision integration")
    print("   📚 Auto-learning database that grows with each upload")
    print("   💊 Comprehensive medicinal plant information")
    print("   🔍 Advanced search and statistics")
    print()
    print("🌐 Open your browser and go to: http://127.0.0.1:5000")
    print("📱 Upload ANY plant image and get global identification!")
    print()
    
    app.run(host='127.0.0.1', port=5000, debug=True)
