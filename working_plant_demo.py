#!/usr/bin/env python3
"""
🌱 Working Plant AI Demo
Simple, self-contained demo that works without template issues
"""

import os
import json
import random
import time
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024

# Create upload directory
Path('uploads').mkdir(exist_ok=True)

# Mock plant database
PLANTS_DB = {
    "aloe_vera": {
        "common_name": "Aloe Vera",
        "scientific_name": "Aloe vera",
        "local_name": "Ghritkumari",
        "family": "Asphodelaceae",
        "medicinal_uses": ["Skin healing", "Digestive health", "Anti-inflammatory", "Wound healing"],
        "description": "Succulent plant known for its healing gel and medicinal properties",
        "habitat": "Arid regions, cultivated worldwide",
        "parts_used": "Leaves (gel and latex)",
        "preparation": "Fresh gel applied topically, juice consumed orally",
        "precautions": "May cause allergic reactions in some individuals"
    },
    "tulsi": {
        "common_name": "Holy Basil",
        "scientific_name": "Ocimum tenuiflorum",
        "local_name": "Tulsi",
        "family": "Lamiaceae",
        "medicinal_uses": ["Respiratory health", "Stress relief", "Immunity booster", "Fever reduction"],
        "description": "Sacred plant in Hindu tradition with numerous health benefits",
        "habitat": "Tropical and subtropical regions",
        "parts_used": "Leaves, seeds, roots",
        "preparation": "Tea, powder, fresh leaves",
        "precautions": "May interact with blood-thinning medications"
    },
    "neem": {
        "common_name": "Neem",
        "scientific_name": "Azadirachta indica",
        "local_name": "Neem",
        "family": "Meliaceae",
        "medicinal_uses": ["Antibacterial", "Antifungal", "Skin conditions", "Dental health"],
        "description": "Versatile medicinal tree with powerful antimicrobial properties",
        "habitat": "Native to Indian subcontinent",
        "parts_used": "Leaves, bark, seeds, oil",
        "preparation": "Oil, powder, decoction, paste",
        "precautions": "Avoid during pregnancy and breastfeeding"
    },
    "turmeric": {
        "common_name": "Turmeric",
        "scientific_name": "Curcuma longa",
        "local_name": "Haldi",
        "family": "Zingiberaceae",
        "medicinal_uses": ["Anti-inflammatory", "Antioxidant", "Digestive health", "Joint pain"],
        "description": "Golden spice with powerful anti-inflammatory compounds",
        "habitat": "Tropical regions of Asia",
        "parts_used": "Rhizome (underground stem)",
        "preparation": "Powder, fresh root, extract",
        "precautions": "May increase bleeding risk, avoid with gallstones"
    },
    "ginger": {
        "common_name": "Ginger",
        "scientific_name": "Zingiber officinale",
        "local_name": "Adrak",
        "family": "Zingiberaceae",
        "medicinal_uses": ["Digestive aid", "Anti-nausea", "Anti-inflammatory", "Cold relief"],
        "description": "Aromatic root used for digestive and respiratory health",
        "habitat": "Tropical regions, widely cultivated",
        "parts_used": "Fresh or dried rhizome",
        "preparation": "Tea, powder, fresh slices, oil",
        "precautions": "May interact with blood thinners"
    }
}

def simulate_plant_detection(image_path):
    """Simulate plant detection with random results"""
    print(f"🔍 Analyzing image: {image_path}")
    time.sleep(random.uniform(1.0, 2.5))
    
    # 70% chance of detecting a known plant
    if random.random() < 0.7:
        plant_key = random.choice(list(PLANTS_DB.keys()))
        plant_info = PLANTS_DB[plant_key]
        confidence = random.uniform(0.75, 0.95)
        
        print(f"✅ Detected: {plant_info['common_name']} ({confidence:.2f})")
        return {
            "status": "known_plant",
            "plant_info": plant_info,
            "confidence": confidence,
            "message": "Plant identified from existing database"
        }
    else:
        # Generate new plant info
        print("🧠 Generating new plant information...")
        time.sleep(random.uniform(2.0, 3.0))
        
        new_plants = ["Ashwagandha", "Brahmi", "Giloy", "Amla", "Fenugreek", "Mint", "Coriander"]
        families = ["Solanaceae", "Plantaginaceae", "Menispermaceae", "Phyllanthaceae", "Fabaceae"]
        
        plant_name = random.choice(new_plants)
        
        plant_info = {
            "common_name": plant_name,
            "scientific_name": f"Generated {plant_name.lower()}",
            "local_name": plant_name,
            "family": random.choice(families),
            "medicinal_uses": ["Stress relief", "Immunity booster", "Digestive health"],
            "description": f"AI-generated medicinal plant: {plant_name}",
            "habitat": "Various regions",
            "parts_used": "Leaves, roots",
            "preparation": "Tea, powder",
            "precautions": "Consult healthcare provider",
            "discovery_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        print(f"✅ Generated new plant: {plant_name}")
        return {
            "status": "new_plant_generated",
            "plant_info": plant_info,
            "synthetic_images_count": 10,
            "augmented_images_count": 50,
            "message": f"New plant '{plant_name}' discovered and added to dataset"
        }

# HTML template embedded in Python
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌱 AI Medicinal Plant Recognition</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container { max-width: 900px; margin: 0 auto; }
        .header { 
            text-align: center; background: rgba(255,255,255,0.95); color: #333; 
            padding: 40px; border-radius: 20px; margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; color: #2c5530; }
        .header p { font-size: 1.2em; color: #666; }
        .demo-badge { 
            background: #ff6b6b; color: white; padding: 5px 15px; 
            border-radius: 20px; font-size: 0.9em; margin-top: 10px; display: inline-block;
        }
        .upload-area { 
            border: 3px dashed #4CAF50; border-radius: 15px; padding: 50px; 
            text-align: center; background: rgba(255,255,255,0.95); margin-bottom: 20px;
            cursor: pointer; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .upload-area:hover { 
            background: rgba(255,255,255,1); transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .upload-area h3 { color: #2c5530; margin-bottom: 15px; font-size: 1.5em; }
        .upload-area p { color: #666; font-size: 1.1em; }
        .result { 
            background: rgba(255,255,255,0.95); padding: 30px; border-radius: 15px; 
            margin-top: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .loading { 
            display: none; text-align: center; padding: 40px;
            background: rgba(255,255,255,0.95); border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .loading h3 { color: #2c5530; margin-bottom: 15px; }
        .spinner { 
            border: 4px solid #f3f3f3; border-top: 4px solid #4CAF50;
            border-radius: 50%; width: 40px; height: 40px;
            animation: spin 1s linear infinite; margin: 20px auto;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .plant-info { 
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0); 
            padding: 20px; border-radius: 10px; margin: 15px 0;
            border-left: 5px solid #4CAF50;
        }
        .plant-info h4 { color: #2c5530; margin-bottom: 10px; font-size: 1.3em; }
        .medicinal-uses { 
            background: linear-gradient(135deg, #fff3cd, #ffeaa7); 
            padding: 15px; border-radius: 8px; margin: 15px 0;
            border-left: 5px solid #ffc107;
        }
        .medicinal-uses h4 { color: #856404; margin-bottom: 10px; }
        .confidence { font-weight: bold; color: #28a745; font-size: 1.1em; }
        .processing-time { color: #6c757d; font-size: 0.9em; margin-top: 15px; }
        .stats { 
            display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 15px; margin: 15px 0; 
        }
        .stat-card { 
            background: rgba(255,255,255,0.8); padding: 15px; border-radius: 10px; 
            text-align: center; border: 2px solid #e0e0e0;
        }
        .stat-number { font-size: 1.5em; font-weight: bold; color: #4CAF50; }
        .stat-label { color: #666; font-size: 0.9em; }
        .features { 
            background: rgba(255,255,255,0.9); padding: 20px; border-radius: 15px; 
            margin: 20px 0; box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .features h3 { color: #2c5530; margin-bottom: 15px; }
        .feature-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; }
        .feature-item { 
            padding: 10px; background: #f8f9fa; border-radius: 5px; 
            border-left: 3px solid #4CAF50; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌱 AI Medicinal Plant Recognition</h1>
            <p>Self-Learning Plant Identification with Auto-Dataset Generation</p>
            <div class="demo-badge">WORKING DEMO</div>
        </div>
        
        <div class="features">
            <h3>🚀 System Features</h3>
            <div class="feature-list">
                <div class="feature-item">🔍 <strong>Plant Detection</strong> - Identifies known plants instantly</div>
                <div class="feature-item">🧠 <strong>AI Generation</strong> - Creates info for unknown plants</div>
                <div class="feature-item">🎨 <strong>Dataset Creation</strong> - Auto-generates training data</div>
                <div class="feature-item">📈 <strong>Self-Learning</strong> - Improves with each discovery</div>
                <div class="feature-item">💊 <strong>Medicinal Info</strong> - Detailed plant properties</div>
                <div class="feature-item">🌐 <strong>Web Interface</strong> - Easy-to-use system</div>
            </div>
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>📸 Upload Plant Image</h3>
            <p>Click here or drag & drop your plant image to test the AI system</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="uploadImage()">
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <h3>🧠 AI Processing...</h3>
            <p>Analyzing plant image and generating information...</p>
            <p><small>This may take a few seconds as the AI processes the image</small></p>
        </div>
        
        <div id="results"></div>
    </div>
    
    <script>
        function uploadImage() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;
            
            const formData = new FormData();
            formData.append('image', file);
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').innerHTML = '';
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                displayResults(data);
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').innerHTML = 
                    '<div class="result"><h3>❌ Error</h3><p>' + error + '</p></div>';
            });
        }
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            let html = '<div class="result">';
            
            if (data.status === 'known_plant') {
                html += '<h3>✅ Known Plant Identified</h3>';
                html += `<p class="confidence">Confidence: ${(data.confidence * 100).toFixed(1)}%</p>`;
                html += '<p>This plant was found in the existing database.</p>';
            } else {
                html += '<h3>🆕 New Plant Discovered & Added to Dataset</h3>';
                html += '<div class="stats">';
                html += `<div class="stat-card"><div class="stat-number">${data.synthetic_images_count}</div><div class="stat-label">Synthetic Images</div></div>`;
                html += `<div class="stat-card"><div class="stat-number">${data.augmented_images_count}</div><div class="stat-label">Total Training Images</div></div>`;
                html += `<div class="stat-card"><div class="stat-number">NEW</div><div class="stat-label">Plant Species</div></div>`;
                html += '</div>';
            }
            
            const plantInfo = data.plant_info;
            html += `
                <div class="plant-info">
                    <h4>🌿 ${plantInfo.common_name}</h4>
                    <p><strong>Scientific Name:</strong> ${plantInfo.scientific_name}</p>
                    <p><strong>Local Name:</strong> ${plantInfo.local_name || 'N/A'}</p>
                    <p><strong>Family:</strong> ${plantInfo.family || 'N/A'}</p>
                    <p><strong>Habitat:</strong> ${plantInfo.habitat || 'N/A'}</p>
                    <p><strong>Parts Used:</strong> ${plantInfo.parts_used || 'N/A'}</p>
                    <p><strong>Preparation:</strong> ${plantInfo.preparation || 'N/A'}</p>
                    <p><strong>Description:</strong> ${plantInfo.description || 'N/A'}</p>
                </div>
            `;
            
            if (plantInfo.medicinal_uses && plantInfo.medicinal_uses.length > 0) {
                html += '<div class="medicinal-uses"><h4>💊 Medicinal Uses:</h4><ul>';
                plantInfo.medicinal_uses.forEach(use => {
                    html += `<li>${use}</li>`;
                });
                html += '</ul></div>';
            }
            
            if (plantInfo.precautions) {
                html += `<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 3px solid #dc3545;">`;
                html += `<strong>⚠️ Precautions:</strong> ${plantInfo.precautions}`;
                html += '</div>';
            }
            
            html += `<p class="processing-time">⏱️ Processing completed</p>`;
            html += `<p><em>${data.message}</em></p>`;
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Main page"""
    return HTML_TEMPLATE

@app.route('/upload', methods=['POST'])
def upload_image():
    """Handle image upload and processing"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if file:
            # Save uploaded file
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Process the image
            start_time = time.time()
            result = simulate_plant_detection(filepath)
            result['processing_time'] = time.time() - start_time
            
            # Clean up uploaded file
            try:
                os.remove(filepath)
            except:
                pass
            
            return jsonify(result)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'AI Plant Recognition Demo is running',
        'version': '1.0.0'
    })

if __name__ == '__main__':
    print("🌱 AI Medicinal Plant Recognition System - Working Demo")
    print("=" * 70)
    print("🚀 Features:")
    print("  • Plant detection simulation (70% known, 30% new)")
    print("  • AI plant identification and info generation")
    print("  • Medicinal properties database")
    print("  • Self-learning dataset expansion simulation")
    print("  • Beautiful web interface")
    print("=" * 70)
    print("📱 Open your browser and go to: http://localhost:5000")
    print("🌿 Upload plant images to test the AI system!")
    print("💡 This demo simulates the complete AI pipeline")
    print("=" * 70)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
