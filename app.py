"""
Medicinal Plant Recognition System
A Streamlit web application for identifying medicinal plants using YOLOv8
"""

import streamlit as st
import cv2
import numpy as np
from PIL import Image
import torch
from ultralytics import YOLO
import os
from plant_database import get_plant_info, get_all_plant_names, search_plants_by_use
import plotly.express as px
import pandas as pd

# Page configuration
st.set_page_config(
    page_title="🌿 Medicinal Plant Recognition System",
    page_icon="🌿",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #2E8B57;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .plant-card {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .confidence-bar {
        background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #0abde3);
        height: 20px;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    .sidebar-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
    }
    .prediction-container {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        padding: 2rem;
        border-radius: 20px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_model():
    """Load the trained YOLOv8 model"""
    model_path = "best.pt"
    if os.path.exists(model_path):
        try:
            model = YOLO(model_path)
            return model
        except Exception as e:
            st.error(f"Error loading model: {str(e)}")
            return None
    else:
        st.error("Model file 'best.pt' not found. Please train the model first.")
        return None

def preprocess_image(image):
    """Preprocess the uploaded image for prediction"""
    # Convert PIL image to OpenCV format
    image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    
    # Resize image to model input size
    image_resized = cv2.resize(image_cv, (640, 640))
    
    return image_resized

def predict_plant(model, image):
    """Make prediction on the uploaded image"""
    try:
        # Run inference
        results = model(image, conf=0.25, iou=0.45)
        
        predictions = []
        
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Get class ID and confidence
                    class_id = int(box.cls[0])
                    confidence = float(box.conf[0])
                    
                    # Get class name
                    class_name = model.names[class_id]
                    
                    # Get bounding box coordinates
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    
                    predictions.append({
                        'class_name': class_name,
                        'confidence': confidence,
                        'bbox': [x1, y1, x2, y2]
                    })
        
        # Sort predictions by confidence
        predictions.sort(key=lambda x: x['confidence'], reverse=True)
        
        return predictions
    
    except Exception as e:
        st.error(f"Error during prediction: {str(e)}")
        return []

def draw_predictions(image, predictions):
    """Draw bounding boxes and labels on the image"""
    image_with_boxes = image.copy()
    
    for pred in predictions:
        x1, y1, x2, y2 = [int(coord) for coord in pred['bbox']]
        confidence = pred['confidence']
        class_name = pred['class_name']
        
        # Draw bounding box
        cv2.rectangle(image_with_boxes, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # Draw label
        label = f"{class_name}: {confidence:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
        cv2.rectangle(image_with_boxes, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), (0, 255, 0), -1)
        cv2.putText(image_with_boxes, label, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
    
    return image_with_boxes

def display_plant_info(plant_name, confidence):
    """Display detailed information about the identified plant"""
    plant_info = get_plant_info(plant_name)
    
    st.markdown(f"""
    <div class="plant-card">
        <h2 style="color: #2E8B57; margin-bottom: 1rem;">🌿 {plant_name}</h2>
        <div style="margin-bottom: 1rem;">
            <strong>Confidence:</strong> {confidence:.1%}
            <div class="confidence-bar" style="width: {confidence*100}%;"></div>
        </div>
        <p><strong>🔬 Scientific Name:</strong> <em>{plant_info['scientific_name']}</em></p>
        <p><strong>🏠 Local Name:</strong> {plant_info['local_name']}</p>
        <p><strong>💊 Medicinal Uses:</strong> {plant_info['medicinal_uses']}</p>
        <p><strong>📝 Description:</strong> {plant_info['description']}</p>
    </div>
    """, unsafe_allow_html=True)

def main():
    """Main application function"""
    
    # Header
    st.markdown('<h1 class="main-header">🌿 Medicinal Plant Recognition System</h1>', 
                unsafe_allow_html=True)
    
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem; color: #666;">
        Upload an image of a medicinal plant to identify it and learn about its therapeutic properties
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown('<div class="sidebar-content">', unsafe_allow_html=True)
        st.markdown("### 🔧 Settings")
        
        # Model status
        model = load_model()
        if model:
            st.success("✅ Model loaded successfully")
            st.info(f"📊 Supports {len(model.names)} plant species")
        else:
            st.error("❌ Model not available")
            st.stop()
        
        st.markdown("### 📊 Dataset Info")
        st.info(f"🌱 Total Plants: 144 species")
        st.info(f"🎯 Model: YOLOv8")
        st.info(f"📸 Input Size: 640x640")
        
        st.markdown("### 🔍 Plant Search")
        search_term = st.text_input("Search by medicinal use:")
        if search_term:
            matching_plants = search_plants_by_use(search_term)
            if matching_plants:
                st.write(f"Found {len(matching_plants)} plants:")
                for plant_name, info in matching_plants[:5]:  # Show top 5
                    st.write(f"• {plant_name}")
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Main content
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("### 📤 Upload Plant Image")
        uploaded_file = st.file_uploader(
            "Choose an image...", 
            type=['jpg', 'jpeg', 'png'],
            help="Upload a clear image of a medicinal plant"
        )
        
        if uploaded_file is not None:
            # Display uploaded image
            image = Image.open(uploaded_file)
            st.image(image, caption="Uploaded Image", use_column_width=True)
            
            # Preprocess image
            processed_image = preprocess_image(image)
            
            # Make prediction
            with st.spinner("🔍 Analyzing plant..."):
                predictions = predict_plant(model, processed_image)
            
            if predictions:
                # Draw predictions on image
                image_with_boxes = draw_predictions(processed_image, predictions)
                
                # Convert back to RGB for display
                image_with_boxes_rgb = cv2.cvtColor(image_with_boxes, cv2.COLOR_BGR2RGB)
                
                st.markdown("### 🎯 Detection Results")
                st.image(image_with_boxes_rgb, caption="Detected Plants", use_column_width=True)
    
    with col2:
        if uploaded_file is not None and predictions:
            st.markdown('<div class="prediction-container">', unsafe_allow_html=True)
            st.markdown("### 🌿 Plant Identification Results")
            
            # Display top 3 predictions
            for i, pred in enumerate(predictions[:3]):
                plant_name = pred['class_name']
                confidence = pred['confidence']
                
                if i == 0:
                    st.markdown("#### 🥇 Top Match")
                elif i == 1:
                    st.markdown("#### 🥈 Second Match")
                else:
                    st.markdown("#### 🥉 Third Match")
                
                display_plant_info(plant_name, confidence)
                
                if i < len(predictions) - 1:
                    st.markdown("---")
            
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Confidence chart
            if len(predictions) > 1:
                st.markdown("### 📊 Confidence Comparison")
                
                chart_data = pd.DataFrame({
                    'Plant': [pred['class_name'] for pred in predictions[:5]],
                    'Confidence': [pred['confidence'] for pred in predictions[:5]]
                })
                
                fig = px.bar(
                    chart_data, 
                    x='Confidence', 
                    y='Plant',
                    orientation='h',
                    color='Confidence',
                    color_continuous_scale='Viridis'
                )
                fig.update_layout(height=300)
                st.plotly_chart(fig, use_container_width=True)
        
        elif uploaded_file is not None:
            st.markdown('<div class="prediction-container">', unsafe_allow_html=True)
            st.warning("🔍 No plants detected in the image. Please try with a clearer image of a medicinal plant.")
            st.markdown('</div>', unsafe_allow_html=True)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; margin-top: 2rem;">
        <p>🌿 Medicinal Plant Recognition System | Powered by YOLOv8 & Streamlit</p>
        <p>⚠️ <strong>Disclaimer:</strong> This tool is for educational purposes only. 
        Always consult healthcare professionals before using any medicinal plants.</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
