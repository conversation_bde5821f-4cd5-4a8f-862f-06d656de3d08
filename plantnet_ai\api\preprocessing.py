"""
Image Preprocessing Module
Handles image validation, preprocessing, and optimization for plant identification
"""

import os
import io
import logging
from typing import <PERSON>ple, Set, Union, Optional
from pathlib import Path

import numpy as np
from PIL import Image, ImageOps, ExifTags
import cv2
from werkzeug.datastructures import FileStorage

logger = logging.getLogger(__name__)

class ImagePreprocessor:
    """
    Advanced image preprocessing for plant identification
    Handles validation, normalization, and optimization
    """
    
    def __init__(self, 
                 target_size: Tuple[int, int] = (224, 224),
                 allowed_extensions: Set[str] = None,
                 max_file_size: int = 16 * 1024 * 1024,  # 16MB
                 quality_threshold: float = 0.3):
        """
        Initialize the image preprocessor
        
        Args:
            target_size: Target image size for model input
            allowed_extensions: Set of allowed file extensions
            max_file_size: Maximum file size in bytes
            quality_threshold: Minimum image quality score (0-1)
        """
        self.target_size = target_size
        self.allowed_extensions = allowed_extensions or {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        self.max_file_size = max_file_size
        self.quality_threshold = quality_threshold
        
        # ImageNet normalization parameters
        self.imagenet_mean = np.array([0.485, 0.456, 0.406])
        self.imagenet_std = np.array([0.229, 0.224, 0.225])
        
        logger.info(f"✅ ImagePreprocessor initialized with target size: {target_size}")
    
    def is_valid_file(self, file: FileStorage) -> bool:
        """
        Validate uploaded file
        
        Args:
            file: Uploaded file object
            
        Returns:
            True if file is valid, False otherwise
        """
        try:
            # Check if file exists and has content
            if not file or not file.filename:
                logger.warning("⚠️ No file or filename provided")
                return False
            
            # Check file extension
            file_ext = file.filename.rsplit('.', 1)[-1].lower()
            if file_ext not in self.allowed_extensions:
                logger.warning(f"⚠️ Unsupported file extension: {file_ext}")
                return False
            
            # Check file size (if available)
            if hasattr(file, 'content_length') and file.content_length:
                if file.content_length > self.max_file_size:
                    logger.warning(f"⚠️ File too large: {file.content_length} bytes")
                    return False
            
            # Try to open and validate image
            file.seek(0)  # Reset file pointer
            try:
                with Image.open(file.stream) as img:
                    # Check if it's a valid image
                    img.verify()
                    
                    # Reset file pointer after verify
                    file.seek(0)
                    
                    # Check image dimensions
                    with Image.open(file.stream) as img:
                        width, height = img.size
                        if width < 32 or height < 32:
                            logger.warning(f"⚠️ Image too small: {width}x{height}")
                            return False
                        
                        if width > 4096 or height > 4096:
                            logger.warning(f"⚠️ Image too large: {width}x{height}")
                            return False
                
                file.seek(0)  # Reset for actual processing
                return True
                
            except Exception as e:
                logger.warning(f"⚠️ Invalid image file: {e}")
                return False
            
        except Exception as e:
            logger.error(f"❌ File validation error: {e}")
            return False
    
    def fix_image_orientation(self, image: Image.Image) -> Image.Image:
        """
        Fix image orientation based on EXIF data
        
        Args:
            image: PIL Image object
            
        Returns:
            Corrected PIL Image
        """
        try:
            # Get EXIF data
            exif = image._getexif()
            
            if exif is not None:
                # Find orientation tag
                orientation_tag = None
                for tag, value in ExifTags.TAGS.items():
                    if value == 'Orientation':
                        orientation_tag = tag
                        break
                
                if orientation_tag and orientation_tag in exif:
                    orientation = exif[orientation_tag]
                    
                    # Apply rotation based on orientation
                    if orientation == 2:
                        image = image.transpose(Image.FLIP_LEFT_RIGHT)
                    elif orientation == 3:
                        image = image.rotate(180, expand=True)
                    elif orientation == 4:
                        image = image.transpose(Image.FLIP_TOP_BOTTOM)
                    elif orientation == 5:
                        image = image.transpose(Image.FLIP_LEFT_RIGHT).rotate(90, expand=True)
                    elif orientation == 6:
                        image = image.rotate(270, expand=True)
                    elif orientation == 7:
                        image = image.transpose(Image.FLIP_LEFT_RIGHT).rotate(270, expand=True)
                    elif orientation == 8:
                        image = image.rotate(90, expand=True)
            
            return image
            
        except Exception as e:
            logger.warning(f"⚠️ Could not fix image orientation: {e}")
            return image
    
    def assess_image_quality(self, image: np.ndarray) -> float:
        """
        Assess image quality using various metrics
        
        Args:
            image: Image as numpy array
            
        Returns:
            Quality score between 0 and 1
        """
        try:
            # Convert to grayscale for analysis
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            else:
                gray = image
            
            # Calculate Laplacian variance (focus measure)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            focus_score = min(laplacian_var / 1000.0, 1.0)  # Normalize
            
            # Calculate brightness distribution
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist_norm = hist / hist.sum()
            
            # Avoid very dark or very bright images
            dark_pixels = hist_norm[:50].sum()
            bright_pixels = hist_norm[200:].sum()
            brightness_score = 1.0 - max(dark_pixels, bright_pixels)
            
            # Calculate contrast using standard deviation
            contrast_score = min(gray.std() / 64.0, 1.0)  # Normalize
            
            # Combined quality score
            quality_score = (focus_score * 0.5 + brightness_score * 0.3 + contrast_score * 0.2)
            
            return float(np.clip(quality_score, 0.0, 1.0))
            
        except Exception as e:
            logger.warning(f"⚠️ Could not assess image quality: {e}")
            return 0.5  # Default medium quality
    
    def preprocess_image(self, image: Union[Image.Image, np.ndarray]) -> np.ndarray:
        """
        Preprocess image for model inference
        
        Args:
            image: PIL Image or numpy array
            
        Returns:
            Preprocessed image as numpy array
        """
        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                # Fix orientation
                image = self.fix_image_orientation(image)
                
                # Convert to RGB if needed
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # Convert to numpy array
                img_array = np.array(image)
            else:
                img_array = image.copy()
            
            # Ensure RGB format
            if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                # Assume it's already RGB
                pass
            elif len(img_array.shape) == 3 and img_array.shape[2] == 4:
                # Convert RGBA to RGB
                img_array = img_array[:, :, :3]
            else:
                raise ValueError(f"Unsupported image shape: {img_array.shape}")
            
            # Assess quality
            quality_score = self.assess_image_quality(img_array)
            if quality_score < self.quality_threshold:
                logger.warning(f"⚠️ Low image quality detected: {quality_score:.3f}")
            
            # Resize image
            img_resized = cv2.resize(img_array, self.target_size, interpolation=cv2.INTER_LANCZOS4)
            
            # Normalize to [0, 1]
            img_normalized = img_resized.astype(np.float32) / 255.0
            
            # Apply ImageNet normalization
            img_final = (img_normalized - self.imagenet_mean) / self.imagenet_std
            
            return img_final
            
        except Exception as e:
            logger.error(f"❌ Image preprocessing failed: {e}")
            raise
    
    def preprocess_uploaded_file(self, file: FileStorage) -> np.ndarray:
        """
        Preprocess uploaded file for model inference
        
        Args:
            file: Uploaded file object
            
        Returns:
            Preprocessed image as numpy array
        """
        try:
            # Validate file
            if not self.is_valid_file(file):
                raise ValueError("Invalid file")
            
            # Read file content
            file.seek(0)
            file_content = file.read()
            
            # Open image
            image = Image.open(io.BytesIO(file_content))
            
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            logger.debug(f"✅ Preprocessed uploaded file: {file.filename}")
            
            return processed_image
            
        except Exception as e:
            logger.error(f"❌ Failed to preprocess uploaded file: {e}")
            raise
    
    def preprocess_from_path(self, image_path: Union[str, Path]) -> np.ndarray:
        """
        Preprocess image from file path
        
        Args:
            image_path: Path to image file
            
        Returns:
            Preprocessed image as numpy array
        """
        try:
            # Open image
            image = Image.open(image_path)
            
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            logger.debug(f"✅ Preprocessed image from path: {image_path}")
            
            return processed_image
            
        except Exception as e:
            logger.error(f"❌ Failed to preprocess image from path: {e}")
            raise
    
    def create_thumbnail(self, image: Image.Image, size: Tuple[int, int] = (150, 150)) -> Image.Image:
        """
        Create thumbnail of image
        
        Args:
            image: PIL Image object
            size: Thumbnail size
            
        Returns:
            Thumbnail as PIL Image
        """
        try:
            # Create thumbnail while maintaining aspect ratio
            thumbnail = image.copy()
            thumbnail.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Create a square thumbnail with padding if needed
            square_thumbnail = Image.new('RGB', size, (255, 255, 255))
            
            # Calculate position to center the thumbnail
            x = (size[0] - thumbnail.width) // 2
            y = (size[1] - thumbnail.height) // 2
            
            square_thumbnail.paste(thumbnail, (x, y))
            
            return square_thumbnail
            
        except Exception as e:
            logger.error(f"❌ Failed to create thumbnail: {e}")
            raise
    
    def batch_preprocess(self, images: list) -> list:
        """
        Preprocess a batch of images
        
        Args:
            images: List of images (PIL Images, numpy arrays, or file paths)
            
        Returns:
            List of preprocessed images
        """
        processed_images = []
        
        for i, image in enumerate(images):
            try:
                if isinstance(image, str) or isinstance(image, Path):
                    processed = self.preprocess_from_path(image)
                elif isinstance(image, FileStorage):
                    processed = self.preprocess_uploaded_file(image)
                else:
                    processed = self.preprocess_image(image)
                
                processed_images.append(processed)
                
            except Exception as e:
                logger.error(f"❌ Failed to preprocess image {i}: {e}")
                # Skip failed images
                continue
        
        logger.info(f"✅ Batch preprocessing completed: {len(processed_images)}/{len(images)} images")
        
        return processed_images

if __name__ == '__main__':
    # Test preprocessor
    preprocessor = ImagePreprocessor()
    print("✅ ImagePreprocessor test successful!")
    print(f"Target size: {preprocessor.target_size}")
    print(f"Allowed extensions: {preprocessor.allowed_extensions}")
