/* PlantNet AI - Main Stylesheet */

/* Root Variables */
:root {
    --primary-color: #28a745;
    --secondary-color: #20c997;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    --transition-base: all 0.3s ease;
}

/* Base Styles */
body {
    font-family: var(--font-family-sans-serif);
    line-height: 1.6;
    color: var(--dark-color);
    padding-top: 76px; /* Account for fixed navbar */
}

.main-content {
    min-height: calc(100vh - 200px);
}

/* Custom Bootstrap Overrides */
.bg-gradient-success {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition-base);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.text-success {
    color: var(--primary-color) !important;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
    transition: var(--transition-base);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    transition: var(--transition-base);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: white;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
    left: 0;
}

/* Cards and Components */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition-base);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition-base);
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-outline-success {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-success:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition-base);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition-base);
    cursor: pointer;
    background-color: #f8f9fa;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(40, 167, 69, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.file-upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Image Preview */
.image-preview {
    position: relative;
    display: inline-block;
    margin: 0.5rem;
}

.image-preview img {
    max-width: 150px;
    max-height: 150px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.image-preview .remove-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-base);
}

.image-preview .remove-btn:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

/* Organ Tags */
.organ-tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0.25rem;
}

.organ-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.organ-option {
    padding: 0.5rem 1rem;
    border: 2px solid #ced4da;
    border-radius: var(--border-radius);
    background-color: white;
    cursor: pointer;
    transition: var(--transition-base);
    font-weight: 500;
}

.organ-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(40, 167, 69, 0.05);
}

.organ-option.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

/* Results */
.result-card {
    border-left: 4px solid var(--primary-color);
    transition: var(--transition-base);
}

.result-card:hover {
    border-left-color: var(--secondary-color);
}

.confidence-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--danger-color) 0%, var(--warning-color) 50%, var(--success-color) 100%);
    transition: width 0.5s ease;
}

.species-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding-top: 70px;
    }
    
    .file-upload-area {
        padding: 2rem 1rem;
    }
    
    .file-upload-icon {
        font-size: 2rem;
    }
    
    .image-preview img {
        max-width: 100px;
        max-height: 100px;
    }
    
    .organ-selector {
        justify-content: center;
    }
    
    .species-image {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 576px) {
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
    
    .display-6 {
        font-size: 1.5rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-success {
    border-color: var(--primary-color) !important;
}

.shadow-success {
    box-shadow: 0 0.5rem 1rem rgba(40, 167, 69, 0.15) !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2d3748;
        --dark-color: #f7fafc;
    }
    
    body {
        background-color: #1a202c;
        color: var(--dark-color);
    }
    
    .card {
        background-color: var(--light-color);
        color: var(--dark-color);
    }
    
    .file-upload-area {
        background-color: var(--light-color);
        color: var(--dark-color);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .loading-overlay {
        display: none !important;
    }
    
    body {
        padding-top: 0;
    }
    
    .main-content {
        min-height: auto;
    }
}
