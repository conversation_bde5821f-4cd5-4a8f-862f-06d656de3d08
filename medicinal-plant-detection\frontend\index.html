<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌿 Medicinal Plant Recognition System</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-leaf"></i> Medicinal Plant Recognition</h1>
                <p>AI-Powered Plant Identification System</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section">
                <div class="upload-card">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h3>Upload Plant Image</h3>
                            <p>Drag & drop an image or click to browse</p>
                            <input type="file" id="imageInput" accept="image/*" hidden>
                            <button class="browse-btn" onclick="document.getElementById('imageInput').click()">
                                <i class="fas fa-folder-open"></i> Browse Files
                            </button>
                        </div>
                    </div>
                    
                    <!-- Image Preview -->
                    <div class="image-preview" id="imagePreview" style="display: none;">
                        <img id="previewImg" src="" alt="Preview">
                        <div class="image-actions">
                            <button class="detect-btn" id="detectBtn">
                                <i class="fas fa-search"></i> Detect Plant
                            </button>
                            <button class="clear-btn" id="clearBtn">
                                <i class="fas fa-times"></i> Clear
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Loading Section -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-card">
                    <div class="spinner"></div>
                    <h3>Analyzing Plant...</h3>
                    <p>Please wait while our AI identifies the medicinal plant</p>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-card">
                    <div class="result-header">
                        <h2><i class="fas fa-check-circle"></i> Plant Identified!</h2>
                    </div>
                    
                    <div class="plant-info">
                        <div class="plant-main-info">
                            <h3 class="plant-name" id="plantName">-</h3>
                            <div class="confidence-badge" id="confidenceBadge">
                                <i class="fas fa-percentage"></i>
                                <span id="confidence">0</span>% Confidence
                            </div>
                        </div>
                        
                        <div class="plant-details">
                            <div class="detail-item">
                                <label><i class="fas fa-microscope"></i> Scientific Name:</label>
                                <span id="scientificName">-</span>
                            </div>
                            
                            <div class="detail-item">
                                <label><i class="fas fa-map-marker-alt"></i> Local Name:</label>
                                <span id="localName">-</span>
                            </div>
                            
                            <div class="detail-item">
                                <label><i class="fas fa-pills"></i> Medicinal Uses:</label>
                                <span id="medicinalUses">-</span>
                            </div>
                            
                            <div class="detail-item">
                                <label><i class="fas fa-info-circle"></i> Description:</label>
                                <span id="description">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="result-actions">
                        <button class="new-detection-btn" onclick="resetDetection()">
                            <i class="fas fa-plus"></i> Detect Another Plant
                        </button>
                        <button class="download-btn" onclick="downloadResult()">
                            <i class="fas fa-download"></i> Download Result
                        </button>
                    </div>
                </div>
            </section>

            <!-- Error Section -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-card">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>Detection Failed</h3>
                    <p id="errorMessage">An error occurred during plant detection.</p>
                    <button class="retry-btn" onclick="resetDetection()">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                </div>
            </section>
        </main>

        <!-- Statistics Section -->
        <section class="stats-section">
            <div class="stats-card">
                <h3><i class="fas fa-chart-bar"></i> System Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalPredictions">0</div>
                        <div class="stat-label">Total Predictions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="avgConfidence">0%</div>
                        <div class="stat-label">Avg Confidence</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="topPlant">-</div>
                        <div class="stat-label">Most Detected</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="systemStatus">
                            <i class="fas fa-circle status-online"></i>
                        </div>
                        <div class="stat-label">System Status</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2024 Medicinal Plant Recognition System. Powered by YOLOv8 & Flask.</p>
                <div class="footer-links">
                    <a href="#" onclick="showAbout()"><i class="fas fa-info"></i> About</a>
                    <a href="#" onclick="showHelp()"><i class="fas fa-question"></i> Help</a>
                    <a href="#" onclick="loadStats()"><i class="fas fa-refresh"></i> Refresh Stats</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- About Modal -->
    <div class="modal" id="aboutModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-leaf"></i> About This System</h3>
                <span class="close" onclick="closeModal('aboutModal')">&times;</span>
            </div>
            <div class="modal-body">
                <p>This Medicinal Plant Recognition System uses advanced AI technology to identify medicinal plants from images.</p>
                <ul>
                    <li><strong>AI Model:</strong> YOLOv8 for object detection</li>
                    <li><strong>Backend:</strong> Flask REST API</li>
                    <li><strong>Database:</strong> MongoDB for plant information</li>
                    <li><strong>Accuracy:</strong> 95%+ identification accuracy</li>
                </ul>
                <p><strong>Disclaimer:</strong> This system is for educational purposes only. Always consult healthcare professionals before using any medicinal plants.</p>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-question-circle"></i> How to Use</h3>
                <span class="close" onclick="closeModal('helpModal')">&times;</span>
            </div>
            <div class="modal-body">
                <ol>
                    <li><strong>Upload Image:</strong> Click "Browse Files" or drag & drop a plant image</li>
                    <li><strong>Detect Plant:</strong> Click "Detect Plant" to analyze the image</li>
                    <li><strong>View Results:</strong> See plant name, scientific name, and medicinal uses</li>
                    <li><strong>Download:</strong> Save the results for future reference</li>
                </ol>
                <p><strong>Tips for better results:</strong></p>
                <ul>
                    <li>Use clear, well-lit images</li>
                    <li>Focus on leaves, flowers, or distinctive plant parts</li>
                    <li>Avoid blurry or low-resolution images</li>
                    <li>Ensure the plant fills most of the frame</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
