"""
MongoDB Database Manager for Medicinal Plant Recognition System
Handles all database operations including CRUD operations for plant data
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, DuplicateKeyError
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    print("PyMongo not installed. Install with: pip install pymongo")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MedicinalPlantDB:
    """MongoDB database manager for medicinal plants"""
    
    def __init__(self):
        """Initialize database connection"""
        self.client = None
        self.db = None
        self.collection = None
        self.connected = False
        
        if PYMONGO_AVAILABLE:
            self.connect()
        else:
            logger.warning("PyMongo not available. Database operations will be simulated.")
    
    def connect(self):
        """Connect to MongoDB"""
        try:
            # Get connection details from environment
            mongodb_uri = os.getenv('MONGODB_URI')
            database_name = os.getenv('DATABASE_NAME', 'medicinal_plants_db')
            collection_name = os.getenv('COLLECTION_NAME', 'plants')
            
            if not mongodb_uri:
                logger.error("MONGODB_URI not found in environment variables")
                return False
            
            # Create MongoDB client
            self.client = MongoClient(mongodb_uri, serverSelectionTimeoutMS=5000)
            
            # Test connection
            self.client.admin.command('ping')
            
            # Get database and collection
            self.db = self.client[database_name]
            self.collection = self.db[collection_name]
            
            self.connected = True
            logger.info(f"Successfully connected to MongoDB: {database_name}.{collection_name}")
            
            # Create indexes for better performance
            self.create_indexes()
            
            return True
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self.connected = False
            return False
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            self.connected = False
            return False
    
    def create_indexes(self):
        """Create database indexes for better performance"""
        try:
            if not self.connected:
                return
            
            # Create indexes
            self.collection.create_index("plant_name", unique=True)
            self.collection.create_index("scientific_name")
            self.collection.create_index("local_name")
            self.collection.create_index([("medicinal_uses", "text")])
            self.collection.create_index("created_at")
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
    
    def insert_plant(self, plant_data: Dict[str, Any]) -> bool:
        """Insert a single plant record"""
        try:
            if not self.connected:
                logger.warning("Database not connected. Simulating insert operation.")
                return True
            
            # Add metadata
            plant_data['created_at'] = datetime.utcnow()
            plant_data['updated_at'] = datetime.utcnow()
            
            # Insert document
            result = self.collection.insert_one(plant_data)
            
            if result.inserted_id:
                logger.info(f"Plant '{plant_data.get('plant_name')}' inserted successfully")
                return True
            else:
                logger.error("Failed to insert plant data")
                return False
                
        except DuplicateKeyError:
            logger.warning(f"Plant '{plant_data.get('plant_name')}' already exists")
            return False
        except Exception as e:
            logger.error(f"Error inserting plant: {e}")
            return False
    
    def insert_many_plants(self, plants_data: List[Dict[str, Any]]) -> int:
        """Insert multiple plant records"""
        try:
            if not self.connected:
                logger.warning("Database not connected. Simulating bulk insert operation.")
                return len(plants_data)
            
            # Add metadata to all records
            current_time = datetime.utcnow()
            for plant in plants_data:
                plant['created_at'] = current_time
                plant['updated_at'] = current_time
            
            # Insert documents
            result = self.collection.insert_many(plants_data, ordered=False)
            
            inserted_count = len(result.inserted_ids)
            logger.info(f"Successfully inserted {inserted_count} plants")
            return inserted_count
            
        except Exception as e:
            logger.error(f"Error in bulk insert: {e}")
            return 0
    
    def get_plant_by_name(self, plant_name: str) -> Optional[Dict[str, Any]]:
        """Get plant information by name"""
        try:
            if not self.connected:
                # Fallback to local data
                from plant_database import get_plant_info
                return get_plant_info(plant_name)
            
            result = self.collection.find_one({"plant_name": plant_name})
            
            if result:
                # Remove MongoDB ObjectId for JSON serialization
                result.pop('_id', None)
                return result
            else:
                logger.warning(f"Plant '{plant_name}' not found in database")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving plant: {e}")
            return None
    
    def search_plants_by_use(self, medicinal_use: str) -> List[Dict[str, Any]]:
        """Search plants by medicinal use"""
        try:
            if not self.connected:
                # Fallback to local data
                from plant_database import search_plants_by_use
                return search_plants_by_use(medicinal_use)
            
            # Text search in medicinal_uses field
            results = self.collection.find(
                {"$text": {"$search": medicinal_use}},
                {"_id": 0}  # Exclude ObjectId
            ).limit(20)
            
            return list(results)
            
        except Exception as e:
            logger.error(f"Error searching plants: {e}")
            return []
    
    def get_all_plants(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get all plants with optional limit"""
        try:
            if not self.connected:
                # Fallback to local data
                from plant_database import MEDICINAL_PLANTS_DB
                plants = []
                for name, info in list(MEDICINAL_PLANTS_DB.items())[:limit]:
                    plant_data = info.copy()
                    plant_data['plant_name'] = name
                    plants.append(plant_data)
                return plants
            
            results = self.collection.find({}, {"_id": 0}).limit(limit)
            return list(results)
            
        except Exception as e:
            logger.error(f"Error retrieving all plants: {e}")
            return []
    
    def update_plant(self, plant_name: str, update_data: Dict[str, Any]) -> bool:
        """Update plant information"""
        try:
            if not self.connected:
                logger.warning("Database not connected. Simulating update operation.")
                return True
            
            # Add update timestamp
            update_data['updated_at'] = datetime.utcnow()
            
            result = self.collection.update_one(
                {"plant_name": plant_name},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                logger.info(f"Plant '{plant_name}' updated successfully")
                return True
            else:
                logger.warning(f"No changes made to plant '{plant_name}'")
                return False
                
        except Exception as e:
            logger.error(f"Error updating plant: {e}")
            return False
    
    def delete_plant(self, plant_name: str) -> bool:
        """Delete a plant record"""
        try:
            if not self.connected:
                logger.warning("Database not connected. Simulating delete operation.")
                return True
            
            result = self.collection.delete_one({"plant_name": plant_name})
            
            if result.deleted_count > 0:
                logger.info(f"Plant '{plant_name}' deleted successfully")
                return True
            else:
                logger.warning(f"Plant '{plant_name}' not found for deletion")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting plant: {e}")
            return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            if not self.connected:
                return {
                    "connected": False,
                    "total_plants": 0,
                    "database_size": "Unknown",
                    "last_updated": "Unknown"
                }
            
            total_plants = self.collection.count_documents({})
            
            # Get database stats
            db_stats = self.db.command("dbStats")
            
            return {
                "connected": True,
                "total_plants": total_plants,
                "database_size": f"{db_stats.get('dataSize', 0) / 1024 / 1024:.2f} MB",
                "storage_size": f"{db_stats.get('storageSize', 0) / 1024 / 1024:.2f} MB",
                "indexes": db_stats.get('indexes', 0),
                "collections": db_stats.get('collections', 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {"connected": False, "error": str(e)}
    
    def populate_database_from_local(self) -> bool:
        """Populate MongoDB with data from local plant_database.py"""
        try:
            from plant_database import MEDICINAL_PLANTS_DB
            
            plants_data = []
            for plant_name, plant_info in MEDICINAL_PLANTS_DB.items():
                if plant_name == '-':  # Skip unknown entry
                    continue
                
                plant_data = {
                    'plant_name': plant_name,
                    'scientific_name': plant_info.get('scientific_name', ''),
                    'local_name': plant_info.get('local_name', ''),
                    'medicinal_uses': plant_info.get('medicinal_uses', ''),
                    'description': plant_info.get('description', ''),
                    'source': 'local_database',
                    'verified': True
                }
                plants_data.append(plant_data)
            
            # Clear existing data
            if self.connected:
                self.collection.delete_many({})
                logger.info("Cleared existing plant data")
            
            # Insert new data
            inserted_count = self.insert_many_plants(plants_data)
            
            logger.info(f"Database populated with {inserted_count} plants from local database")
            return True
            
        except Exception as e:
            logger.error(f"Error populating database: {e}")
            return False
    
    def add_prediction_log(self, prediction_data: Dict[str, Any]) -> bool:
        """Log plant prediction for analytics"""
        try:
            if not self.connected:
                return True
            
            # Use predictions collection
            predictions_collection = self.db['predictions']
            
            prediction_data['timestamp'] = datetime.utcnow()
            
            result = predictions_collection.insert_one(prediction_data)
            
            if result.inserted_id:
                logger.info("Prediction logged successfully")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error logging prediction: {e}")
            return False
    
    def get_prediction_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get prediction analytics for the last N days"""
        try:
            if not self.connected:
                return {"error": "Database not connected"}
            
            from datetime import timedelta
            
            start_date = datetime.utcnow() - timedelta(days=days)
            
            predictions_collection = self.db['predictions']
            
            # Aggregate predictions
            pipeline = [
                {"$match": {"timestamp": {"$gte": start_date}}},
                {"$group": {
                    "_id": "$predicted_plant",
                    "count": {"$sum": 1},
                    "avg_confidence": {"$avg": "$confidence"}
                }},
                {"$sort": {"count": -1}},
                {"$limit": 10}
            ]
            
            results = list(predictions_collection.aggregate(pipeline))
            
            total_predictions = predictions_collection.count_documents(
                {"timestamp": {"$gte": start_date}}
            )
            
            return {
                "total_predictions": total_predictions,
                "top_predicted_plants": results,
                "period_days": days
            }
            
        except Exception as e:
            logger.error(f"Error getting analytics: {e}")
            return {"error": str(e)}
    
    def close_connection(self):
        """Close database connection"""
        try:
            if self.client:
                self.client.close()
                self.connected = False
                logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing connection: {e}")

# Global database instance
db_manager = MedicinalPlantDB()

def get_db():
    """Get database manager instance"""
    return db_manager

if __name__ == "__main__":
    # Test database operations
    print("Testing Medicinal Plant Database Manager")
    print("=" * 50)
    
    # Test connection
    db = MedicinalPlantDB()
    
    # Get database stats
    stats = db.get_database_stats()
    print(f"Database Stats: {stats}")
    
    # Populate database if empty
    if stats.get('total_plants', 0) == 0:
        print("\nPopulating database from local data...")
        success = db.populate_database_from_local()
        if success:
            print("Database populated successfully!")
        else:
            print("Failed to populate database")
    
    # Test plant retrieval
    print("\nTesting plant retrieval...")
    aloe_info = db.get_plant_by_name("Aloe vera")
    if aloe_info:
        print(f"Found Aloe vera: {aloe_info['scientific_name']}")
    
    # Test search
    print("\nTesting search...")
    digestive_plants = db.search_plants_by_use("digestive")
    print(f"Found {len(digestive_plants)} plants for digestive use")
    
    # Close connection
    db.close_connection()
    print("\nDatabase test completed!")
