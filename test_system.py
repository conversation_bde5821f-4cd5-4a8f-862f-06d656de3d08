"""
Comprehensive test script for Medicinal Plant Recognition System
"""

import os
import sys
import unittest
import tempfile
import numpy as np
from PIL import Image
import cv2

class TestPlantDatabase(unittest.TestCase):
    """Test the plant database functionality"""
    
    def setUp(self):
        """Set up test environment"""
        from plant_database import MEDICINAL_PLANTS_DB
        self.db = MEDICINAL_PLANTS_DB
    
    def test_database_structure(self):
        """Test database structure and content"""
        # Check if database is not empty
        self.assertGreater(len(self.db), 0, "Database should not be empty")
        
        # Check if all required keys exist for each plant
        required_keys = ['scientific_name', 'local_name', 'medicinal_uses', 'description']
        
        for plant_name, plant_info in self.db.items():
            with self.subTest(plant=plant_name):
                for key in required_keys:
                    self.assertIn(key, plant_info, f"Missing key '{key}' for plant '{plant_name}'")
                    self.assertIsInstance(plant_info[key], str, f"Key '{key}' should be string for plant '{plant_name}'")
                    self.assertGreater(len(plant_info[key]), 0, f"Key '{key}' should not be empty for plant '{plant_name}'")
    
    def test_get_plant_info(self):
        """Test get_plant_info function"""
        from plant_database import get_plant_info
        
        # Test with existing plant
        if 'Aloe vera' in self.db:
            info = get_plant_info('Aloe vera')
            self.assertIsInstance(info, dict)
            self.assertIn('scientific_name', info)
        
        # Test with non-existing plant
        info = get_plant_info('NonExistentPlant')
        self.assertIsInstance(info, dict)
        self.assertEqual(info['scientific_name'], 'Unknown')
    
    def test_search_functionality(self):
        """Test search functionality"""
        from plant_database import search_plants_by_use
        
        # Test search with common term
        results = search_plants_by_use('skin')
        self.assertIsInstance(results, list)
        
        # Test search with non-existing term
        results = search_plants_by_use('nonexistentuse')
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 0)

class TestDatasetStructure(unittest.TestCase):
    """Test dataset structure and configuration"""
    
    def test_data_yaml_exists(self):
        """Test if data.yaml exists and is valid"""
        self.assertTrue(os.path.exists('data.yaml'), "data.yaml file should exist")
        
        import yaml
        with open('data.yaml', 'r') as f:
            data_config = yaml.safe_load(f)
        
        required_keys = ['train', 'val', 'nc', 'names']
        for key in required_keys:
            self.assertIn(key, data_config, f"Missing key '{key}' in data.yaml")
    
    def test_dataset_directories(self):
        """Test if dataset directories exist"""
        directories = ['train/images', 'train/labels', 'valid/images', 'valid/labels']
        
        for directory in directories:
            with self.subTest(directory=directory):
                self.assertTrue(os.path.exists(directory), f"Directory '{directory}' should exist")
                
                # Check if directory contains files
                files = os.listdir(directory)
                self.assertGreater(len(files), 0, f"Directory '{directory}' should not be empty")

class TestModelFunctionality(unittest.TestCase):
    """Test model loading and prediction functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.model_path = "best.pt"
        self.model_exists = os.path.exists(self.model_path)
    
    def test_model_file_exists(self):
        """Test if model file exists"""
        if not self.model_exists:
            self.skipTest("Model file 'best.pt' not found. Train the model first.")
        
        self.assertTrue(os.path.exists(self.model_path), "Model file should exist")
    
    def test_model_loading(self):
        """Test model loading"""
        if not self.model_exists:
            self.skipTest("Model file not found")
        
        try:
            from ultralytics import YOLO
            model = YOLO(self.model_path)
            self.assertIsNotNone(model, "Model should load successfully")
            self.assertIsNotNone(model.names, "Model should have class names")
        except Exception as e:
            self.fail(f"Model loading failed: {e}")
    
    def test_prediction_functionality(self):
        """Test prediction on a sample image"""
        if not self.model_exists:
            self.skipTest("Model file not found")
        
        # Create a sample image
        sample_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        try:
            from ultralytics import YOLO
            model = YOLO(self.model_path)
            
            # Run prediction
            results = model(sample_image, conf=0.25, verbose=False)
            self.assertIsNotNone(results, "Prediction should return results")
            
        except Exception as e:
            self.fail(f"Prediction failed: {e}")

class TestWebAppComponents(unittest.TestCase):
    """Test web application components"""
    
    def test_app_imports(self):
        """Test if all required modules can be imported"""
        required_modules = [
            'streamlit',
            'cv2',
            'numpy',
            'PIL',
            'torch',
            'ultralytics',
            'plotly',
            'pandas'
        ]
        
        for module in required_modules:
            with self.subTest(module=module):
                try:
                    if module == 'cv2':
                        import cv2
                    elif module == 'PIL':
                        import PIL
                    else:
                        __import__(module)
                except ImportError as e:
                    self.fail(f"Failed to import {module}: {e}")
    
    def test_app_file_exists(self):
        """Test if app.py exists"""
        self.assertTrue(os.path.exists('app.py'), "app.py file should exist")
    
    def test_plant_database_import(self):
        """Test if plant database can be imported"""
        try:
            from plant_database import get_plant_info, get_all_plant_names, search_plants_by_use
            
            # Test basic functionality
            all_plants = get_all_plant_names()
            self.assertIsInstance(all_plants, list)
            self.assertGreater(len(all_plants), 0)
            
        except ImportError as e:
            self.fail(f"Failed to import plant database: {e}")

class TestSystemIntegration(unittest.TestCase):
    """Test system integration and end-to-end functionality"""
    
    def test_complete_pipeline(self):
        """Test the complete prediction pipeline"""
        # Check if all components are available
        components = {
            'model': os.path.exists('best.pt'),
            'database': True,  # Always available
            'app': os.path.exists('app.py')
        }
        
        missing_components = [name for name, exists in components.items() if not exists]
        
        if missing_components:
            self.skipTest(f"Missing components: {missing_components}")
        
        # Test the pipeline
        try:
            # Import required modules
            from ultralytics import YOLO
            from plant_database import get_plant_info
            
            # Load model
            model = YOLO('best.pt')
            
            # Create test image
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            
            # Run prediction
            results = model(test_image, conf=0.25, verbose=False)
            
            # Process results
            predictions = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        class_name = model.names[class_id]
                        predictions.append({
                            'class_name': class_name,
                            'confidence': confidence
                        })
            
            # Test database lookup (even if no predictions)
            if predictions:
                plant_info = get_plant_info(predictions[0]['class_name'])
                self.assertIsInstance(plant_info, dict)
            
            # If we reach here, the pipeline works
            self.assertTrue(True, "Complete pipeline test passed")
            
        except Exception as e:
            self.fail(f"Pipeline test failed: {e}")

def run_performance_test():
    """Run performance tests"""
    print("\n🚀 Performance Tests")
    print("=" * 40)
    
    if not os.path.exists('best.pt'):
        print("⚠️  Model not found - skipping performance tests")
        return
    
    try:
        import time
        from ultralytics import YOLO
        
        # Load model
        start_time = time.time()
        model = YOLO('best.pt')
        load_time = time.time() - start_time
        print(f"📊 Model loading time: {load_time:.2f} seconds")
        
        # Test inference speed
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # Warm up
        model(test_image, verbose=False)
        
        # Measure inference time
        num_tests = 5
        total_time = 0
        
        for _ in range(num_tests):
            start_time = time.time()
            results = model(test_image, verbose=False)
            inference_time = time.time() - start_time
            total_time += inference_time
        
        avg_inference_time = total_time / num_tests
        print(f"📊 Average inference time: {avg_inference_time:.3f} seconds")
        print(f"📊 Inference FPS: {1/avg_inference_time:.1f}")
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")

def main():
    """Main test function"""
    print("🧪 Medicinal Plant Recognition System - Test Suite")
    print("=" * 60)
    
    # Run unit tests
    print("\n🔬 Running Unit Tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestPlantDatabase,
        TestDatasetStructure,
        TestModelFunctionality,
        TestWebAppComponents,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Run performance tests
    run_performance_test()
    
    # Summary
    print(f"\n📊 Test Summary")
    print("=" * 40)
    print(f"✅ Tests run: {result.testsRun}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"⚠️  Errors: {len(result.errors)}")
    print(f"⏭️  Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.wasSuccessful():
        print("\n🎉 All tests passed! System is ready for use.")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        
        if result.failures:
            print("\n❌ Failures:")
            for test, traceback in result.failures:
                print(f"   - {test}")
        
        if result.errors:
            print("\n⚠️  Errors:")
            for test, traceback in result.errors:
                print(f"   - {test}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
