"""
Advanced Augmentation Techniques for Medicinal Plant Dataset
Implements state-of-the-art augmentation methods for maximum diversity
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import random
import albumentations as A
from pathlib import Path
import logging
from typing import List, Tuple, Union
import json

logger = logging.getLogger(__name__)

class AdvancedAugmentation:
    """Advanced augmentation techniques for medicinal plant images"""
    
    def __init__(self):
        self.setup_albumentations_pipeline()
        self.augmentation_count = 0
    
    def setup_albumentations_pipeline(self):
        """Setup Albumentations augmentation pipeline"""
        self.transform = A.Compose([
            A.OneOf([
                A.RandomRotate90(p=0.5),
                <PERSON><PERSON>(limit=45, p=0.5),
                A.<PERSON>(p=0.3),
            ], p=0.8),
            
            A.OneOf([
                A.HorizontalFlip(p=0.5),
                A.<PERSON>ert<PERSON>(p=0.3),
                A.RandomRotate90(p=0.3),
            ], p=0.7),
            
            A<PERSON>([
                A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.8),
                <PERSON><PERSON>Value(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.8),
                A.CLAHE(clip_limit=2.0, tile_grid_size=(8, 8), p=0.5),
            ], p=0.9),
            
            A.OneOf([
                A.Blur(blur_limit=3, p=0.5),
                A.GaussianBlur(blur_limit=3, p=0.5),
                A.MotionBlur(blur_limit=3, p=0.3),
                A.MedianBlur(blur_limit=3, p=0.3),
            ], p=0.4),
            
            A.OneOf([
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.5),
                A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5), p=0.3),
                A.MultiplicativeNoise(multiplier=[0.9, 1.1], p=0.3),
            ], p=0.3),
            
            A.OneOf([
                A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.3),
                A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.3),
                A.OpticalDistortion(distort_limit=0.1, shift_limit=0.1, p=0.3),
            ], p=0.2),
            
            A.OneOf([
                A.RandomCrop(height=400, width=400, p=0.5),
                A.CenterCrop(height=450, width=450, p=0.3),
                A.Crop(x_min=0, y_min=0, x_max=400, y_max=400, p=0.2),
            ], p=0.3),
            
            A.Resize(height=512, width=512, p=1.0),
        ])
    
    def apply_albumentations(self, image: np.ndarray) -> np.ndarray:
        """Apply Albumentations augmentation pipeline"""
        try:
            augmented = self.transform(image=image)
            return augmented['image']
        except Exception as e:
            logger.warning(f"Albumentations failed: {e}")
            return image
    
    def apply_color_space_augmentation(self, image: np.ndarray) -> np.ndarray:
        """Apply color space transformations"""
        augmentations = [
            self.hsv_augmentation,
            self.lab_augmentation,
            self.rgb_channel_shift,
            self.gamma_correction,
            self.histogram_equalization
        ]
        
        aug_func = random.choice(augmentations)
        return aug_func(image)
    
    def hsv_augmentation(self, image: np.ndarray) -> np.ndarray:
        """HSV color space augmentation"""
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV).astype(np.float32)
        
        # Adjust hue
        hue_shift = random.uniform(-20, 20)
        hsv[:, :, 0] = (hsv[:, :, 0] + hue_shift) % 180
        
        # Adjust saturation
        sat_factor = random.uniform(0.8, 1.2)
        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * sat_factor, 0, 255)
        
        # Adjust value
        val_factor = random.uniform(0.9, 1.1)
        hsv[:, :, 2] = np.clip(hsv[:, :, 2] * val_factor, 0, 255)
        
        return cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
    
    def lab_augmentation(self, image: np.ndarray) -> np.ndarray:
        """LAB color space augmentation"""
        lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB).astype(np.float32)
        
        # Adjust L channel (lightness)
        l_factor = random.uniform(0.9, 1.1)
        lab[:, :, 0] = np.clip(lab[:, :, 0] * l_factor, 0, 255)
        
        # Adjust A and B channels (color)
        a_shift = random.uniform(-10, 10)
        b_shift = random.uniform(-10, 10)
        lab[:, :, 1] = np.clip(lab[:, :, 1] + a_shift, 0, 255)
        lab[:, :, 2] = np.clip(lab[:, :, 2] + b_shift, 0, 255)
        
        return cv2.cvtColor(lab.astype(np.uint8), cv2.COLOR_LAB2RGB)
    
    def rgb_channel_shift(self, image: np.ndarray) -> np.ndarray:
        """Shift RGB channels independently"""
        result = image.copy()
        
        for channel in range(3):
            shift = random.uniform(-20, 20)
            result[:, :, channel] = np.clip(result[:, :, channel] + shift, 0, 255)
        
        return result
    
    def gamma_correction(self, image: np.ndarray) -> np.ndarray:
        """Apply gamma correction"""
        gamma = random.uniform(0.7, 1.3)
        
        # Build lookup table
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
        
        return cv2.LUT(image, table)
    
    def histogram_equalization(self, image: np.ndarray) -> np.ndarray:
        """Apply histogram equalization"""
        # Convert to YUV
        yuv = cv2.cvtColor(image, cv2.COLOR_RGB2YUV)
        
        # Equalize Y channel
        yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
        
        return cv2.cvtColor(yuv, cv2.COLOR_YUV2RGB)
    
    def apply_geometric_augmentation(self, image: np.ndarray) -> np.ndarray:
        """Apply geometric transformations"""
        augmentations = [
            self.perspective_transform,
            self.affine_transform,
            self.elastic_deformation,
            self.barrel_distortion,
            self.pincushion_distortion
        ]
        
        aug_func = random.choice(augmentations)
        return aug_func(image)
    
    def perspective_transform(self, image: np.ndarray) -> np.ndarray:
        """Apply perspective transformation"""
        h, w = image.shape[:2]
        
        # Define source points
        src_points = np.float32([[0, 0], [w, 0], [w, h], [0, h]])
        
        # Add random perturbation
        max_offset = min(w, h) * 0.1
        dst_points = src_points + np.random.uniform(-max_offset, max_offset, src_points.shape)
        
        # Apply transformation
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        return cv2.warpPerspective(image, matrix, (w, h))
    
    def affine_transform(self, image: np.ndarray) -> np.ndarray:
        """Apply affine transformation"""
        h, w = image.shape[:2]
        
        # Random affine parameters
        angle = random.uniform(-15, 15)
        scale = random.uniform(0.9, 1.1)
        shear_x = random.uniform(-0.1, 0.1)
        shear_y = random.uniform(-0.1, 0.1)
        
        # Create transformation matrix
        center = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D(center, angle, scale)
        
        # Add shear
        M[0, 1] += shear_x
        M[1, 0] += shear_y
        
        return cv2.warpAffine(image, M, (w, h))
    
    def elastic_deformation(self, image: np.ndarray) -> np.ndarray:
        """Apply elastic deformation"""
        h, w = image.shape[:2]
        
        # Generate random displacement fields
        alpha = random.uniform(20, 40)
        sigma = random.uniform(4, 8)
        
        dx = np.random.randn(h, w) * alpha
        dy = np.random.randn(h, w) * alpha
        
        # Smooth the displacement fields
        dx = cv2.GaussianBlur(dx, (0, 0), sigma)
        dy = cv2.GaussianBlur(dy, (0, 0), sigma)
        
        # Create coordinate grids
        x, y = np.meshgrid(np.arange(w), np.arange(h))
        x_new = np.clip(x + dx, 0, w - 1).astype(np.float32)
        y_new = np.clip(y + dy, 0, h - 1).astype(np.float32)
        
        return cv2.remap(image, x_new, y_new, cv2.INTER_LINEAR)
    
    def barrel_distortion(self, image: np.ndarray) -> np.ndarray:
        """Apply barrel distortion"""
        h, w = image.shape[:2]
        
        # Distortion parameters
        k1 = random.uniform(-0.3, -0.1)
        k2 = random.uniform(-0.1, 0.1)
        
        # Camera matrix
        fx = fy = w
        cx, cy = w // 2, h // 2
        
        camera_matrix = np.array([[fx, 0, cx],
                                 [0, fy, cy],
                                 [0, 0, 1]], dtype=np.float32)
        
        dist_coeffs = np.array([k1, k2, 0, 0, 0], dtype=np.float32)
        
        return cv2.undistort(image, camera_matrix, dist_coeffs)
    
    def pincushion_distortion(self, image: np.ndarray) -> np.ndarray:
        """Apply pincushion distortion"""
        h, w = image.shape[:2]
        
        # Distortion parameters
        k1 = random.uniform(0.1, 0.3)
        
        # Camera matrix
        fx = fy = w
        cx, cy = w // 2, h // 2
        
        camera_matrix = np.array([[fx, 0, cx],
                                 [0, fy, cy],
                                 [0, 0, 1]], dtype=np.float32)
        
        dist_coeffs = np.array([k1, 0, 0, 0, 0], dtype=np.float32)
        
        return cv2.undistort(image, camera_matrix, dist_coeffs)
    
    def apply_texture_augmentation(self, image: np.ndarray) -> np.ndarray:
        """Apply texture-based augmentations"""
        augmentations = [
            self.add_texture_noise,
            self.apply_emboss_filter,
            self.apply_edge_enhancement,
            self.add_scratches,
            self.simulate_lighting_changes
        ]
        
        aug_func = random.choice(augmentations)
        return aug_func(image)
    
    def add_texture_noise(self, image: np.ndarray) -> np.ndarray:
        """Add texture-like noise"""
        h, w = image.shape[:2]
        
        # Generate Perlin-like noise
        noise = np.random.randn(h // 4, w // 4)
        noise = cv2.resize(noise, (w, h), interpolation=cv2.INTER_CUBIC)
        noise = (noise - noise.min()) / (noise.max() - noise.min()) * 50 - 25
        
        # Apply noise
        result = image.astype(np.float32) + noise[:, :, np.newaxis]
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def apply_emboss_filter(self, image: np.ndarray) -> np.ndarray:
        """Apply emboss filter"""
        kernel = np.array([[-2, -1, 0],
                          [-1, 1, 1],
                          [0, 1, 2]])
        
        embossed = cv2.filter2D(image, -1, kernel)
        
        # Blend with original
        alpha = random.uniform(0.1, 0.3)
        return cv2.addWeighted(image, 1 - alpha, embossed, alpha, 0)
    
    def apply_edge_enhancement(self, image: np.ndarray) -> np.ndarray:
        """Apply edge enhancement"""
        kernel = np.array([[0, -1, 0],
                          [-1, 5, -1],
                          [0, -1, 0]])
        
        enhanced = cv2.filter2D(image, -1, kernel)
        
        # Blend with original
        alpha = random.uniform(0.1, 0.3)
        return cv2.addWeighted(image, 1 - alpha, enhanced, alpha, 0)
    
    def add_scratches(self, image: np.ndarray) -> np.ndarray:
        """Add random scratches/lines"""
        result = image.copy()
        h, w = image.shape[:2]
        
        num_scratches = random.randint(1, 5)
        
        for _ in range(num_scratches):
            # Random line
            pt1 = (random.randint(0, w), random.randint(0, h))
            pt2 = (random.randint(0, w), random.randint(0, h))
            
            # Random color (darker than background)
            color = tuple(int(c * random.uniform(0.3, 0.7)) for c in [128, 128, 128])
            thickness = random.randint(1, 3)
            
            cv2.line(result, pt1, pt2, color, thickness)
        
        return result
    
    def simulate_lighting_changes(self, image: np.ndarray) -> np.ndarray:
        """Simulate different lighting conditions"""
        h, w = image.shape[:2]
        
        # Create gradient mask
        center_x, center_y = random.randint(w//4, 3*w//4), random.randint(h//4, 3*h//4)
        
        y, x = np.ogrid[:h, :w]
        mask = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        mask = mask / mask.max()
        
        # Apply lighting effect
        intensity = random.uniform(0.7, 1.3)
        lighting = intensity * (1 - mask * random.uniform(0.2, 0.5))
        
        result = image.astype(np.float32) * lighting[:, :, np.newaxis]
        return np.clip(result, 0, 255).astype(np.uint8)
