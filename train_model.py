"""
YOLOv8 Training Script for Medicinal Plant Recognition
This script trains a YOLOv8 model on the medicinal plant dataset.
"""

import os
import yaml
from ultralytics import YOLO
import torch
import matplotlib.pyplot as plt
from pathlib import Path
import shutil

def setup_training_environment():
    """Set up the training environment and check requirements"""
    print("🔧 Setting up training environment...")
    
    # Check if CUDA is available
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"💻 Using device: {device}")
    
    # Create models directory if it doesn't exist
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    return device

def validate_dataset():
    """Validate the dataset structure and configuration"""
    print("📊 Validating dataset...")
    
    # Check if data.yaml exists
    if not os.path.exists('data.yaml'):
        raise FileNotFoundError("data.yaml not found. Please ensure the dataset is properly configured.")
    
    # Load and validate data.yaml
    with open('data.yaml', 'r') as f:
        data_config = yaml.safe_load(f)
    
    required_keys = ['train', 'val', 'nc', 'names']
    for key in required_keys:
        if key not in data_config:
            raise ValueError(f"Missing required key '{key}' in data.yaml")
    
    # Check if directories exist
    train_dir = data_config['train'].replace('./train/images', 'train/images')
    val_dir = data_config['val'].replace('./valid/images', 'valid/images')
    
    if not os.path.exists(train_dir):
        raise FileNotFoundError(f"Training directory not found: {train_dir}")
    
    if not os.path.exists(val_dir):
        raise FileNotFoundError(f"Validation directory not found: {val_dir}")
    
    print(f"✅ Dataset validated successfully!")
    print(f"   - Number of classes: {data_config['nc']}")
    print(f"   - Training images directory: {train_dir}")
    print(f"   - Validation images directory: {val_dir}")
    
    return data_config

def train_yolov8_model(data_config, device='cpu'):
    """Train the YOLOv8 model"""
    print("🚀 Starting YOLOv8 training...")
    
    # Initialize YOLOv8 model with pretrained weights
    model = YOLO('yolov8n.pt')  # Using nano version for faster training
    
    # Training parameters
    training_params = {
        'data': 'data.yaml',
        'epochs': 100,  # Adjust based on your needs
        'imgsz': 640,
        'batch': 16,  # Adjust based on your GPU memory
        'device': device,
        'project': 'models',
        'name': 'medicinal_plants_yolov8',
        'save': True,
        'save_period': 10,  # Save checkpoint every 10 epochs
        'patience': 20,  # Early stopping patience
        'optimizer': 'AdamW',
        'lr0': 0.01,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        'pose': 12.0,
        'kobj': 1.0,
        'label_smoothing': 0.0,
        'nbs': 64,
        'overlap_mask': True,
        'mask_ratio': 4,
        'dropout': 0.0,
        'val': True,
        'plots': True,
        'verbose': True
    }
    
    print("📋 Training Configuration:")
    for key, value in training_params.items():
        print(f"   - {key}: {value}")
    
    # Start training
    try:
        results = model.train(**training_params)
        print("✅ Training completed successfully!")
        return results, model
    except Exception as e:
        print(f"❌ Training failed: {str(e)}")
        raise

def save_best_model(model, results):
    """Save the best model weights"""
    print("💾 Saving best model...")
    
    # The best model is automatically saved by YOLOv8
    # Copy it to a more accessible location
    best_model_path = "models/medicinal_plants_yolov8/weights/best.pt"
    
    if os.path.exists(best_model_path):
        # Copy to root directory for easy access
        shutil.copy2(best_model_path, "best.pt")
        print("✅ Best model saved as 'best.pt'")
        
        # Also save to models directory
        shutil.copy2(best_model_path, "models/best.pt")
        print("✅ Best model also saved in 'models/best.pt'")
    else:
        print("⚠️ Best model not found at expected location")

def plot_training_results():
    """Plot training results"""
    print("📈 Generating training plots...")
    
    results_dir = "models/medicinal_plants_yolov8"
    
    # Check if results exist
    if os.path.exists(f"{results_dir}/results.png"):
        print("✅ Training results plot saved in models/medicinal_plants_yolov8/results.png")
    
    if os.path.exists(f"{results_dir}/confusion_matrix.png"):
        print("✅ Confusion matrix saved in models/medicinal_plants_yolov8/confusion_matrix.png")

def validate_trained_model():
    """Validate the trained model"""
    print("🔍 Validating trained model...")
    
    if os.path.exists("best.pt"):
        model = YOLO("best.pt")
        
        # Run validation
        try:
            val_results = model.val(data='data.yaml')
            print("✅ Model validation completed!")
            print(f"   - mAP50: {val_results.box.map50:.4f}")
            print(f"   - mAP50-95: {val_results.box.map:.4f}")
            return val_results
        except Exception as e:
            print(f"⚠️ Validation failed: {str(e)}")
            return None
    else:
        print("❌ Best model not found!")
        return None

def main():
    """Main training function"""
    print("🌿 Medicinal Plant Recognition - YOLOv8 Training")
    print("=" * 50)
    
    try:
        # Setup environment
        device = setup_training_environment()
        
        # Validate dataset
        data_config = validate_dataset()
        
        # Train model
        results, model = train_yolov8_model(data_config, device)
        
        # Save best model
        save_best_model(model, results)
        
        # Plot results
        plot_training_results()
        
        # Validate trained model
        val_results = validate_trained_model()
        
        print("\n🎉 Training pipeline completed successfully!")
        print("📁 Files created:")
        print("   - best.pt (trained model weights)")
        print("   - models/medicinal_plants_yolov8/ (training results)")
        print("\n🚀 You can now use the trained model with the Streamlit app!")
        
    except Exception as e:
        print(f"\n❌ Training failed: {str(e)}")
        print("Please check the error message and try again.")

if __name__ == "__main__":
    main()
