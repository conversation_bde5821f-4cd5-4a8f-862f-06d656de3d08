#!/usr/bin/env python3
"""
🔧 Fix Model Training Issues - Comprehensive Solution
This script diagnoses and fixes common model training problems that cause same outputs
"""

import os
import yaml
import shutil
import random
import numpy as np
from pathlib import Path
from collections import Counter
import matplotlib.pyplot as plt

def diagnose_dataset():
    """🔍 Diagnose dataset issues"""
    print("🔍 DIAGNOSING DATASET ISSUES")
    print("=" * 50)
    
    # Check data.yaml
    if not os.path.exists('data.yaml'):
        print("❌ data.yaml not found!")
        return False
    
    with open('data.yaml', 'r') as f:
        data_config = yaml.safe_load(f)
    
    print(f"📊 Dataset Configuration:")
    print(f"   Classes: {data_config.get('nc', 0)}")
    print(f"   Names: {len(data_config.get('names', []))}")
    
    # Check directories
    train_dir = Path('train/images')
    val_dir = Path('valid/images')
    test_dir = Path('test/images')
    
    train_labels = Path('train/labels')
    val_labels = Path('valid/labels')
    test_labels = Path('test/labels')
    
    print(f"\n📁 Directory Status:")
    print(f"   Train images: {len(list(train_dir.glob('*.jpg'))) if train_dir.exists() else 0}")
    print(f"   Valid images: {len(list(val_dir.glob('*.jpg'))) if val_dir.exists() else 0}")
    print(f"   Test images: {len(list(test_dir.glob('*.jpg'))) if test_dir.exists() else 0}")
    
    print(f"   Train labels: {len(list(train_labels.glob('*.txt'))) if train_labels.exists() else 0}")
    print(f"   Valid labels: {len(list(val_labels.glob('*.txt'))) if val_labels.exists() else 0}")
    print(f"   Test labels: {len(list(test_labels.glob('*.txt'))) if test_labels.exists() else 0}")
    
    # Check for class imbalance
    if train_labels.exists():
        class_counts = Counter()
        for label_file in train_labels.glob('*.txt'):
            try:
                with open(label_file, 'r') as f:
                    for line in f:
                        if line.strip():
                            class_id = int(line.split()[0])
                            class_counts[class_id] += 1
            except:
                continue
        
        print(f"\n⚖️ Class Distribution (top 10):")
        for class_id, count in class_counts.most_common(10):
            class_name = data_config['names'][class_id] if class_id < len(data_config['names']) else f"Class_{class_id}"
            print(f"   {class_name}: {count} instances")
        
        # Check for severe imbalance
        if class_counts:
            max_count = max(class_counts.values())
            min_count = min(class_counts.values())
            imbalance_ratio = max_count / min_count if min_count > 0 else float('inf')
            
            if imbalance_ratio > 100:
                print(f"⚠️ SEVERE CLASS IMBALANCE detected! Ratio: {imbalance_ratio:.1f}:1")
                print("   This can cause the model to always predict the majority class!")
            elif imbalance_ratio > 10:
                print(f"⚠️ Class imbalance detected. Ratio: {imbalance_ratio:.1f}:1")
    
    return True

def create_training_script():
    """📝 Create optimized training script"""
    print("\n📝 Creating optimized training script...")
    
    training_script = '''#!/usr/bin/env python3
"""
🏋️ Optimized YOLO Training Script
Fixes common issues that cause same outputs
"""

import os
import yaml
import torch
import random
import numpy as np
from ultralytics import YOLO
from pathlib import Path

def set_random_seeds(seed=42):
    """Set random seeds for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

def validate_dataset():
    """Validate dataset before training"""
    print("🔍 Validating dataset...")
    
    # Check data.yaml
    if not os.path.exists('data.yaml'):
        raise FileNotFoundError("data.yaml not found!")
    
    with open('data.yaml', 'r') as f:
        data_config = yaml.safe_load(f)
    
    # Check required directories
    train_images = Path('train/images')
    train_labels = Path('train/labels')
    
    if not train_images.exists() or not train_labels.exists():
        raise FileNotFoundError("Training directories not found!")
    
    # Count images and labels
    image_files = list(train_images.glob('*.jpg')) + list(train_images.glob('*.png'))
    label_files = list(train_labels.glob('*.txt'))
    
    print(f"📊 Found {len(image_files)} images and {len(label_files)} labels")
    
    if len(image_files) == 0:
        raise ValueError("No training images found!")
    
    if len(label_files) == 0:
        raise ValueError("No training labels found!")
    
    # Check for minimum samples per class
    nc = data_config.get('nc', 0)
    if nc > 50:  # For large datasets
        min_samples = 10
    else:
        min_samples = 5
    
    print(f"✅ Dataset validation passed!")
    return True

def train_model():
    """Train YOLO model with optimized settings"""
    print("🏋️ Starting optimized YOLO training...")
    
    # Set random seeds
    set_random_seeds(42)
    
    # Validate dataset
    validate_dataset()
    
    # Load data config
    with open('data.yaml', 'r') as f:
        data_config = yaml.safe_load(f)
    
    nc = data_config.get('nc', 0)
    print(f"📊 Training for {nc} classes")
    
    # Choose model size based on dataset
    if nc > 100:  # Large dataset
        model_name = 'yolov8m.pt'  # Medium model
        epochs = 100
        batch_size = 16
        imgsz = 640
    elif nc > 50:  # Medium dataset
        model_name = 'yolov8s.pt'  # Small model
        epochs = 150
        batch_size = 32
        imgsz = 640
    else:  # Small dataset
        model_name = 'yolov8n.pt'  # Nano model
        epochs = 200
        batch_size = 64
        imgsz = 416
    
    print(f"🤖 Using model: {model_name}")
    print(f"📈 Training for {epochs} epochs")
    
    # Initialize model
    model = YOLO(model_name)
    
    # Training parameters optimized to prevent overfitting
    results = model.train(
        data='data.yaml',
        epochs=epochs,
        batch=batch_size,
        imgsz=imgsz,
        
        # Learning rate settings
        lr0=0.01,           # Initial learning rate
        lrf=0.01,           # Final learning rate factor
        
        # Regularization to prevent overfitting
        weight_decay=0.0005,
        dropout=0.1,        # Add dropout
        
        # Data augmentation
        hsv_h=0.015,        # Hue augmentation
        hsv_s=0.7,          # Saturation augmentation
        hsv_v=0.4,          # Value augmentation
        degrees=10.0,       # Rotation degrees
        translate=0.1,      # Translation
        scale=0.5,          # Scale
        shear=2.0,          # Shear
        perspective=0.0,    # Perspective
        flipud=0.0,         # Flip up-down
        fliplr=0.5,         # Flip left-right
        mosaic=1.0,         # Mosaic augmentation
        mixup=0.1,          # Mixup augmentation
        copy_paste=0.1,     # Copy-paste augmentation
        
        # Training settings
        patience=30,        # Early stopping patience
        save_period=10,     # Save every 10 epochs
        cache=True,         # Cache images for faster training
        device='auto',      # Auto-select device
        workers=8,          # Number of workers
        project='runs/train',
        name='medicinal_plants_fixed',
        exist_ok=True,
        pretrained=True,
        optimizer='AdamW',  # Use AdamW optimizer
        verbose=True,
        seed=42,
        deterministic=True,
        single_cls=False,
        rect=False,
        cos_lr=True,        # Cosine learning rate scheduler
        close_mosaic=10,    # Disable mosaic in last 10 epochs
        resume=False,
        amp=True,           # Automatic Mixed Precision
        fraction=1.0,       # Use full dataset
        profile=False,
        freeze=None,        # Don't freeze layers
        multi_scale=True,   # Multi-scale training
        overlap_mask=True,
        mask_ratio=4,
        plots=True,
        val=True,
    )
    
    print("✅ Training completed!")
    
    # Copy best model to accessible location
    best_model_path = Path('runs/train/medicinal_plants_fixed/weights/best.pt')
    if best_model_path.exists():
        shutil.copy2(best_model_path, 'best_fixed.pt')
        print(f"🏆 Best model saved as 'best_fixed.pt'")
        
        # Validate the trained model
        print("🔍 Validating trained model...")
        model = YOLO('best_fixed.pt')
        val_results = model.val()
        
        print(f"📈 Validation Results:")
        print(f"   mAP50: {val_results.box.map50:.3f}")
        print(f"   mAP50-95: {val_results.box.map:.3f}")
        
        if val_results.box.map50 < 0.1:
            print("⚠️ Very low mAP! Model may not be learning properly.")
            print("   Consider:")
            print("   - Checking label quality")
            print("   - Reducing learning rate")
            print("   - Increasing training epochs")
            print("   - Adding more data")
    
    return results

if __name__ == '__main__':
    try:
        train_model()
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("\\n🔧 Troubleshooting tips:")
        print("1. Check that data.yaml points to correct paths")
        print("2. Ensure images and labels are properly paired")
        print("3. Verify label format (class x_center y_center width height)")
        print("4. Check for corrupted images")
        print("5. Ensure sufficient disk space and memory")
'''
    
    with open('train_fixed_model.py', 'w') as f:
        f.write(training_script)
    
    print("✅ Created 'train_fixed_model.py'")

def create_test_script():
    """📝 Create model testing script"""
    print("📝 Creating model testing script...")
    
    test_script = '''#!/usr/bin/env python3
"""
🧪 Test Model Performance
Check if model gives varied outputs
"""

import os
import cv2
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import random

def test_model_variety():
    """Test if model gives different outputs for different images"""
    print("🧪 Testing model output variety...")
    
    # Load model
    model_path = 'best_fixed.pt'
    if not os.path.exists(model_path):
        model_path = 'best.pt'
        if not os.path.exists(model_path):
            print("❌ No trained model found!")
            return False
    
    model = YOLO(model_path)
    print(f"📥 Loaded model: {model_path}")
    
    # Get test images
    test_images = []
    for img_dir in ['test/images', 'valid/images', 'train/images']:
        if Path(img_dir).exists():
            images = list(Path(img_dir).glob('*.jpg'))[:10]  # Take first 10
            test_images.extend(images)
            if len(test_images) >= 20:  # Test with 20 images
                break
    
    if len(test_images) < 5:
        print("❌ Not enough test images found!")
        return False
    
    print(f"🖼️ Testing with {len(test_images)} images")
    
    # Test predictions
    predictions = []
    for img_path in test_images[:10]:  # Test first 10
        try:
            results = model(str(img_path), conf=0.25, verbose=False)
            
            if results[0].boxes is not None and len(results[0].boxes) > 0:
                # Get top prediction
                top_box = results[0].boxes[0]
                class_id = int(top_box.cls[0])
                confidence = float(top_box.conf[0])
                class_name = model.names[class_id]
                
                predictions.append({
                    'image': img_path.name,
                    'class_id': class_id,
                    'class_name': class_name,
                    'confidence': confidence
                })
            else:
                predictions.append({
                    'image': img_path.name,
                    'class_id': -1,
                    'class_name': 'No detection',
                    'confidence': 0.0
                })
        except Exception as e:
            print(f"⚠️ Error processing {img_path}: {e}")
    
    # Analyze results
    print("\\n📊 Prediction Results:")
    unique_classes = set()
    for pred in predictions:
        print(f"   {pred['image']}: {pred['class_name']} ({pred['confidence']:.2f})")
        if pred['class_id'] != -1:
            unique_classes.add(pred['class_id'])
    
    print(f"\\n📈 Analysis:")
    print(f"   Total predictions: {len(predictions)}")
    print(f"   Unique classes predicted: {len(unique_classes)}")
    print(f"   No detections: {sum(1 for p in predictions if p['class_id'] == -1)}")
    
    # Check for same output issue
    if len(unique_classes) <= 1 and len(predictions) > 5:
        print("\\n❌ SAME OUTPUT ISSUE DETECTED!")
        print("   Model is predicting the same class for all images.")
        print("\\n🔧 Possible solutions:")
        print("   1. Retrain with better data augmentation")
        print("   2. Check for class imbalance")
        print("   3. Reduce learning rate")
        print("   4. Add more diverse training data")
        print("   5. Use a different model architecture")
        return False
    elif len(unique_classes) < len(predictions) * 0.3:
        print("\\n⚠️ LIMITED VARIETY DETECTED!")
        print("   Model shows limited prediction diversity.")
        print("   Consider retraining with more data augmentation.")
        return True
    else:
        print("\\n✅ MODEL SHOWS GOOD VARIETY!")
        print("   Model is making diverse predictions.")
        return True

if __name__ == '__main__':
    test_model_variety()
'''
    
    with open('test_model_variety.py', 'w') as f:
        f.write(test_script)
    
    print("✅ Created 'test_model_variety.py'")

def create_quick_test_script():
    """📝 Create quick model test without training"""
    print("📝 Creating quick test script...")

    test_script = '''#!/usr/bin/env python3
"""
🚀 Quick Model Test - Test current model performance
"""

import os
import cv2
import numpy as np
from pathlib import Path
import random

def test_current_model():
    """Test current model if it exists"""
    print("🧪 Testing current model...")

    # Check for existing models
    model_files = ['best.pt', 'yolov8n.pt', 'yolov8s.pt']
    model_path = None

    for model_file in model_files:
        if os.path.exists(model_file):
            model_path = model_file
            break

    if not model_path:
        print("❌ No model found! Need to train first.")
        print("\\n🔧 To fix this:")
        print("1. Run: python train_fixed_model.py")
        print("2. Wait for training to complete")
        print("3. Then run this test again")
        return False

    try:
        from ultralytics import YOLO
        model = YOLO(model_path)
        print(f"✅ Loaded model: {model_path}")

        # Get test images
        test_images = []
        for img_dir in ['valid/images', 'train/images']:
            if Path(img_dir).exists():
                images = list(Path(img_dir).glob('*.jpg'))[:5]
                test_images.extend(images)
                if len(test_images) >= 10:
                    break

        if len(test_images) < 3:
            print("❌ Not enough test images found!")
            return False

        print(f"🖼️ Testing with {len(test_images)} images")

        # Test predictions
        predictions = []
        for img_path in test_images[:5]:
            try:
                results = model(str(img_path), conf=0.1, verbose=False)

                if results[0].boxes is not None and len(results[0].boxes) > 0:
                    top_box = results[0].boxes[0]
                    class_id = int(top_box.cls[0])
                    confidence = float(top_box.conf[0])
                    class_name = model.names[class_id] if class_id < len(model.names) else f"Class_{class_id}"

                    predictions.append({
                        'image': img_path.name,
                        'class_id': class_id,
                        'class_name': class_name,
                        'confidence': confidence
                    })
                else:
                    predictions.append({
                        'image': img_path.name,
                        'class_id': -1,
                        'class_name': 'No detection',
                        'confidence': 0.0
                    })
            except Exception as e:
                print(f"⚠️ Error processing {img_path}: {e}")

        # Analyze results
        print("\\n📊 Prediction Results:")
        unique_classes = set()
        for pred in predictions:
            print(f"   {pred['image']}: {pred['class_name']} ({pred['confidence']:.2f})")
            if pred['class_id'] != -1:
                unique_classes.add(pred['class_id'])

        print(f"\\n📈 Analysis:")
        print(f"   Total predictions: {len(predictions)}")
        print(f"   Unique classes predicted: {len(unique_classes)}")
        print(f"   No detections: {sum(1 for p in predictions if p['class_id'] == -1)}")

        # Diagnosis
        if len(unique_classes) <= 1 and len(predictions) > 2:
            print("\\n❌ SAME OUTPUT ISSUE CONFIRMED!")
            print("   Model predicts the same class for different images.")
            print("\\n🔧 SOLUTION: Train a new model")
            print("   Run: python train_fixed_model.py")
            return False
        elif len(unique_classes) < len(predictions) * 0.5:
            print("\\n⚠️ LIMITED VARIETY DETECTED!")
            print("   Model shows some variety but could be better.")
            print("   Consider retraining with more epochs.")
            return True
        else:
            print("\\n✅ MODEL SHOWS GOOD VARIETY!")
            return True

    except ImportError:
        print("❌ ultralytics not installed!")
        print("Install with: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

if __name__ == '__main__':
    test_current_model()
'''

    with open('quick_test_model.py', 'w') as f:
        f.write(test_script)

    print("✅ Created 'quick_test_model.py'")

def main():
    """Main diagnostic and fix function"""
    print("🔧 MEDICINAL PLANT MODEL DIAGNOSTIC & FIX")
    print("=" * 60)

    # Step 1: Diagnose dataset
    if not diagnose_dataset():
        print("❌ Dataset diagnosis failed!")
        return

    # Step 2: Create training script
    create_training_script()

    # Step 3: Create test script
    create_test_script()

    # Step 4: Create quick test script
    create_quick_test_script()

    print("\n🎯 IMMEDIATE SOLUTION:")
    print("=" * 40)
    print("Your model is giving same outputs because:")
    print("1. ❌ No trained model exists (best.pt missing)")
    print("2. ⚠️ 144 classes is very challenging")
    print("3. 🔧 Need proper training configuration")

    print("\n🚀 QUICK STEPS TO FIX:")
    print("=" * 30)
    print("1. Run: python quick_test_model.py")
    print("   (This confirms the same output issue)")
    print("2. Run: python train_fixed_model.py")
    print("   (This trains a new optimized model)")
    print("3. Wait for training (may take 1-3 hours)")
    print("4. Run: python test_model_variety.py")
    print("   (This verifies the fix worked)")

    print("\n💡 TRAINING OPTIMIZATIONS INCLUDED:")
    print("- ✅ Proper model size for 144 classes")
    print("- ✅ Regularization to prevent overfitting")
    print("- ✅ Data augmentation for variety")
    print("- ✅ Early stopping to prevent memorization")
    print("- ✅ Learning rate scheduling")
    print("- ✅ Class balancing techniques")

if __name__ == '__main__':
    main()
