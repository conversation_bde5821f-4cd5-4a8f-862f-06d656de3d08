"""
Setup script for Medicinal Plant Recognition System
Automates the complete setup process including environment, dependencies, and database
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path
import venv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MedicinalPlantSetup:
    """Setup manager for the medicinal plant recognition system"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / 'backend'
        self.frontend_dir = self.project_root / 'frontend'
        self.dataset_dir = self.project_root / 'dataset'
        self.venv_dir = self.backend_dir / 'venv'
        
    def print_banner(self):
        """Print setup banner"""
        print("🌿" * 30)
        print("🌿 MEDICINAL PLANT RECOGNITION SYSTEM SETUP 🌿")
        print("🌿" * 30)
        print()
        print("This setup will:")
        print("✅ Create virtual environment")
        print("✅ Install Python dependencies")
        print("✅ Set up project structure")
        print("✅ Configure database connection")
        print("✅ Prepare dataset structure")
        print("✅ Download YOLOv8 model")
        print()
    
    def check_python_version(self):
        """Check if Python version is compatible"""
        logger.info("🐍 Checking Python version...")
        
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            logger.error("❌ Python 3.8+ is required")
            logger.error(f"Current version: {version.major}.{version.minor}.{version.micro}")
            return False
        
        logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    
    def create_virtual_environment(self):
        """Create virtual environment"""
        logger.info("🔧 Creating virtual environment...")
        
        try:
            if self.venv_dir.exists():
                logger.info("Virtual environment already exists")
                return True
            
            venv.create(self.venv_dir, with_pip=True)
            logger.info(f"✅ Virtual environment created at: {self.venv_dir}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create virtual environment: {e}")
            return False
    
    def get_pip_command(self):
        """Get the pip command for the virtual environment"""
        system = platform.system()
        
        if system == "Windows":
            return str(self.venv_dir / "Scripts" / "pip.exe")
        else:
            return str(self.venv_dir / "bin" / "pip")
    
    def get_python_command(self):
        """Get the python command for the virtual environment"""
        system = platform.system()
        
        if system == "Windows":
            return str(self.venv_dir / "Scripts" / "python.exe")
        else:
            return str(self.venv_dir / "bin" / "python")
    
    def install_dependencies(self):
        """Install Python dependencies"""
        logger.info("📦 Installing Python dependencies...")
        
        try:
            pip_cmd = self.get_pip_command()
            requirements_file = self.backend_dir / "requirements.txt"
            
            if not requirements_file.exists():
                logger.error(f"❌ Requirements file not found: {requirements_file}")
                return False
            
            # Upgrade pip first
            subprocess.run([pip_cmd, "install", "--upgrade", "pip"], check=True)
            
            # Install requirements
            subprocess.run([pip_cmd, "install", "-r", str(requirements_file)], check=True)
            
            logger.info("✅ Dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            return False
    
    def create_project_structure(self):
        """Create necessary project directories"""
        logger.info("📁 Creating project structure...")
        
        directories = [
            self.backend_dir / "uploads",
            self.backend_dir / "logs",
            self.dataset_dir / "train" / "images",
            self.dataset_dir / "train" / "labels",
            self.dataset_dir / "valid" / "images",
            self.dataset_dir / "valid" / "labels",
            self.dataset_dir / "test" / "images",
            self.dataset_dir / "test" / "labels",
            self.project_root / "runs" / "detect" / "train" / "weights"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ Created directory: {directory}")
        
        return True
    
    def download_yolo_model(self):
        """Download YOLOv8 pretrained model"""
        logger.info("🤖 Downloading YOLOv8 model...")
        
        try:
            python_cmd = self.get_python_command()
            
            # Create a simple script to download the model
            download_script = '''
import os
from ultralytics import YOLO

# Download YOLOv8 nano model
model = YOLO('yolov8n.pt')
print("YOLOv8 model downloaded successfully!")
'''
            
            # Write and execute the script
            script_path = self.backend_dir / "download_model.py"
            with open(script_path, 'w') as f:
                f.write(download_script)
            
            subprocess.run([python_cmd, str(script_path)], check=True, cwd=self.backend_dir)
            
            # Clean up
            script_path.unlink()
            
            logger.info("✅ YOLOv8 model downloaded")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to download model: {e}")
            return False
    
    def test_database_connection(self):
        """Test MongoDB database connection"""
        logger.info("🗄️ Testing database connection...")
        
        try:
            python_cmd = self.get_python_command()
            
            # Create a simple database test script
            test_script = '''
import sys
sys.path.append('.')
from database import get_db

db = get_db()
if db.connected:
    print("✅ Database connection successful")
    # Populate with sample data
    db.populate_plants_database()
    print("✅ Sample data populated")
else:
    print("⚠️ Database connection failed - will use fallback mode")
'''
            
            script_path = self.backend_dir / "test_db.py"
            with open(script_path, 'w') as f:
                f.write(test_script)
            
            result = subprocess.run([python_cmd, str(script_path)], 
                                  capture_output=True, text=True, cwd=self.backend_dir)
            
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
            
            # Clean up
            script_path.unlink()
            
            logger.info("✅ Database test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Database test failed: {e}")
            return False
    
    def create_run_scripts(self):
        """Create convenient run scripts"""
        logger.info("📝 Creating run scripts...")
        
        # Windows batch script
        windows_script = f'''@echo off
echo Starting Medicinal Plant Recognition System...
cd /d "{self.backend_dir}"
"{self.get_python_command()}" app.py
pause
'''
        
        # Unix shell script
        unix_script = f'''#!/bin/bash
echo "Starting Medicinal Plant Recognition System..."
cd "{self.backend_dir}"
"{self.get_python_command()}" app.py
'''
        
        # Write scripts
        if platform.system() == "Windows":
            script_path = self.project_root / "run_server.bat"
            with open(script_path, 'w') as f:
                f.write(windows_script)
        else:
            script_path = self.project_root / "run_server.sh"
            with open(script_path, 'w') as f:
                f.write(unix_script)
            # Make executable
            os.chmod(script_path, 0o755)
        
        # Training script
        train_script = f'''#!/usr/bin/env python3
import subprocess
import sys

python_cmd = "{self.get_python_command()}"
train_script = "{self.project_root / 'train_model.py'}"

print("🚀 Starting model training...")
subprocess.run([python_cmd, train_script, "--create-sample"], cwd="{self.project_root}")
'''
        
        train_script_path = self.project_root / "train_model_easy.py"
        with open(train_script_path, 'w') as f:
            f.write(train_script)
        
        logger.info("✅ Run scripts created")
        return True
    
    def print_completion_message(self):
        """Print setup completion message"""
        print("\n" + "🎉" * 30)
        print("🎉 SETUP COMPLETED SUCCESSFULLY! 🎉")
        print("🎉" * 30)
        print()
        print("📋 Next Steps:")
        print("1. 📸 Add your plant images to dataset/train/images/")
        print("2. 🏷️ Add corresponding labels to dataset/train/labels/")
        print("3. 🚀 Train the model:")
        print(f"   python {self.project_root}/train_model.py --create-sample")
        print("4. 🌐 Start the server:")
        
        if platform.system() == "Windows":
            print(f"   {self.project_root}/run_server.bat")
        else:
            print(f"   {self.project_root}/run_server.sh")
        
        print("5. 🌍 Open your browser to: http://127.0.0.1:5000")
        print()
        print("📁 Project Structure:")
        print(f"   Backend: {self.backend_dir}")
        print(f"   Frontend: {self.frontend_dir}")
        print(f"   Dataset: {self.dataset_dir}")
        print(f"   Virtual Environment: {self.venv_dir}")
        print()
        print("🔧 Manual Start Commands:")
        print(f"   Activate venv: {self.get_activation_command()}")
        print(f"   Start server: python {self.backend_dir}/app.py")
        print()
        print("📚 Documentation:")
        print("   - Upload plant images through the web interface")
        print("   - View detection results with confidence scores")
        print("   - Download results as JSON files")
        print("   - Monitor system statistics")
        print()
    
    def get_activation_command(self):
        """Get virtual environment activation command"""
        if platform.system() == "Windows":
            return f"{self.venv_dir}\\Scripts\\activate.bat"
        else:
            return f"source {self.venv_dir}/bin/activate"
    
    def run_setup(self):
        """Run the complete setup process"""
        self.print_banner()
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Creating virtual environment", self.create_virtual_environment),
            ("Installing dependencies", self.install_dependencies),
            ("Creating project structure", self.create_project_structure),
            ("Downloading YOLOv8 model", self.download_yolo_model),
            ("Testing database connection", self.test_database_connection),
            ("Creating run scripts", self.create_run_scripts)
        ]
        
        for step_name, step_function in steps:
            logger.info(f"🔄 {step_name}...")
            
            if not step_function():
                logger.error(f"❌ Setup failed at: {step_name}")
                return False
        
        self.print_completion_message()
        return True

def main():
    """Main setup function"""
    setup = MedicinalPlantSetup()
    
    try:
        success = setup.run_setup()
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("\n⚠️ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
