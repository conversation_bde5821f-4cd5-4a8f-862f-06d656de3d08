<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PlantNet AI - World-Class Plant Identification{% endblock %}</title>
    
    <!-- Meta tags -->
    <meta name="description" content="{% block description %}Advanced AI-powered plant identification system inspired by PlantNet. Identify any plant species using state-of-the-art computer vision.{% endblock %}">
    <meta name="keywords" content="plant identification, AI, machine learning, botany, species recognition, PlantNet">
    <meta name="author" content="PlantNet AI">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{% block og_title %}PlantNet AI - World-Class Plant Identification{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Advanced AI-powered plant identification system{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:image" content="{{ url_for('static', filename='images/og-image.jpg') }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='apple-touch-icon.png') }}">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-leaf me-2"></i>PlantNet AI
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('identify_page') }}">
                            <i class="fas fa-camera me-1"></i>Identify
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search_page') }}">
                            <i class="fas fa-search me-1"></i>Search
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('about_page') }}">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('api_documentation') }}">
                            <i class="fas fa-code me-1"></i>API
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-leaf me-2"></i>PlantNet AI</h5>
                    <p class="text-muted">
                        Advanced AI-powered plant identification system inspired by PlantNet.
                        Identify plant species using state-of-the-art computer vision technology.
                    </p>
                </div>
                <div class="col-md-3">
                    <h6>Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('identify_page') }}" class="text-muted">Identify Plants</a></li>
                        <li><a href="{{ url_for('search_page') }}" class="text-muted">Search Species</a></li>
                        <li><a href="{{ url_for('api_documentation') }}" class="text-muted">API Documentation</a></li>
                        <li><a href="{{ url_for('about_page') }}" class="text-muted">About</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>Technology</h6>
                    <ul class="list-unstyled">
                        <li class="text-muted"><i class="fas fa-brain me-1"></i>EfficientNet CNN</li>
                        <li class="text-muted"><i class="fas fa-eye me-1"></i>Computer Vision</li>
                        <li class="text-muted"><i class="fas fa-database me-1"></i>MongoDB</li>
                        <li class="text-muted"><i class="fas fa-flask me-1"></i>Flask API</li>
                    </ul>
                </div>
            </div>
            <hr class="my-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 PlantNet AI. Inspired by the original PlantNet project.
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        Model-First Architecture | Version 1.0.0
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-content">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Processing your request...</p>
        </div>
    </div>
</body>
</html>
