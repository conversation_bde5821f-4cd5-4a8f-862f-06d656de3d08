"""
PlantNet AI Flask Application
World-class plant identification system inspired by PlantNet
"""

import os
import logging
from pathlib import Path
from flask import Flask, render_template, send_from_directory
from flask_cors import CORS

from config import Config
from api.routes import api_bp, init_api_components

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('plantnet_ai.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def create_app(config_class=Config):
    """
    Application factory pattern
    
    Args:
        config_class: Configuration class to use
        
    Returns:
        Configured Flask application
    """
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Enable CORS for API endpoints
    CORS(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # Create necessary directories
    create_directories(app)
    
    # Register blueprints
    app.register_blueprint(api_bp)
    
    # Initialize API components
    with app.app_context():
        init_api_components(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Register main routes
    register_main_routes(app)
    
    logger.info("✅ PlantNet AI application created successfully")
    
    return app

def create_directories(app):
    """Create necessary directories for the application"""
    directories = [
        app.config['UPLOAD_FOLDER'],
        app.config['MODEL_PATH'],
        'logs',
        'static/uploads',
        'static/thumbnails'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ Application directories created")

def register_error_handlers(app):
    """Register error handlers for the application"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"Internal server error: {error}")
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        return {
            'success': False,
            'error': 'File too large. Maximum size is 16MB per file.'
        }, 413
    
    @app.errorhandler(415)
    def unsupported_media_type(error):
        return {
            'success': False,
            'error': 'Unsupported file type. Please upload images in JPG, PNG, or WebP format.'
        }, 415

def register_main_routes(app):
    """Register main application routes"""
    
    @app.route('/')
    def index():
        """Main application page"""
        return render_template('index.html')
    
    @app.route('/identify')
    def identify_page():
        """Plant identification page"""
        return render_template('identify.html')
    
    @app.route('/species/<plant_id>')
    def species_detail(plant_id):
        """Species detail page"""
        return render_template('species.html', plant_id=plant_id)
    
    @app.route('/search')
    def search_page():
        """Plant search page"""
        return render_template('search.html')
    
    @app.route('/about')
    def about_page():
        """About page"""
        return render_template('about.html')
    
    @app.route('/api-docs')
    def api_documentation():
        """API documentation page"""
        return render_template('api_docs.html')
    
    @app.route('/favicon.ico')
    def favicon():
        """Serve favicon"""
        return send_from_directory(
            os.path.join(app.root_path, 'static'),
            'favicon.ico',
            mimetype='image/vnd.microsoft.icon'
        )
    
    @app.route('/robots.txt')
    def robots_txt():
        """Serve robots.txt"""
        return send_from_directory(
            os.path.join(app.root_path, 'static'),
            'robots.txt',
            mimetype='text/plain'
        )
    
    @app.route('/health')
    def health_check():
        """Simple health check endpoint"""
        return {
            'status': 'healthy',
            'message': 'PlantNet AI is running',
            'version': '1.0.0'
        }

# Create application instance
app = create_app()

if __name__ == '__main__':
    # Development server
    logger.info("🚀 Starting PlantNet AI development server...")
    
    # Check if model exists
    model_path = app.config['MODEL_PATH'] / app.config['MODEL_NAME']
    if not model_path.exists():
        logger.warning(f"⚠️ Model not found at {model_path}")
        logger.warning("⚠️ Please train a model first using the training scripts")
    
    # Check database connection
    try:
        from api.database import PlantDatabase
        db = PlantDatabase(app.config['MONGODB_URI'])
        if db.is_connected():
            logger.info("✅ Database connection verified")
            db.close_connection()
        else:
            logger.warning("⚠️ Database connection failed")
    except Exception as e:
        logger.warning(f"⚠️ Database check failed: {e}")
    
    # Run development server
    app.run(
        host=app.config.get('HOST', '127.0.0.1'),
        port=app.config.get('PORT', 5000),
        debug=app.config.get('DEBUG', True),
        threaded=True
    )
