"""
YOLOv8 Model Training Script for Medicinal Plant Recognition
This script trains a YOLOv8 model on the medicinal plant dataset
"""

import os
import sys
import yaml
import logging
from pathlib import Path
from datetime import datetime
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        from ultralytics import YOLO
        import torch
        logger.info("✅ All dependencies are installed")
        logger.info(f"PyTorch version: {torch.__version__}")
        logger.info(f"CUDA available: {torch.cuda.is_available()}")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.error("Please install required packages:")
        logger.error("pip install ultralytics torch torchvision")
        return False

def validate_dataset_structure(data_yaml_path):
    """Validate dataset structure and paths"""
    try:
        # Load data.yaml
        with open(data_yaml_path, 'r') as f:
            data_config = yaml.safe_load(f)
        
        logger.info("📁 Validating dataset structure...")
        
        # Check required keys
        required_keys = ['path', 'train', 'val', 'nc', 'names']
        for key in required_keys:
            if key not in data_config:
                logger.error(f"❌ Missing key '{key}' in data.yaml")
                return False
        
        # Get dataset root path
        dataset_root = Path(data_yaml_path).parent / data_config['path']
        
        # Check if paths exist
        train_path = dataset_root / data_config['train']
        val_path = dataset_root / data_config['val']
        
        logger.info(f"Dataset root: {dataset_root}")
        logger.info(f"Train path: {train_path}")
        logger.info(f"Validation path: {val_path}")
        
        # Create directories if they don't exist
        for path_name, path in [('train', train_path), ('val', val_path)]:
            if not path.exists():
                logger.warning(f"⚠️ {path_name} directory doesn't exist: {path}")
                path.mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ Created {path_name} directory")
        
        # Check for label directories
        train_labels = train_path.parent / 'labels'
        val_labels = val_path.parent / 'labels'
        
        for label_path in [train_labels, val_labels]:
            if not label_path.exists():
                label_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ Created labels directory: {label_path}")
        
        logger.info(f"📊 Dataset configuration:")
        logger.info(f"   Classes: {data_config['nc']}")
        logger.info(f"   Class names: {list(data_config['names'].values())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error validating dataset: {e}")
        return False

def create_sample_dataset(data_yaml_path):
    """Create sample dataset structure for demonstration"""
    try:
        logger.info("🔧 Creating sample dataset structure...")
        
        # Load data.yaml
        with open(data_yaml_path, 'r') as f:
            data_config = yaml.safe_load(f)
        
        dataset_root = Path(data_yaml_path).parent
        
        # Create directory structure
        directories = [
            'train/images',
            'train/labels',
            'valid/images',
            'valid/labels',
            'test/images',
            'test/labels'
        ]
        
        for dir_path in directories:
            full_path = dataset_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ Created directory: {full_path}")
        
        # Create sample annotation files
        sample_classes = list(data_config['names'].values())
        
        # Create a few sample label files
        for split in ['train', 'valid']:
            labels_dir = dataset_root / split / 'labels'
            
            for i, class_name in enumerate(sample_classes[:5]):  # First 5 classes
                label_file = labels_dir / f"sample_{class_name.lower()}_{i}.txt"
                
                # Sample YOLO format annotation (class_id x_center y_center width height)
                sample_annotation = f"{i} 0.5 0.5 0.3 0.4\n"
                
                with open(label_file, 'w') as f:
                    f.write(sample_annotation)
        
        logger.info("✅ Sample dataset structure created")
        logger.info("📝 Note: Add your actual images and annotations to train the model")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating sample dataset: {e}")
        return False

def train_model(data_yaml_path, epochs=50, batch_size=16, img_size=640, model_size='n'):
    """Train YOLOv8 model"""
    try:
        from ultralytics import YOLO
        
        logger.info("🚀 Starting YOLOv8 model training...")
        
        # Initialize model
        model_name = f'yolov8{model_size}.pt'
        model = YOLO(model_name)
        
        logger.info(f"📦 Using model: {model_name}")
        logger.info(f"📊 Training parameters:")
        logger.info(f"   Epochs: {epochs}")
        logger.info(f"   Batch size: {batch_size}")
        logger.info(f"   Image size: {img_size}")
        logger.info(f"   Data config: {data_yaml_path}")
        
        # Start training
        results = model.train(
            data=data_yaml_path,
            epochs=epochs,
            batch=batch_size,
            imgsz=img_size,
            patience=10,
            save_period=5,
            device='auto',  # Automatically select GPU if available
            workers=4,
            project='runs/detect',
            name='train',
            exist_ok=True
        )
        
        logger.info("✅ Training completed successfully!")
        
        # Get the path to the best model
        best_model_path = results.save_dir / 'weights' / 'best.pt'
        logger.info(f"🏆 Best model saved at: {best_model_path}")
        
        # Validate the model
        logger.info("🔍 Validating trained model...")
        validation_results = model.val()
        
        logger.info("📈 Validation Results:")
        logger.info(f"   mAP50: {validation_results.box.map50:.3f}")
        logger.info(f"   mAP50-95: {validation_results.box.map:.3f}")
        
        return str(best_model_path)
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        return None

def test_model(model_path, test_image_path=None):
    """Test the trained model"""
    try:
        from ultralytics import YOLO
        import cv2
        import numpy as np
        
        logger.info("🧪 Testing trained model...")
        
        # Load the trained model
        model = YOLO(model_path)
        
        if test_image_path and os.path.exists(test_image_path):
            # Test on specific image
            results = model(test_image_path)
            
            for result in results:
                logger.info(f"📸 Test image: {test_image_path}")
                
                if result.boxes is not None:
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        class_name = model.names[class_id]
                        
                        logger.info(f"   Detected: {class_name} (confidence: {confidence:.3f})")
                else:
                    logger.info("   No objects detected")
        else:
            # Create a dummy test image
            logger.info("📸 Creating dummy test image...")
            dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            
            results = model(dummy_image)
            logger.info("✅ Model inference test completed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Model testing failed: {e}")
        return False

def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Train YOLOv8 model for medicinal plant recognition')
    parser.add_argument('--data', type=str, default='dataset/data.yaml', help='Path to data.yaml file')
    parser.add_argument('--epochs', type=int, default=50, help='Number of training epochs')
    parser.add_argument('--batch', type=int, default=16, help='Batch size')
    parser.add_argument('--imgsz', type=int, default=640, help='Image size')
    parser.add_argument('--model', type=str, default='n', choices=['n', 's', 'm', 'l', 'x'], help='Model size')
    parser.add_argument('--create-sample', action='store_true', help='Create sample dataset structure')
    parser.add_argument('--test-image', type=str, help='Path to test image')
    
    args = parser.parse_args()
    
    print("🌿 Medicinal Plant Recognition - YOLOv8 Training")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Validate dataset
    data_yaml_path = args.data
    if not os.path.exists(data_yaml_path):
        logger.error(f"❌ Data config file not found: {data_yaml_path}")
        sys.exit(1)
    
    if not validate_dataset_structure(data_yaml_path):
        logger.error("❌ Dataset validation failed")
        sys.exit(1)
    
    # Create sample dataset if requested
    if args.create_sample:
        if not create_sample_dataset(data_yaml_path):
            logger.error("❌ Failed to create sample dataset")
            sys.exit(1)
        logger.info("✅ Sample dataset created. Add your images and labels to start training.")
        return
    
    # Start training
    start_time = datetime.now()
    logger.info(f"🕐 Training started at: {start_time}")
    
    best_model_path = train_model(
        data_yaml_path=data_yaml_path,
        epochs=args.epochs,
        batch_size=args.batch,
        img_size=args.imgsz,
        model_size=args.model
    )
    
    if best_model_path:
        end_time = datetime.now()
        training_duration = end_time - start_time
        
        logger.info(f"🕐 Training completed at: {end_time}")
        logger.info(f"⏱️ Total training time: {training_duration}")
        
        # Test the model
        test_model(best_model_path, args.test_image)
        
        logger.info("🎉 Training pipeline completed successfully!")
        logger.info(f"🏆 Best model: {best_model_path}")
        logger.info("📝 Next steps:")
        logger.info("   1. Copy the best model to backend/runs/detect/train/weights/best.pt")
        logger.info("   2. Start the Flask backend: python backend/app.py")
        logger.info("   3. Open the frontend in your browser")
        
    else:
        logger.error("❌ Training failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
