#!/usr/bin/env python3
"""
One-click dataset generation script
Runs the complete pipeline with optimal settings
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def print_banner():
    """Print startup banner"""
    print("🌿" * 60)
    print("🌿 ONE-CLICK MEDICINAL PLANT DATASET GENERATOR 🌿")
    print("🌿" * 60)
    print()
    print("This script will:")
    print("✅ Setup the environment")
    print("✅ Generate AI images for all plant classes")
    print("✅ Apply comprehensive augmentations")
    print("✅ Create final dataset with train/val/test splits")
    print("✅ Generate detailed statistics")
    print()

def check_requirements():
    """Check if required files exist"""
    required_files = [
        "data.yaml",
        "comprehensive_dataset_generator.py",
        "ai_dataset_generator.py",
        "advanced_augmentation.py",
        "requirements_dataset_generation.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ Missing required files: {missing_files}")
        return False
    
    logger.info("✅ All required files found")
    return True

def setup_environment():
    """Setup the environment"""
    logger.info("🔧 Setting up environment...")
    
    try:
        # Run setup script
        subprocess.run([sys.executable, "setup_dataset_generation.py"], check=True)
        logger.info("✅ Environment setup completed")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Environment setup failed: {e}")
        return False

def run_dataset_generation(ai_images=20, basic_augs=10, advanced_augs=15, create_final=True):
    """Run the comprehensive dataset generation"""
    logger.info("🚀 Starting dataset generation...")
    
    cmd = [
        sys.executable, 
        "comprehensive_dataset_generator.py",
        "--ai-images", str(ai_images),
        "--basic-augs", str(basic_augs),
        "--advanced-augs", str(advanced_augs)
    ]
    
    if create_final:
        cmd.append("--create-final")
    
    try:
        subprocess.run(cmd, check=True)
        logger.info("✅ Dataset generation completed")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Dataset generation failed: {e}")
        return False

def print_results():
    """Print results summary"""
    output_dir = Path("comprehensive_dataset")
    
    if output_dir.exists():
        print("\n🎉 Dataset generation completed successfully!")
        print(f"📁 Output directory: {output_dir.absolute()}")
        
        # Count generated files
        total_files = 0
        for subdir in output_dir.rglob("*.jpg"):
            total_files += 1
        
        print(f"📊 Total images generated: {total_files}")
        
        # Check for final dataset
        final_dataset = output_dir / "final_dataset"
        if final_dataset.exists():
            print(f"📦 Final dataset: {final_dataset}")
            
            # Count train/val/test
            train_count = len(list((final_dataset / "train" / "images").glob("*.jpg")))
            val_count = len(list((final_dataset / "valid" / "images").glob("*.jpg")))
            test_count = len(list((final_dataset / "test" / "images").glob("*.jpg")))
            
            print(f"📈 Dataset splits:")
            print(f"   Train: {train_count} images")
            print(f"   Validation: {val_count} images")
            print(f"   Test: {test_count} images")
        
        # Check for statistics
        stats_file = output_dir / "generation_results.json"
        if stats_file.exists():
            print(f"📊 Statistics: {stats_file}")
        
        print("\n📋 Next steps:")
        print("1. Review generated images in the output directory")
        print("2. Train your YOLOv8 model with the enhanced dataset:")
        print("   yolo detect train data=comprehensive_dataset/final_dataset/data.yaml model=yolov8n.pt epochs=100")
        print("3. Evaluate model performance improvements")
        
    else:
        print("❌ Output directory not found. Generation may have failed.")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='One-click dataset generation')
    parser.add_argument('--ai-images', type=int, default=20, help='AI images per class (default: 20)')
    parser.add_argument('--basic-augs', type=int, default=10, help='Basic augmentations per image (default: 10)')
    parser.add_argument('--advanced-augs', type=int, default=15, help='Advanced augmentations per image (default: 15)')
    parser.add_argument('--skip-setup', action='store_true', help='Skip environment setup')
    parser.add_argument('--no-final', action='store_true', help='Skip final dataset creation')
    
    args = parser.parse_args()
    
    print_banner()
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup environment (unless skipped)
    if not args.skip_setup:
        if not setup_environment():
            logger.warning("⚠️ Environment setup failed, but continuing...")
    
    # Run dataset generation
    success = run_dataset_generation(
        ai_images=args.ai_images,
        basic_augs=args.basic_augs,
        advanced_augs=args.advanced_augs,
        create_final=not args.no_final
    )
    
    if success:
        print_results()
        print("\n🎉 All done! Your enhanced dataset is ready for training.")
    else:
        print("\n❌ Dataset generation failed. Check the logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
