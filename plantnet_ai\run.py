#!/usr/bin/env python3
"""
PlantNet AI Application Runner
Quick start script for the PlantNet AI system
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Run PlantNet AI application')
    
    parser.add_argument('--host', type=str, default='127.0.0.1',
                       help='Host to bind to (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000,
                       help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--config', type=str, choices=['development', 'production', 'testing'],
                       default='development', help='Configuration to use')
    parser.add_argument('--train-sample', action='store_true',
                       help='Train a sample model before starting the app')
    parser.add_argument('--check-deps', action='store_true',
                       help='Check dependencies and exit')
    
    return parser.parse_args()

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'flask',
        'flask_cors',
        'tensorflow',
        'pillow',
        'opencv-python',
        'numpy',
        'pandas',
        'scikit-learn',
        'albumentations',
        'pymongo',
        'matplotlib',
        'seaborn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"❌ {package}")
    
    if missing_packages:
        logger.error(f"Missing packages: {', '.join(missing_packages)}")
        logger.error("Install missing packages with: pip install -r requirements.txt")
        return False
    
    logger.info("✅ All dependencies are installed")
    return True

def check_system_requirements():
    """Check system requirements and configuration"""
    logger.info("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 8):
        logger.error(f"❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        return False
    logger.info(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check model file
    model_path = Config.MODEL_PATH / Config.MODEL_NAME
    if model_path.exists():
        logger.info(f"✅ Model found: {model_path}")
    else:
        logger.warning(f"⚠️ Model not found: {model_path}")
        logger.warning("⚠️ You can train a sample model with --train-sample")
    
    # Check data directories
    for directory in [Config.UPLOAD_FOLDER, Config.MODEL_PATH]:
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Directory ready: {directory}")
    
    # Check MongoDB connection (optional)
    try:
        from api.database import PlantDatabase
        db = PlantDatabase(Config.MONGODB_URI)
        if db.is_connected():
            logger.info("✅ Database connection successful")
            db.close_connection()
        else:
            logger.warning("⚠️ Database connection failed (optional)")
    except Exception as e:
        logger.warning(f"⚠️ Database check failed: {e} (optional)")
    
    return True

def train_sample_model():
    """Train a small sample model for testing"""
    logger.info("🏋️ Training sample model...")
    
    try:
        from train_model import create_sample_data, main as train_main
        import tempfile
        
        # Create temporary data directory
        with tempfile.TemporaryDirectory() as temp_dir:
            sample_data_dir = Path(temp_dir) / 'sample_data'
            
            # Create sample data
            create_sample_data(sample_data_dir, num_species=3, images_per_organ=5)
            
            # Mock command line arguments for training
            sys.argv = [
                'train_model.py',
                '--data-dir', str(sample_data_dir),
                '--epochs', '2',
                '--fine-tune-epochs', '1',
                '--batch-size', '4'
            ]
            
            # Train model
            train_main()
            
        logger.info("✅ Sample model training completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Sample model training failed: {e}")
        return False

def main():
    """Main function"""
    args = parse_arguments()
    
    # ASCII Art Banner
    banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║    🌿 PlantNet AI - World-Class Plant Identification 🌿      ║
    ║                                                               ║
    ║    Model-First Architecture | EfficientNet CNN               ║
    ║    Inspired by the original PlantNet project                 ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    """
    print(banner)
    
    logger.info("🚀 Starting PlantNet AI application...")
    
    # Check dependencies if requested
    if args.check_deps:
        if check_dependencies():
            logger.info("✅ Dependency check passed")
            sys.exit(0)
        else:
            logger.error("❌ Dependency check failed")
            sys.exit(1)
    
    # Check system requirements
    if not check_system_requirements():
        logger.error("❌ System requirements check failed")
        sys.exit(1)
    
    # Train sample model if requested
    if args.train_sample:
        if not train_sample_model():
            logger.error("❌ Sample model training failed")
            sys.exit(1)
    
    # Set configuration
    config_map = {
        'development': Config,
        'production': Config,  # You can create ProductionConfig class
        'testing': Config      # You can create TestingConfig class
    }
    
    config_class = config_map.get(args.config, Config)
    
    # Create Flask application
    logger.info(f"🔧 Creating application with {args.config} configuration...")
    app = create_app(config_class)
    
    # Update configuration with command line arguments
    app.config['HOST'] = args.host
    app.config['PORT'] = args.port
    app.config['DEBUG'] = args.debug
    
    # Print startup information
    logger.info("📋 Application Configuration:")
    logger.info(f"   🌐 Host: {args.host}")
    logger.info(f"   🔌 Port: {args.port}")
    logger.info(f"   🐛 Debug: {args.debug}")
    logger.info(f"   ⚙️ Config: {args.config}")
    logger.info(f"   📁 Upload folder: {app.config['UPLOAD_FOLDER']}")
    logger.info(f"   🧠 Model path: {app.config['MODEL_PATH']}")
    
    # Print available endpoints
    logger.info("🛣️ Available endpoints:")
    logger.info("   🏠 Home: http://{}:{}".format(args.host, args.port))
    logger.info("   📷 Identify: http://{}:{}/identify".format(args.host, args.port))
    logger.info("   🔍 Search: http://{}:{}/search".format(args.host, args.port))
    logger.info("   📚 API Docs: http://{}:{}/api-docs".format(args.host, args.port))
    logger.info("   ❤️ Health: http://{}:{}/api/v1/health".format(args.host, args.port))
    
    try:
        # Start the application
        logger.info("🎯 Starting Flask development server...")
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
    except Exception as e:
        logger.error(f"❌ Application failed to start: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
