"""
Quick run script for Medicinal Plant Recognition System
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("🌿 Medicinal Plant Recognition System")
    print("=" * 60)
    print("🚀 Starting the application...")
    print()

def check_model():
    """Check if the trained model exists"""
    model_path = "best.pt"
    if os.path.exists(model_path):
        print("✅ Model found: best.pt")
        return True
    else:
        print("❌ Model not found: best.pt")
        print()
        print("🔧 You need to train the model first:")
        print("   python train_model.py")
        print()
        response = input("Do you want to train the model now? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            print("\n🏋️ Starting model training...")
            try:
                subprocess.run([sys.executable, "train_model.py"], check=True)
                print("✅ Model training completed!")
                return True
            except subprocess.CalledProcessError:
                print("❌ Model training failed!")
                return False
        else:
            print("⚠️  Cannot run without a trained model")
            return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        'streamlit',
        'ultralytics',
        'opencv-python',
        'torch',
        'PIL'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'opencv-python':
                import cv2
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("🔧 Please install dependencies first:")
        print("   pip install -r requirements.txt")
        print("   or run: python setup.py")
        return False
    
    print("✅ All dependencies available")
    return True

def start_streamlit():
    """Start the Streamlit application"""
    print("\n🌐 Starting Streamlit server...")
    print("📱 The application will open in your default browser")
    print("🔗 URL: http://localhost:8501")
    print()
    print("💡 Tips:")
    print("   - Upload clear, well-lit images of medicinal plants")
    print("   - The app supports JPG, JPEG, and PNG formats")
    print("   - Press Ctrl+C to stop the server")
    print()
    
    try:
        # Start Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.address", "localhost",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Streamlit: {e}")
        return False
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
        return True
    
    return True

def open_browser():
    """Open the application in the default browser"""
    url = "http://localhost:8501"
    print(f"🌐 Opening {url} in your browser...")
    
    try:
        webbrowser.open(url)
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print(f"   Please manually open: {url}")

def main():
    """Main run function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check model
    if not check_model():
        sys.exit(1)
    
    # Start the application
    print("🎯 All checks passed! Starting the application...")
    print()
    
    # Small delay to let user read the messages
    time.sleep(2)
    
    # Start Streamlit
    if not start_streamlit():
        sys.exit(1)

if __name__ == "__main__":
    main()
