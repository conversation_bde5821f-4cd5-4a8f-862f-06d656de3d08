"""
Setup script for Medicinal Plant Recognition System
"""

import os
import sys
import subprocess
import platform

def print_banner():
    """Print setup banner"""
    print("=" * 60)
    print("🌿 Medicinal Plant Recognition System - Setup")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_system_requirements():
    """Check system requirements"""
    print("\n💻 Checking system requirements...")
    
    # Check operating system
    os_name = platform.system()
    print(f"   Operating System: {os_name}")
    
    # Check available memory (basic check)
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"   Available RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  Warning: Less than 4GB RAM detected. Training may be slow.")
        else:
            print("✅ Sufficient RAM available")
    except ImportError:
        print("   RAM check skipped (psutil not available)")
    
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        print("   Upgrading pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        print("   Installing requirements from requirements.txt...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found!")
        return False

def check_cuda_availability():
    """Check if CUDA is available for GPU acceleration"""
    print("\n🚀 Checking GPU availability...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ CUDA available - {gpu_count} GPU(s) detected")
            print(f"   Primary GPU: {gpu_name}")
            return True
        else:
            print("⚠️  CUDA not available - will use CPU for training")
            print("   Note: GPU training is much faster but not required")
            return False
    except ImportError:
        print("   PyTorch not installed yet - GPU check will be done after installation")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = ["models", "temp", "uploads"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   Created: {directory}/")
        else:
            print(f"   Exists: {directory}/")
    
    print("✅ Directories ready")

def verify_dataset():
    """Verify dataset structure"""
    print("\n📊 Verifying dataset...")
    
    required_files = ["data.yaml"]
    required_dirs = ["train/images", "train/labels", "valid/images", "valid/labels"]
    
    all_good = True
    
    # Check files
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - Missing")
            all_good = False
    
    # Check directories
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            file_count = len(os.listdir(dir_path))
            print(f"   ✅ {dir_path} ({file_count} files)")
        else:
            print(f"   ❌ {dir_path} - Missing")
            all_good = False
    
    if all_good:
        print("✅ Dataset structure verified")
    else:
        print("⚠️  Some dataset files/directories are missing")
        print("   The system may not work properly without a complete dataset")
    
    return all_good

def run_quick_test():
    """Run a quick test to verify installation"""
    print("\n🧪 Running quick test...")
    
    try:
        # Test imports
        print("   Testing imports...")
        import streamlit
        import ultralytics
        import cv2
        import torch
        import PIL
        print("   ✅ All imports successful")
        
        # Test model loading (if model exists)
        if os.path.exists("best.pt"):
            print("   Testing model loading...")
            from ultralytics import YOLO
            model = YOLO("best.pt")
            print("   ✅ Model loaded successfully")
        else:
            print("   ⚠️  Model file 'best.pt' not found - you'll need to train first")
        
        print("✅ Quick test passed!")
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Test error: {e}")
        return False

def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Setup completed!")
    print("\n📋 Next Steps:")
    print("   1. If you haven't trained the model yet:")
    print("      python train_model.py")
    print()
    print("   2. To start the web application:")
    print("      streamlit run app.py")
    print()
    print("   3. Open your browser and go to:")
    print("      http://localhost:8501")
    print()
    print("💡 Tips:")
    print("   - Use high-quality, clear images for best results")
    print("   - The first run may take longer as models are loaded")
    print("   - Check the README.md for detailed usage instructions")
    print()
    print("🌿 Happy plant identification!")

def main():
    """Main setup function"""
    print_banner()
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    check_system_requirements()
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Check GPU availability
    check_cuda_availability()
    
    # Create directories
    create_directories()
    
    # Verify dataset
    verify_dataset()
    
    # Run quick test
    if not run_quick_test():
        print("\n⚠️  Setup completed but some tests failed")
        print("   You may encounter issues when running the application")
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
