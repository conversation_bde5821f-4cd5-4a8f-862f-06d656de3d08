// Medicinal Plant Recognition System - JavaScript

// Global variables
let currentImage = null;
let currentResult = null;
const API_BASE_URL = 'http://127.0.0.1:5000';

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const imageInput = document.getElementById('imageInput');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const detectBtn = document.getElementById('detectBtn');
const clearBtn = document.getElementById('clearBtn');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');
const errorSection = document.getElementById('errorSection');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadStats();
    checkSystemHealth();
});

function initializeEventListeners() {
    // File input change
    imageInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', () => imageInput.click());
    
    // Button events
    detectBtn.addEventListener('click', detectPlant);
    clearBtn.addEventListener('click', resetDetection);
    
    // Modal events
    window.addEventListener('click', handleModalClick);
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processSelectedFile(file);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processSelectedFile(files[0]);
    }
}

function processSelectedFile(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp'];
    if (!allowedTypes.includes(file.type)) {
        showError('Please select a valid image file (JPEG, PNG, WebP, or BMP)');
        return;
    }
    
    // Validate file size (16MB max)
    const maxSize = 16 * 1024 * 1024; // 16MB
    if (file.size > maxSize) {
        showError('File size too large. Please select an image smaller than 16MB.');
        return;
    }
    
    currentImage = file;
    
    // Display image preview
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        showImagePreview();
    };
    reader.readAsDataURL(file);
}

function showImagePreview() {
    uploadArea.style.display = 'none';
    imagePreview.style.display = 'block';
    hideAllSections();
}

function detectPlant() {
    if (!currentImage) {
        showError('Please select an image first');
        return;
    }
    
    showLoading();
    
    const formData = new FormData();
    formData.append('image', currentImage);
    
    fetch(`${API_BASE_URL}/detect`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            currentResult = data;
            showResults(data);
        } else {
            showError(data.message || 'Detection failed');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showError('Network error. Please check if the server is running.');
    });
}

function showResults(data) {
    hideAllSections();
    
    // Update result elements
    document.getElementById('plantName').textContent = data.plant_name;
    document.getElementById('scientificName').textContent = data.scientific_name;
    document.getElementById('localName').textContent = data.local_name;
    document.getElementById('medicinalUses').textContent = data.medicinal_uses;
    document.getElementById('description').textContent = data.description;
    
    // Update confidence badge
    const confidence = Math.round(data.confidence * 100);
    document.getElementById('confidence').textContent = confidence;
    
    const confidenceBadge = document.getElementById('confidenceBadge');
    if (confidence >= 90) {
        confidenceBadge.style.background = 'var(--success)';
    } else if (confidence >= 70) {
        confidenceBadge.style.background = 'var(--warning)';
    } else {
        confidenceBadge.style.background = 'var(--danger)';
    }
    
    resultsSection.style.display = 'block';
    
    // Refresh stats after successful detection
    setTimeout(loadStats, 1000);
}

function showError(message) {
    hideAllSections();
    document.getElementById('errorMessage').textContent = message;
    errorSection.style.display = 'block';
}

function showLoading() {
    hideAllSections();
    loadingSection.style.display = 'block';
}

function hideLoading() {
    loadingSection.style.display = 'none';
}

function hideAllSections() {
    loadingSection.style.display = 'none';
    resultsSection.style.display = 'none';
    errorSection.style.display = 'none';
}

function resetDetection() {
    currentImage = null;
    currentResult = null;
    
    // Reset UI
    uploadArea.style.display = 'block';
    imagePreview.style.display = 'none';
    hideAllSections();
    
    // Clear file input
    imageInput.value = '';
    previewImg.src = '';
}

function downloadResult() {
    if (!currentResult) {
        showError('No result to download');
        return;
    }
    
    const resultData = {
        timestamp: new Date().toISOString(),
        plant_name: currentResult.plant_name,
        scientific_name: currentResult.scientific_name,
        local_name: currentResult.local_name,
        confidence: currentResult.confidence,
        medicinal_uses: currentResult.medicinal_uses,
        description: currentResult.description
    };
    
    const dataStr = JSON.stringify(resultData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `plant_detection_${currentResult.plant_name}_${Date.now()}.json`;
    link.click();
}

function loadStats() {
    fetch(`${API_BASE_URL}/stats?days=7`)
        .then(response => response.json())
        .then(data => {
            updateStatsDisplay(data);
        })
        .catch(error => {
            console.error('Error loading stats:', error);
            updateStatsDisplay({
                total_predictions: 0,
                avg_confidence: 0,
                top_plants: [],
                status: 'offline'
            });
        });
}

function updateStatsDisplay(stats) {
    document.getElementById('totalPredictions').textContent = stats.total_predictions || 0;
    
    const avgConfidence = stats.avg_confidence || 0;
    document.getElementById('avgConfidence').textContent = `${Math.round(avgConfidence * 100)}%`;
    
    const topPlant = stats.top_plants && stats.top_plants.length > 0 
        ? stats.top_plants[0]._id 
        : '-';
    document.getElementById('topPlant').textContent = topPlant;
    
    // Update system status
    const statusElement = document.getElementById('systemStatus');
    const statusIcon = statusElement.querySelector('i');
    
    if (stats.status === 'connected' || stats.status === 'offline') {
        statusIcon.className = 'fas fa-circle status-online';
        statusIcon.style.color = 'var(--success)';
    } else {
        statusIcon.className = 'fas fa-circle status-offline';
        statusIcon.style.color = 'var(--danger)';
    }
}

function checkSystemHealth() {
    fetch(`${API_BASE_URL}/health`)
        .then(response => response.json())
        .then(data => {
            console.log('System health:', data);
        })
        .catch(error => {
            console.error('Health check failed:', error);
        });
}

function showAbout() {
    document.getElementById('aboutModal').style.display = 'block';
}

function showHelp() {
    document.getElementById('helpModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function handleModalClick(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Auto-refresh stats every 30 seconds
setInterval(loadStats, 30000);

// Add keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + U to upload
    if ((event.ctrlKey || event.metaKey) && event.key === 'u') {
        event.preventDefault();
        imageInput.click();
    }
    
    // Ctrl/Cmd + D to detect
    if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
        event.preventDefault();
        if (currentImage) {
            detectPlant();
        }
    }
    
    // Ctrl/Cmd + R to reset
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        resetDetection();
    }
    
    // Escape to close modals
    if (event.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }
});

// Add loading animation for better UX
function addLoadingAnimation() {
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled) {
                this.style.opacity = '0.7';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 200);
            }
        });
    });
}

// Initialize loading animations
addLoadingAnimation();

// Add error handling for network issues
window.addEventListener('online', function() {
    console.log('Network connection restored');
    checkSystemHealth();
});

window.addEventListener('offline', function() {
    console.log('Network connection lost');
    showError('Network connection lost. Please check your internet connection.');
});

// Performance monitoring
const performanceObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
            console.log(`Page load time: ${entry.loadEventEnd - entry.loadEventStart}ms`);
        }
    }
});

if ('PerformanceObserver' in window) {
    performanceObserver.observe({entryTypes: ['navigation']});
}
