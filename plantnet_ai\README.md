# 🌿 PlantNet AI - World-Class Plant Identification System

A state-of-the-art, model-first plant identification system inspired by PlantNet, featuring deep learning-based image classification with organ-specific recognition.

## 🎯 Project Philosophy

**Model-First Approach**: The entire system is designed around a sophisticated AI model that performs multi-class image classification. The UI and backend exist solely to serve the model with the highest quality data for accurate predictions.

## 🏗️ System Architecture

### Module 1: AI Core - Multi-Class Image Classification
- **EfficientNet-based CNN** for plant species identification
- **Organ-aware classification** (leaf, flower, fruit, bark, stem)
- **Probability distribution output** with confidence scores
- **Ranked species predictions** (top 5-10 matches)

### Module 2: Backend System (Flask)
- **RESTful API** with `/identify` endpoint
- **Image preprocessing** and model inference
- **Result fusion** from multiple images
- **Botanical database** integration (MongoDB)

### Module 3: Frontend Experience
- **3-Step Workflow**: Upload → Tag Organs → Identify
- **Intuitive UI** with drag-and-drop support
- **Professional results display** with confidence visualization
- **Detailed plant information** pages

## 🚀 Features

### Core Capabilities
- ✅ **Multi-organ recognition** (leaves, flowers, fruits, bark)
- ✅ **High-accuracy classification** using EfficientNet
- ✅ **Ranked probability output** with confidence scores
- ✅ **Comprehensive plant database** with medicinal information
- ✅ **Professional web interface** with 3-step workflow

### Advanced Features
- 🔄 **Community verification** system
- 📊 **Continuous learning** from user feedback
- 🌍 **Multi-language support** for plant names
- 📱 **Responsive design** for mobile devices
- 🔍 **Advanced search** and filtering

## 📁 Project Structure

```
plantnet_ai/
├── README.md                 # Project documentation
├── requirements.txt          # Python dependencies
├── config.py                # Configuration settings
├── app.py                   # Main Flask application
├── models/                  # AI model components
│   ├── __init__.py
│   ├── efficientnet_model.py    # EfficientNet implementation
│   ├── data_loader.py           # Dataset management
│   ├── trainer.py               # Model training pipeline
│   └── inference.py             # Model inference engine
├── api/                     # Backend API
│   ├── __init__.py
│   ├── routes.py               # API endpoints
│   ├── preprocessing.py        # Image preprocessing
│   └── database.py             # Database operations
├── frontend/                # Web interface
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── templates/
│       ├── index.html          # Main interface
│       ├── results.html        # Results display
│       └── plant_detail.html   # Plant information
├── data/                    # Training data
│   ├── raw/                    # Original images
│   ├── processed/              # Preprocessed data
│   └── annotations/            # Labels and metadata
├── database/                # Database schemas
│   ├── plant_schema.py         # Plant information schema
│   └── seed_data.py            # Initial plant data
└── scripts/                 # Utility scripts
    ├── setup_database.py      # Database initialization
    ├── train_model.py          # Model training script
    └── evaluate_model.py      # Model evaluation
```

## 🎯 Development Phases

### Phase 1: MVP (Current)
- ✅ Core AI model with EfficientNet
- ✅ 25-50 key medicinal plants
- ✅ 3-step frontend workflow
- ✅ Basic `/identify` API endpoint

### Phase 2: Scaling
- 🔄 500+ plant species dataset
- 🔄 Enhanced model accuracy
- 🔄 Comprehensive botanical database

### Phase 3: Community
- 🔄 User verification system
- 🔄 Continuous model improvement
- 🔄 Community data flywheel

## 🛠️ Quick Start

### 1. Setup Environment
```bash
cd plantnet_ai
pip install -r requirements.txt
```

### 2. Initialize Database
```bash
python scripts/setup_database.py
```

### 3. Train Model (Optional)
```bash
python scripts/train_model.py
```

### 4. Run Application
```bash
python app.py
```

### 5. Access Interface
Open http://localhost:5000 in your browser

## 📊 Model Performance

### Target Metrics
- **Top-1 Accuracy**: >85% for common species
- **Top-5 Accuracy**: >95% for all species
- **Inference Time**: <2 seconds per image
- **Confidence Calibration**: Well-calibrated probability scores

### Training Data Requirements
- **Minimum**: 200-500 images per species
- **Organs**: Leaf, flower, fruit, bark, stem
- **Quality**: High-resolution, verified labels
- **Diversity**: Multiple angles, lighting conditions

## 🌟 Key Differentiators

1. **Model-First Design**: Everything serves the AI model
2. **Organ-Specific Recognition**: Specialized for plant parts
3. **Professional UI**: PlantNet-inspired interface
4. **Comprehensive Database**: Rich medicinal plant information
5. **Community-Driven**: Continuous improvement through user feedback

## 🔬 Technical Specifications

### AI Model
- **Architecture**: EfficientNet-B2/B3
- **Input**: 224x224 RGB images + organ type
- **Output**: Probability distribution over species
- **Training**: Transfer learning from ImageNet

### Backend
- **Framework**: Flask with RESTful API
- **Database**: MongoDB for plant information
- **Image Processing**: PIL, OpenCV for preprocessing
- **Model Serving**: TensorFlow/PyTorch inference

### Frontend
- **Technology**: HTML5, CSS3, JavaScript
- **Framework**: Bootstrap for responsive design
- **Features**: Drag-drop, progress bars, confidence visualization
- **Mobile**: Responsive design for all devices

## 📈 Success Metrics

- **Accuracy**: >90% top-5 accuracy on test set
- **Speed**: <3 seconds end-to-end identification
- **User Experience**: <5 clicks from upload to result
- **Database**: 500+ species with complete information
- **Community**: Active user verification system

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- MongoDB (optional, for full functionality)
- 8GB+ RAM recommended
- GPU support recommended for training

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd plantnet_ai

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run the application
python run.py
```

### First Run

```bash
# Check dependencies
python run.py --check-deps

# Train a sample model (for testing)
python run.py --train-sample

# Start the application
python run.py --host 0.0.0.0 --port 5000 --debug
```

## 🎓 Training Your Own Model

### Data Preparation

Organize your training data in the following structure:

```
data/
├── species_1/
│   ├── leaf/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   ├── flower/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── fruit/
│       ├── image1.jpg
│       └── image2.jpg
└── species_2/
    ├── leaf/
    └── flower/
```

### Training Command

```bash
python train_model.py \
    --data-dir data/raw \
    --epochs 100 \
    --batch-size 32 \
    --learning-rate 0.001 \
    --fine-tune-epochs 50
```

### Training Parameters

- `--data-dir`: Path to training data directory
- `--epochs`: Number of initial training epochs (default: 100)
- `--batch-size`: Batch size for training (default: 32)
- `--learning-rate`: Initial learning rate (default: 0.001)
- `--fine-tune-epochs`: Number of fine-tuning epochs (default: 50)
- `--image-size`: Input image size (default: 224 224)

## 📡 API Documentation

### Authentication

Currently, the API is open and does not require authentication. In production, implement proper API key authentication.

### Endpoints

#### POST /api/v1/identify

Identify plant species from uploaded images.

**Request:**
- Content-Type: `multipart/form-data`
- `images`: List of image files
- `organs`: List of organ types (corresponding to images)
- `project`: Geographic scope (optional, default: 'world-flora')

**Response:**
```json
{
  "success": true,
  "data": {
    "predictions": [
      {
        "class_index": 0,
        "confidence": 0.95,
        "scientific_name": "Quercus robur",
        "common_name": "English Oak",
        "family": "Fagaceae"
      }
    ],
    "processing_time": 1.234,
    "organs_detected": ["leaf", "bark"]
  }
}
```

#### GET /api/v1/species/{plant_id}

Get detailed information about a specific plant species.

#### GET /api/v1/search?q={query}

Search plants by name or characteristics.

#### GET /api/v1/organs

Get list of supported plant organs.

#### GET /api/v1/health

Health check endpoint.

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/plantnet_ai

# Model Configuration
MODEL_PATH=models/trained_models
MODEL_NAME=plantnet_efficientnet.h5

# Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216  # 16MB

# API Configuration
API_RATE_LIMIT=100
```

### Model Configuration

The model configuration is defined in `config.py`:

```python
# Model Architecture
MODEL_ARCHITECTURE = 'EfficientNetB2'
INPUT_SHAPE = (224, 224, 3)
NUM_CLASSES = 1000  # Will be set based on training data
DROPOUT_RATE = 0.3

# Supported Plant Organs
SUPPORTED_ORGANS = [
    'leaf', 'flower', 'fruit',
    'bark', 'stem', 'whole_plant'
]

# Training Configuration
BATCH_SIZE = 32
LEARNING_RATE = 0.001
EPOCHS = 100
VALIDATION_SPLIT = 0.2
```

---

**Built with ❤️ for botanical research and education**
