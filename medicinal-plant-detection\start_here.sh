#!/bin/bash

# Medicinal Plant Recognition System - Quick Start Script

echo "========================================"
echo "  MEDICINAL PLANT RECOGNITION SYSTEM"
echo "========================================"
echo ""
echo "Starting the system..."
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "ERROR: Python is not installed"
        echo "Please install Python 3.8+ from https://python.org"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | awk '{print $2}')
echo "Using Python $PYTHON_VERSION"

# Run the quick start script
$PYTHON_CMD run_system.py

echo ""
echo "System stopped."
