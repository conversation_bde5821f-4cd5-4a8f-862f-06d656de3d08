"""
Setup script for dataset generation environment
Installs dependencies and prepares the environment
"""

import subprocess
import sys
import os
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_requirements():
    """Install required packages"""
    logger.info("📦 Installing required packages...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements_dataset_generation.txt'
        ])
        logger.info("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install requirements: {e}")
        return False

def check_dependencies():
    """Check if all dependencies are available"""
    logger.info("🔍 Checking dependencies...")
    
    required_packages = [
        'cv2', 'PIL', 'numpy', 'yaml', 'albumentations', 
        'tqdm', 'requests', 'pathlib'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package}")
        except ImportError:
            logger.warning(f"❌ {package}")
            missing.append(package)
    
    if missing:
        logger.warning(f"Missing packages: {missing}")
        return False
    
    logger.info("✅ All dependencies available")
    return True

def create_directories():
    """Create necessary directories"""
    logger.info("📁 Creating directories...")
    
    directories = [
        "generated_images",
        "augmented_images", 
        "comprehensive_dataset",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"✅ Created: {directory}")

def main():
    """Main setup function"""
    print("🌿" * 50)
    print("🌿 DATASET GENERATION SETUP")
    print("🌿" * 50)
    print()
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        logger.warning("Some dependencies are missing. Please install them manually.")
    
    # Create directories
    create_directories()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Run: python comprehensive_dataset_generator.py")
    print("2. Or run: python ai_dataset_generator.py")
    print("3. Check generated images in the output directories")
    print()

if __name__ == "__main__":
    main()
