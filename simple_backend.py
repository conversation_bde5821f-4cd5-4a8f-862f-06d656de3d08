#!/usr/bin/env python3
"""
Simple Flask Backend for Medicinal Plant Recognition
Minimal version for testing
"""

import os
import sys
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import json

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# HTML template for the frontend
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medicinal Plant Recognition System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        
        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 2rem;
            margin: 2rem 0;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }
        
        #fileInput {
            display: none;
        }
        
        .upload-text {
            color: #34495e;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .result {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .result.show {
            display: block;
        }
        
        .plant-info {
            text-align: left;
            margin-top: 1rem;
        }
        
        .plant-info h3 {
            color: #27ae60;
            margin-bottom: 0.5rem;
        }
        
        .confidence {
            background: #27ae60;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            display: inline-block;
            margin-top: 0.5rem;
        }
        
        .loading {
            display: none;
            color: #3498db;
            font-size: 1.1rem;
            margin-top: 1rem;
        }
        
        .loading.show {
            display: block;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin-top: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌿 Medicinal Plant Recognition</h1>
        <p class="subtitle">Upload an image to identify medicinal plants</p>
        
        <div class="status success">
            ✅ Backend is running successfully!
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div class="upload-text">
                📸 Click here or drag & drop an image
            </div>
            <p>Supported formats: JPG, PNG, WEBP</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>
        
        <button class="btn" onclick="analyzeImage()">🔍 Analyze Plant</button>
        <button class="btn" onclick="clearResults()">🗑️ Clear</button>
        
        <div class="loading" id="loading">
            🔄 Analyzing image...
        </div>
        
        <div class="result" id="result">
            <h3>🌱 Detection Results</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        
        // File input handling
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                displayImagePreview(file);
            }
        });
        
        // Drag and drop handling
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                displayImagePreview(files[0]);
            }
        });
        
        function displayImagePreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const uploadArea = document.querySelector('.upload-area');
                uploadArea.innerHTML = `
                    <img src="${e.target.result}" class="preview-image" alt="Preview">
                    <p style="margin-top: 1rem;">File: ${file.name}</p>
                `;
            };
            reader.readAsDataURL(file);
        }
        
        async function analyzeImage() {
            if (!selectedFile) {
                alert('Please select an image first!');
                return;
            }
            
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            loading.classList.add('show');
            result.classList.remove('show');
            
            const formData = new FormData();
            formData.append('image', selectedFile);
            
            try {
                const response = await fetch('/predict', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                loading.classList.remove('show');
                
                if (data.success) {
                    displayResults(data);
                } else {
                    displayError(data.error || 'Analysis failed');
                }
                
            } catch (error) {
                loading.classList.remove('show');
                displayError('Connection error: ' + error.message);
            }
        }
        
        function displayResults(data) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            if (data.detections && data.detections.length > 0) {
                const detection = data.detections[0];
                resultContent.innerHTML = `
                    <div class="plant-info">
                        <h3>🌿 ${detection.plant_name}</h3>
                        <p><strong>Scientific Name:</strong> ${detection.scientific_name}</p>
                        <p><strong>Local Name:</strong> ${detection.local_name}</p>
                        <p><strong>Uses:</strong> ${detection.medicinal_uses}</p>
                        <span class="confidence">Confidence: ${(detection.confidence * 100).toFixed(1)}%</span>
                    </div>
                `;
            } else {
                resultContent.innerHTML = `
                    <p>No medicinal plants detected in the image.</p>
                    <p>Please try with a clearer image of a medicinal plant.</p>
                `;
            }
            
            result.classList.add('show');
        }
        
        function displayError(message) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `
                <div class="status error">
                    ❌ Error: ${message}
                </div>
            `;
            
            result.classList.add('show');
        }
        
        function clearResults() {
            selectedFile = null;
            document.getElementById('result').classList.remove('show');
            document.getElementById('loading').classList.remove('show');
            
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = `
                <div class="upload-text">
                    📸 Click here or drag & drop an image
                </div>
                <p>Supported formats: JPG, PNG, WEBP</p>
                <input type="file" id="fileInput" accept="image/*">
            `;
            
            // Re-attach event listener
            document.getElementById('fileInput').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    selectedFile = file;
                    displayImagePreview(file);
                }
            });
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Serve the main page"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Medicinal Plant Recognition API is running',
        'version': '1.0.0'
    })

@app.route('/predict', methods=['POST'])
def predict():
    """Predict medicinal plant from uploaded image"""
    try:
        if 'image' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No image file provided'
            }), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # For demo purposes, return a simulated detection
        # In the real app, this would use the YOLO model
        demo_plants = [
            {
                'plant_name': 'Neem',
                'scientific_name': 'Azadirachta indica',
                'local_name': 'Nimba',
                'medicinal_uses': 'Antibacterial, antifungal, blood purifier, skin disorders',
                'confidence': 0.85
            },
            {
                'plant_name': 'Tulsi',
                'scientific_name': 'Ocimum tenuiflorum',
                'local_name': 'Holy Basil',
                'medicinal_uses': 'Respiratory disorders, stress relief, immunity booster',
                'confidence': 0.78
            },
            {
                'plant_name': 'Aloe Vera',
                'scientific_name': 'Aloe barbadensis',
                'local_name': 'Ghritkumari',
                'medicinal_uses': 'Skin healing, digestive health, anti-inflammatory',
                'confidence': 0.92
            }
        ]
        
        # Simulate detection result
        import random
        detected_plant = random.choice(demo_plants)
        
        return jsonify({
            'success': True,
            'detections': [detected_plant],
            'message': 'Plant detected successfully (Demo Mode)',
            'processing_time': 0.5
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Processing error: {str(e)}'
        }), 500

@app.route('/plants')
def get_plants():
    """Get list of all supported plants"""
    plants = [
        {'id': 1, 'name': 'Neem', 'scientific_name': 'Azadirachta indica'},
        {'id': 2, 'name': 'Tulsi', 'scientific_name': 'Ocimum tenuiflorum'},
        {'id': 3, 'name': 'Aloe Vera', 'scientific_name': 'Aloe barbadensis'},
        {'id': 4, 'name': 'Ashwagandha', 'scientific_name': 'Withania somnifera'},
        {'id': 5, 'name': 'Turmeric', 'scientific_name': 'Curcuma longa'}
    ]
    
    return jsonify({
        'success': True,
        'plants': plants,
        'total': len(plants)
    })

if __name__ == '__main__':
    print("🌿" * 50)
    print("🌿 MEDICINAL PLANT RECOGNITION SYSTEM")
    print("🌿 Simple Backend Server")
    print("🌿" * 50)
    print()
    print("✅ Starting Flask server...")
    print("🌐 Open your browser and go to: http://127.0.0.1:5000")
    print("📱 The web interface will load automatically")
    print("🔄 This is running in DEMO MODE with simulated detections")
    print()
    
    app.run(host='127.0.0.1', port=5000, debug=True)
