# Dataset Generation Requirements
# Core dependencies for AI dataset generation and augmentation

# Image processing and computer vision
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.11.0

# Advanced augmentation library
albumentations>=1.3.1

# Machine learning and AI
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.196

# Data handling and utilities
PyYAML>=6.0
pandas>=2.0.0
tqdm>=4.65.0

# Image generation (optional - for Stable Diffusion)
diffusers>=0.21.0
transformers>=4.33.0
accelerate>=0.21.0

# API requests for online AI services
requests>=2.31.0

# Parallel processing
joblib>=1.3.0

# Visualization and plotting
matplotlib>=3.7.0
seaborn>=0.12.0

# File handling and path management
pathlib2>=2.3.7

# Progress bars and logging
colorlog>=6.7.0

# Scientific computing
scikit-image>=0.21.0
scikit-learn>=1.3.0

# Optional: GPU acceleration
# torch-audio>=2.0.0  # Uncomment if needed
# torchtext>=0.15.0   # Uncomment if needed

# Optional: Advanced image generation
# stability-sdk>=0.8.0  # Uncomment for Stability AI API
# openai>=0.28.0        # Uncomment for OpenAI DALL-E API
