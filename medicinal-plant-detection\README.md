# 🌿 Medicinal Plant Recognition System

A complete AI-powered system for identifying medicinal plants using **YOLOv8**, **Flask backend**, and a modern **HTML/CSS/JS frontend** with **MongoDB** database integration.

## 🎯 Features

- **AI-Powered Detection**: Uses YOLOv8 for accurate plant identification
- **Web Interface**: Clean, responsive frontend for easy image upload
- **Real-time Results**: Get plant name, scientific name, local name, and confidence score
- **Database Integration**: MongoDB for storing plant information and analytics
- **Statistics Dashboard**: Track predictions and system performance
- **Download Results**: Export detection results as JSON files

## 🏗️ Project Structure

```
medicinal-plant-detection/
├── backend/                 # Flask API server
│   ├── app.py              # Main Flask application
│   ├── database.py         # MongoDB database manager
│   ├── requirements.txt    # Python dependencies
│   └── uploads/            # Uploaded images storage
├── frontend/               # Web interface
│   ├── index.html         # Main HTML page
│   ├── style.css          # Styling
│   └── script.js          # JavaScript functionality
├── dataset/               # Training dataset
│   ├── data.yaml         # YOLO dataset configuration
│   ├── train/            # Training images and labels
│   ├── valid/            # Validation images and labels
│   └── test/             # Test images and labels
├── .env                  # Environment configuration
├── setup.py             # Automated setup script
├── train_model.py       # Model training script
└── README.md           # This file
```

## 🚀 Quick Start

### 1. Automated Setup (Recommended)

```bash
# Clone or download the project
cd medicinal-plant-detection

# Run the automated setup
python setup.py
```

The setup script will:
- ✅ Create virtual environment
- ✅ Install all dependencies
- ✅ Download YOLOv8 model
- ✅ Set up project structure
- ✅ Test database connection
- ✅ Create run scripts

### 2. Manual Setup

If you prefer manual setup:

```bash
# Create virtual environment
cd backend
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create necessary directories
mkdir uploads logs
```

### 3. Environment Configuration

The `.env` file contains all configuration:

```env
# MongoDB Configuration
MONGODB_URI=mongodb+srv://medicinal-plant:<EMAIL>/
DATABASE_NAME=medicinal_plants_db

# Flask Configuration
FLASK_HOST=127.0.0.1
FLASK_PORT=5000

# Model Configuration
MODEL_PATH=runs/detect/train/weights/best.pt
CONFIDENCE_THRESHOLD=0.5
```

## 🤖 Model Training

### Prepare Dataset

1. **Add Images**: Place plant images in `dataset/train/images/`
2. **Add Labels**: Create YOLO format labels in `dataset/train/labels/`
3. **Configure Classes**: Update `dataset/data.yaml` with your plant classes

### Train the Model

```bash
# Create sample dataset structure
python train_model.py --create-sample

# Train with custom parameters
python train_model.py --epochs 100 --batch 16 --imgsz 640

# Train with different model sizes
python train_model.py --model s  # small model
python train_model.py --model m  # medium model
python train_model.py --model l  # large model
```

### Training Parameters

- `--epochs`: Number of training epochs (default: 50)
- `--batch`: Batch size (default: 16)
- `--imgsz`: Image size (default: 640)
- `--model`: Model size - n/s/m/l/x (default: n)

## 🌐 Running the System

### Start the Backend Server

```bash
# Using the run script (after setup)
# Windows:
run_server.bat
# Linux/Mac:
./run_server.sh

# Or manually:
cd backend
python app.py
```

### Access the Web Interface

Open your browser and go to: **http://127.0.0.1:5000**

## 📊 API Endpoints

### Detection Endpoint
```http
POST /detect
Content-Type: multipart/form-data

# Upload image file
```

**Response:**
```json
{
  "success": true,
  "plant_name": "Tulsi",
  "scientific_name": "Ocimum tenuiflorum",
  "local_name": "Holy Basil",
  "confidence": 0.97,
  "medicinal_uses": "Respiratory problems, immunity booster, stress relief",
  "description": "Sacred herb with adaptogenic properties"
}
```

### Other Endpoints
- `GET /health` - System health check
- `GET /stats` - Prediction statistics
- `GET /plants` - List all available plants
- `GET /plant/<name>` - Get specific plant details

## 🗄️ Database Schema

### Plants Collection
```json
{
  "plant_name": "Tulsi",
  "scientific_name": "Ocimum tenuiflorum",
  "local_name": "Holy Basil",
  "medicinal_uses": "Respiratory problems, immunity booster",
  "description": "Sacred herb with adaptogenic properties",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Predictions Collection
```json
{
  "predicted_plant": "Tulsi",
  "confidence": 0.97,
  "scientific_name": "Ocimum tenuiflorum",
  "local_name": "Holy Basil",
  "timestamp": "2024-01-01T00:00:00Z",
  "image_size": "640x640"
}
```

## 🎨 Frontend Features

- **Drag & Drop Upload**: Easy image upload interface
- **Real-time Preview**: See uploaded images before detection
- **Loading Animations**: Visual feedback during processing
- **Responsive Design**: Works on desktop and mobile devices
- **Statistics Dashboard**: View system performance metrics
- **Download Results**: Export detection results
- **Keyboard Shortcuts**: 
  - `Ctrl+U`: Upload image
  - `Ctrl+D`: Detect plant
  - `Ctrl+R`: Reset
  - `Esc`: Close modals

## 🔧 Configuration

### Model Configuration
- **Confidence Threshold**: Minimum confidence for detections (0.5)
- **Image Size**: Input image size for model (640x640)
- **Model Path**: Location of trained model weights

### Upload Configuration
- **Max File Size**: 16MB
- **Allowed Formats**: JPG, JPEG, PNG, WebP, BMP
- **Upload Directory**: `backend/uploads/`

### Database Configuration
- **MongoDB URI**: Connection string for MongoDB Atlas
- **Database Name**: `medicinal_plants_db`
- **Collections**: `plants`, `predictions`

## 📱 Supported Plant Classes

The system currently supports 20 medicinal plants:

1. **Neem** - *Azadirachta indica*
2. **Tulsi** - *Ocimum tenuiflorum*
3. **Aloe Vera** - *Aloe barbadensis*
4. **Ashwagandha** - *Withania somnifera*
5. **Peppermint** - *Mentha piperita*
6. **Turmeric** - *Curcuma longa*
7. **Ginger** - *Zingiber officinale*
8. **Basil** - *Ocimum basilicum*
9. **Lavender** - *Lavandula angustifolia*
10. **Rosemary** - *Rosmarinus officinalis*
11. **Thyme** - *Thymus vulgaris*
12. **Sage** - *Salvia officinalis*
13. **Chamomile** - *Matricaria chamomilla*
14. **Echinacea** - *Echinacea purpurea*
15. **Ginkgo** - *Ginkgo biloba*
16. **Ginseng** - *Panax ginseng*
17. **Calendula** - *Calendula officinalis*
18. **Dandelion** - *Taraxacum officinale*
19. **Nettle** - *Urtica dioica*
20. **Elderberry** - *Sambucus canadensis*

## 🛠️ Development

### Adding New Plants

1. **Update Dataset**: Add images and labels for new plants
2. **Update Configuration**: Modify `dataset/data.yaml`
3. **Update Database**: Add plant information to MongoDB
4. **Retrain Model**: Run training with updated dataset

### Customizing the Interface

- **Styling**: Modify `frontend/style.css`
- **Functionality**: Update `frontend/script.js`
- **Layout**: Edit `frontend/index.html`

### API Extensions

- **New Endpoints**: Add routes in `backend/app.py`
- **Database Operations**: Extend `backend/database.py`
- **Model Features**: Enhance detection logic

## 🔍 Troubleshooting

### Common Issues

1. **Model Not Found**
   ```bash
   # Download YOLOv8 model
   python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"
   ```

2. **Database Connection Failed**
   - Check MongoDB URI in `.env`
   - Verify network connectivity
   - System will use fallback mode if database unavailable

3. **Import Errors**
   ```bash
   # Reinstall dependencies
   pip install -r backend/requirements.txt
   ```

4. **Port Already in Use**
   - Change `FLASK_PORT` in `.env`
   - Or kill existing process

### Performance Optimization

- **GPU Acceleration**: Install CUDA for faster inference
- **Model Size**: Use larger models (s/m/l/x) for better accuracy
- **Batch Processing**: Process multiple images simultaneously
- **Caching**: Enable result caching for repeated queries

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the API documentation

## ⚠️ Disclaimer

This system is for educational and research purposes only. Always consult qualified healthcare professionals before using any medicinal plants for treatment. The AI predictions should not be used as the sole basis for plant identification in critical applications.

---

**Made with 💚 for preserving traditional medicinal plant knowledge through modern AI technology**
