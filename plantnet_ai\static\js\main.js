/**
 * PlantNet AI - Main JavaScript
 * Handles UI interactions and API communication
 */

// Global variables
let uploadedImages = [];
let selectedOrgans = {};

// API endpoints
const API_BASE = '/api/v1';
const ENDPOINTS = {
    identify: `${API_BASE}/identify`,
    species: `${API_BASE}/species`,
    search: `${API_BASE}/search`,
    organs: `${API_BASE}/organs`,
    health: `${API_BASE}/health`
};

// Utility functions
const Utils = {
    /**
     * Show loading overlay
     */
    showLoading: function(message = 'Processing your request...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.querySelector('p').textContent = message;
            overlay.classList.remove('d-none');
        }
    },

    /**
     * Hide loading overlay
     */
    hideLoading: function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('d-none');
        }
    },

    /**
     * Show toast notification
     */
    showToast: function(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        // Add to toast container
        let container = document.getElementById('toastContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        container.appendChild(toast);

        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove from DOM after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    },

    /**
     * Format file size
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Validate image file
     */
    validateImageFile: function(file) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const maxSize = 16 * 1024 * 1024; // 16MB

        if (!allowedTypes.includes(file.type)) {
            throw new Error('Invalid file type. Please upload JPG, PNG, GIF, or WebP images.');
        }

        if (file.size > maxSize) {
            throw new Error(`File too large. Maximum size is ${Utils.formatFileSize(maxSize)}.`);
        }

        return true;
    },

    /**
     * Create image preview
     */
    createImagePreview: function(file, index) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('div');
                preview.className = 'image-preview';
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="Preview ${index + 1}" class="img-thumbnail">
                    <button type="button" class="remove-btn" onclick="removeImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="mt-2">
                        <small class="text-muted">${file.name}</small><br>
                        <small class="text-muted">${Utils.formatFileSize(file.size)}</small>
                    </div>
                `;
                resolve(preview);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    },

    /**
     * Debounce function
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// File upload handling
const FileUpload = {
    /**
     * Initialize file upload functionality
     */
    init: function() {
        const uploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewContainer = document.getElementById('imagePreview');

        if (!uploadArea || !fileInput) return;

        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());

        // File input change
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });
    },

    /**
     * Handle uploaded files
     */
    handleFiles: function(files) {
        const fileArray = Array.from(files);
        
        fileArray.forEach((file, index) => {
            try {
                Utils.validateImageFile(file);
                uploadedImages.push(file);
                this.addImagePreview(file, uploadedImages.length - 1);
            } catch (error) {
                Utils.showToast(error.message, 'danger');
            }
        });

        this.updateUploadArea();
    },

    /**
     * Add image preview
     */
    addImagePreview: async function(file, index) {
        const previewContainer = document.getElementById('imagePreview');
        if (!previewContainer) return;

        try {
            const preview = await Utils.createImagePreview(file, index);
            previewContainer.appendChild(preview);
        } catch (error) {
            Utils.showToast('Failed to create image preview', 'danger');
        }
    },

    /**
     * Update upload area visibility
     */
    updateUploadArea: function() {
        const uploadArea = document.getElementById('fileUploadArea');
        const previewContainer = document.getElementById('imagePreview');
        
        if (uploadedImages.length > 0) {
            uploadArea.style.display = 'none';
            previewContainer.style.display = 'block';
        } else {
            uploadArea.style.display = 'block';
            previewContainer.style.display = 'none';
        }

        // Update organ selection
        OrganSelection.updateOrganSelection();
    }
};

// Organ selection handling
const OrganSelection = {
    /**
     * Initialize organ selection
     */
    init: function() {
        this.loadSupportedOrgans();
    },

    /**
     * Load supported organs from API
     */
    loadSupportedOrgans: async function() {
        try {
            const response = await fetch(ENDPOINTS.organs);
            const data = await response.json();
            
            if (data.success) {
                this.supportedOrgans = data.data.organs;
                this.organDescriptions = data.data.descriptions;
            }
        } catch (error) {
            console.error('Failed to load supported organs:', error);
            // Fallback organs
            this.supportedOrgans = ['leaf', 'flower', 'fruit', 'bark', 'stem', 'whole_plant'];
            this.organDescriptions = {
                'leaf': 'Leaf or leaves of the plant',
                'flower': 'Flower or inflorescence',
                'fruit': 'Fruit or seed structure',
                'bark': 'Bark or stem surface',
                'stem': 'Stem or branch',
                'whole_plant': 'Entire plant or habit'
            };
        }
    },

    /**
     * Update organ selection UI
     */
    updateOrganSelection: function() {
        const container = document.getElementById('organSelection');
        if (!container || uploadedImages.length === 0) return;

        container.innerHTML = '<h5>Tag Plant Organs</h5>';

        uploadedImages.forEach((file, index) => {
            const organDiv = document.createElement('div');
            organDiv.className = 'mb-3 p-3 border rounded';
            organDiv.innerHTML = `
                <div class="d-flex align-items-center mb-2">
                    <strong>Image ${index + 1}:</strong>
                    <span class="ms-2 text-muted">${file.name}</span>
                </div>
                <div class="organ-selector" data-image-index="${index}">
                    ${this.supportedOrgans.map(organ => `
                        <div class="organ-option" data-organ="${organ}" onclick="selectOrgan(${index}, '${organ}')">
                            <i class="fas fa-${this.getOrganIcon(organ)} me-1"></i>
                            ${organ.replace('_', ' ').toUpperCase()}
                        </div>
                    `).join('')}
                </div>
            `;
            container.appendChild(organDiv);
        });

        container.style.display = 'block';
    },

    /**
     * Get icon for organ type
     */
    getOrganIcon: function(organ) {
        const icons = {
            'leaf': 'leaf',
            'flower': 'flower',
            'fruit': 'apple-alt',
            'bark': 'tree',
            'stem': 'seedling',
            'whole_plant': 'spa'
        };
        return icons[organ] || 'leaf';
    }
};

// Plant identification
const PlantIdentification = {
    /**
     * Submit identification request
     */
    submit: async function() {
        if (uploadedImages.length === 0) {
            Utils.showToast('Please upload at least one image', 'warning');
            return;
        }

        // Check if all images have organ tags
        const missingOrgans = uploadedImages.filter((_, index) => !selectedOrgans[index]);
        if (missingOrgans.length > 0) {
            Utils.showToast('Please tag all images with plant organs', 'warning');
            return;
        }

        Utils.showLoading('Analyzing plant images...');

        try {
            const formData = new FormData();
            
            // Add images
            uploadedImages.forEach((file, index) => {
                formData.append('images', file);
            });

            // Add organ tags
            Object.values(selectedOrgans).forEach(organ => {
                formData.append('organs', organ);
            });

            // Add project (optional)
            formData.append('project', 'world-flora');

            const response = await fetch(ENDPOINTS.identify, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data.data);
                Utils.showToast('Plant identification completed!', 'success');
            } else {
                throw new Error(data.error || 'Identification failed');
            }

        } catch (error) {
            console.error('Identification error:', error);
            Utils.showToast(error.message || 'Failed to identify plant', 'danger');
        } finally {
            Utils.hideLoading();
        }
    },

    /**
     * Display identification results
     */
    displayResults: function(data) {
        const container = document.getElementById('resultsContainer');
        if (!container) return;

        const predictions = data.predictions || [];
        
        if (predictions.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No plant species could be identified from the uploaded images.
                </div>
            `;
            return;
        }

        let html = `
            <h4 class="mb-4">
                <i class="fas fa-search me-2"></i>Identification Results
                <small class="text-muted ms-2">(${data.processing_time}s)</small>
            </h4>
        `;

        predictions.forEach((pred, index) => {
            const confidence = Math.round(pred.confidence * 100);
            const confidenceClass = confidence >= 80 ? 'success' : confidence >= 60 ? 'warning' : 'danger';
            
            html += `
                <div class="card result-card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <div class="badge bg-${confidenceClass} fs-6">#${index + 1}</div>
                                ${pred.reference_image ? `
                                    <img src="${pred.reference_image}" alt="${pred.scientific_name}" class="species-image mt-2">
                                ` : ''}
                            </div>
                            <div class="col-md-7">
                                <h5 class="mb-1">${pred.scientific_name}</h5>
                                ${pred.common_name ? `<p class="text-muted mb-1">${pred.common_name}</p>` : ''}
                                ${pred.family ? `<small class="text-muted">Family: ${pred.family}</small>` : ''}
                            </div>
                            <div class="col-md-3">
                                <div class="text-end">
                                    <div class="fw-bold text-${confidenceClass}">${confidence}%</div>
                                    <div class="confidence-bar mt-1">
                                        <div class="confidence-fill" style="width: ${confidence}%"></div>
                                    </div>
                                </div>
                                ${pred.plant_id ? `
                                    <a href="/species/${pred.plant_id}" class="btn btn-sm btn-outline-success mt-2">
                                        View Details
                                    </a>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
        container.style.display = 'block';

        // Scroll to results
        container.scrollIntoView({ behavior: 'smooth' });
    }
};

// Global functions (called from HTML)
function removeImage(index) {
    uploadedImages.splice(index, 1);
    delete selectedOrgans[index];
    
    // Reindex selected organs
    const newSelectedOrgans = {};
    Object.keys(selectedOrgans).forEach(key => {
        const newKey = parseInt(key) > index ? parseInt(key) - 1 : parseInt(key);
        newSelectedOrgans[newKey] = selectedOrgans[key];
    });
    selectedOrgans = newSelectedOrgans;

    // Update UI
    document.getElementById('imagePreview').innerHTML = '';
    uploadedImages.forEach((file, i) => {
        FileUpload.addImagePreview(file, i);
    });
    
    FileUpload.updateUploadArea();
}

function selectOrgan(imageIndex, organ) {
    selectedOrgans[imageIndex] = organ;
    
    // Update UI
    const selector = document.querySelector(`[data-image-index="${imageIndex}"]`);
    if (selector) {
        selector.querySelectorAll('.organ-option').forEach(option => {
            option.classList.remove('selected');
        });
        selector.querySelector(`[data-organ="${organ}"]`).classList.add('selected');
    }
}

function submitIdentification() {
    PlantIdentification.submit();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize components
    FileUpload.init();
    OrganSelection.init();
    
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Export for use in other scripts
window.PlantNetAI = {
    Utils,
    FileUpload,
    OrganSelection,
    PlantIdentification
};
