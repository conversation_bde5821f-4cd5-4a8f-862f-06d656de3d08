{% extends "base.html" %}

{% block title %}Plant Identification - PlantNet AI{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-5 fw-bold mb-3">
                <i class="fas fa-camera text-success me-3"></i>
                Identify Your Plant
            </h1>
            <p class="lead text-muted">
                Upload photos of different plant parts and let our AI identify the species
            </p>
        </div>
    </div>

    <!-- Step 1: Upload Images -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        Step 1: Upload Plant Images
                    </h4>
                </div>
                <div class="card-body">
                    <!-- File Upload Area -->
                    <div id="fileUploadArea" class="file-upload-area">
                        <div class="file-upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h5>Drag & Drop Images Here</h5>
                        <p class="text-muted mb-3">
                            or click to browse files
                        </p>
                        <div class="mb-3">
                            <span class="badge bg-info me-2">JPG</span>
                            <span class="badge bg-info me-2">PNG</span>
                            <span class="badge bg-info me-2">WebP</span>
                            <span class="badge bg-info">Max 16MB each</span>
                        </div>
                        <small class="text-muted">
                            For best results, upload clear photos of different plant parts
                        </small>
                    </div>

                    <!-- Hidden File Input -->
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">

                    <!-- Image Preview Container -->
                    <div id="imagePreview" class="mt-4" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">Uploaded Images</h5>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-plus me-1"></i>Add More
                            </button>
                        </div>
                        <div class="row" id="imagePreviewGrid">
                            <!-- Image previews will be added here -->
                        </div>
                    </div>

                    <!-- Upload Tips -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>Photography Tips</h6>
                                <ul class="mb-0 small">
                                    <li>Take photos in good lighting</li>
                                    <li>Focus on one plant part per image</li>
                                    <li>Avoid blurry or dark images</li>
                                    <li>Include multiple angles if possible</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-leaf me-2"></i>Best Plant Parts</h6>
                                <ul class="mb-0 small">
                                    <li><strong>Leaves:</strong> Clear view of shape and veins</li>
                                    <li><strong>Flowers:</strong> Full bloom, front view</li>
                                    <li><strong>Fruits:</strong> Mature fruits or seeds</li>
                                    <li><strong>Bark:</strong> Close-up texture</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: Tag Organs -->
    <div class="row mb-5">
        <div class="col-12">
            <div id="organSelection" class="card shadow-sm" style="display: none;">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-tags me-2"></i>
                        Step 2: Tag Plant Organs
                    </h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Select which plant part each image shows. This helps our AI make more accurate predictions.
                    </p>
                    <!-- Organ selection will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Step 3: Identify -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <button id="identifyBtn" class="btn btn-success btn-lg px-5" onclick="submitIdentification()" disabled>
                <i class="fas fa-search me-2"></i>
                Identify Plant Species
            </button>
            <p class="text-muted mt-2 small">
                Our AI will analyze your images and provide species predictions
            </p>
        </div>
    </div>

    <!-- Results Section -->
    <div class="row">
        <div class="col-12">
            <div id="resultsContainer" style="display: none;">
                <!-- Results will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Example Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-images me-2"></i>
                        Example: Good vs Poor Images
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">✅ Good Images</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="example-image good">
                                        <img src="{{ url_for('static', filename='images/examples/good-leaf.jpg') }}" 
                                             alt="Good leaf example" class="img-fluid rounded">
                                        <small class="text-success">Clear leaf detail</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="example-image good">
                                        <img src="{{ url_for('static', filename='images/examples/good-flower.jpg') }}" 
                                             alt="Good flower example" class="img-fluid rounded">
                                        <small class="text-success">Full flower view</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-danger">❌ Poor Images</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="example-image poor">
                                        <img src="{{ url_for('static', filename='images/examples/poor-blurry.jpg') }}" 
                                             alt="Poor blurry example" class="img-fluid rounded">
                                        <small class="text-danger">Too blurry</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="example-image poor">
                                        <img src="{{ url_for('static', filename='images/examples/poor-dark.jpg') }}" 
                                             alt="Poor dark example" class="img-fluid rounded">
                                        <small class="text-danger">Too dark</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.example-image {
    text-align: center;
    margin-bottom: 1rem;
}

.example-image img {
    height: 120px;
    width: 100%;
    object-fit: cover;
    border: 2px solid transparent;
}

.example-image.good img {
    border-color: var(--success-color);
}

.example-image.poor img {
    border-color: var(--danger-color);
    filter: blur(1px) brightness(0.7);
}

.example-image small {
    display: block;
    margin-top: 0.5rem;
    font-weight: 600;
}

#identifyBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.organ-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.organ-option {
    padding: 0.75rem 1rem;
    border: 2px solid #ced4da;
    border-radius: var(--border-radius);
    background-color: white;
    cursor: pointer;
    transition: var(--transition-base);
    font-weight: 500;
    text-align: center;
    min-width: 120px;
}

.organ-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(40, 167, 69, 0.05);
    transform: translateY(-2px);
}

.organ-option.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.image-preview {
    position: relative;
    margin-bottom: 1rem;
}

.image-preview img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.image-preview .remove-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition-base);
    box-shadow: var(--box-shadow);
}

.image-preview .remove-btn:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

.image-preview .image-info {
    position: absolute;
    bottom: 8px;
    left: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .organ-option {
        min-width: 100px;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .example-image img {
        height: 100px;
    }
    
    .image-preview img {
        height: 150px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Enable identify button when images and organs are selected
function updateIdentifyButton() {
    const btn = document.getElementById('identifyBtn');
    const hasImages = uploadedImages.length > 0;
    const hasAllOrgans = uploadedImages.every((_, index) => selectedOrgans[index]);
    
    btn.disabled = !(hasImages && hasAllOrgans);
    
    if (hasImages && hasAllOrgans) {
        btn.classList.remove('btn-secondary');
        btn.classList.add('btn-success');
    } else {
        btn.classList.remove('btn-success');
        btn.classList.add('btn-secondary');
    }
}

// Override the global selectOrgan function to update button
const originalSelectOrgan = window.selectOrgan;
window.selectOrgan = function(imageIndex, organ) {
    originalSelectOrgan(imageIndex, organ);
    updateIdentifyButton();
};

// Override the global removeImage function to update button
const originalRemoveImage = window.removeImage;
window.removeImage = function(index) {
    originalRemoveImage(index);
    updateIdentifyButton();
};

// Update button when images are added
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    fileInput.addEventListener('change', function() {
        setTimeout(updateIdentifyButton, 100);
    });
});
</script>
{% endblock %}
