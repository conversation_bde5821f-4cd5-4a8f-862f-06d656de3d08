/* Medicinal Plant Recognition System - Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-green: #2d5a27;
    --secondary-green: #4a7c59;
    --light-green: #8fbc8f;
    --accent-green: #90ee90;
    --dark-green: #1a3d1a;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #343a40;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--light-green) 0%, var(--secondary-green) 100%);
    min-height: 100vh;
    color: var(--dark-gray);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
}

.header-content h1 {
    color: var(--primary-green);
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.header-content p {
    color: var(--gray);
    font-size: 1.2rem;
}

.header-content i {
    margin-right: 10px;
}

/* Upload Section */
.upload-section {
    margin-bottom: 30px;
}

.upload-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.upload-area {
    border: 3px dashed var(--light-green);
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--secondary-green);
    background-color: rgba(143, 188, 143, 0.1);
}

.upload-area.dragover {
    border-color: var(--primary-green);
    background-color: rgba(143, 188, 143, 0.2);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.upload-icon {
    font-size: 3rem;
    color: var(--secondary-green);
}

.upload-content h3 {
    color: var(--primary-green);
    font-size: 1.5rem;
    margin: 0;
}

.upload-content p {
    color: var(--gray);
    margin: 0;
}

.browse-btn {
    background: var(--secondary-green);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.browse-btn:hover {
    background: var(--primary-green);
    transform: translateY(-2px);
}

/* Image Preview */
.image-preview {
    text-align: center;
    margin-top: 20px;
}

.image-preview img {
    max-width: 100%;
    max-height: 400px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
}

.image-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.detect-btn {
    background: var(--success);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.detect-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

.clear-btn {
    background: var(--gray);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.clear-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Loading Section */
.loading-section {
    margin-bottom: 30px;
}

.loading-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    box-shadow: var(--box-shadow);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--light-gray);
    border-top: 5px solid var(--secondary-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-card h3 {
    color: var(--primary-green);
    margin-bottom: 10px;
}

.loading-card p {
    color: var(--gray);
}

/* Results Section */
.results-section {
    margin-bottom: 30px;
}

.results-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
}

.result-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--light-gray);
}

.result-header h2 {
    color: var(--success);
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.plant-info {
    margin-bottom: 30px;
}

.plant-main-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.plant-name {
    color: var(--primary-green);
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.confidence-badge {
    background: var(--success);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.plant-details {
    display: grid;
    gap: 20px;
}

.detail-item {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 15px;
    align-items: start;
    padding: 15px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
}

.detail-item label {
    font-weight: 600;
    color: var(--primary-green);
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-item span {
    color: var(--dark-gray);
}

.result-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.new-detection-btn {
    background: var(--secondary-green);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.new-detection-btn:hover {
    background: var(--primary-green);
    transform: translateY(-2px);
}

.download-btn {
    background: var(--info);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.download-btn:hover {
    background: #138496;
    transform: translateY(-2px);
}

/* Error Section */
.error-section {
    margin-bottom: 30px;
}

.error-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    box-shadow: var(--box-shadow);
    border-left: 5px solid var(--danger);
}

.error-icon {
    font-size: 3rem;
    color: var(--danger);
    margin-bottom: 20px;
}

.error-card h3 {
    color: var(--danger);
    margin-bottom: 15px;
}

.error-card p {
    color: var(--gray);
    margin-bottom: 25px;
}

.retry-btn {
    background: var(--danger);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.retry-btn:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* Statistics Section */
.stats-section {
    margin-bottom: 30px;
}

.stats-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
}

.stats-card h3 {
    color: var(--primary-green);
    margin-bottom: 25px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-green);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--gray);
    font-size: 0.9rem;
}

.status-online {
    color: var(--success);
}

.status-offline {
    color: var(--danger);
}

/* Footer */
.footer {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    box-shadow: var(--box-shadow);
    margin-top: 40px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.footer-content p {
    color: var(--gray);
    margin: 0;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--secondary-green);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
}

.footer-links a:hover {
    color: var(--primary-green);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--white);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: var(--secondary-green);
    color: var(--white);
    padding: 20px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: var(--white);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 30px;
    line-height: 1.6;
}

.modal-body ul, .modal-body ol {
    margin: 15px 0;
    padding-left: 20px;
}

.modal-body li {
    margin: 8px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .plant-main-info {
        flex-direction: column;
        text-align: center;
    }
    
    .detail-item {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .image-actions,
    .result-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .upload-area {
        padding: 20px;
    }
    
    .upload-content h3 {
        font-size: 1.2rem;
    }
    
    .plant-name {
        font-size: 1.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
